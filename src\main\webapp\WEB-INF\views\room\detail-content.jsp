<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid py-4">
    <!-- 返回按钮 -->
    <div class="mb-3">
        <a href="${pageContext.request.contextPath}/floor/layout?floor=${room.floorNumber}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left"></i> 返回楼层布局
        </a>
    </div>
    
    <!-- 教室基本信息 -->
    <div class="row">
        <div class="col-md-4">
            <div class="card room-info-card">
                <div class="card-body">
                    <h5 class="card-title">${room.roomNumber} - ${room.roomType}</h5>
                    <span class="badge ${room.status == '使用中' ? 'bg-danger' : room.status == '维护中' ? 'bg-warning' : 'bg-success'} room-status-badge">
                        ${room.status}
                    </span>
                    <hr>
                    <div class="row mt-3">
                        <div class="col-6">
                            <p><strong>楼层：</strong> ${room.floorNumber}楼</p>
                            <p><strong>面积：</strong> ${room.area} m²</p>
                            <p><strong>容量：</strong> ${room.capacity} 人</p>
                        </div>
                        <div class="col-6">
                            <p><strong>形状：</strong> ${room.shape}</p>
                            <p><strong>宽度：</strong> ${room.width} cm</p>
                            <p><strong>长度：</strong> ${room.length} cm</p>
                        </div>
                    </div>
                    <p><strong>描述：</strong> ${room.description}</p>
                </div>
            </div>
        </div>
        
        <!-- 教室设备统计 -->
        <div class="col-md-8">
            <div class="card room-info-card">
                <div class="card-body">
                    <h5 class="card-title">设备统计</h5>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h3>${room.devices.size()}</h3>
                                    <p class="mb-0">总设备数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3>${room.devices.stream().filter(d -> d.getStatus() == '空闲').count()}</h3>
                                    <p class="mb-0">正常设备</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning">
                                <div class="card-body text-center">
                                    <h3>${room.devices.stream().filter(d -> d.getStatus() == '使用中').count()}</h3>
                                    <p class="mb-0">使用中</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3>${room.devices.stream().filter(d -> d.getStatus() == '故障').count()}</h3>
                                    <p class="mb-0">故障设备</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 教室布局 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">教室布局</h5>
                </div>
                <div class="card-body">
                    <div class="room-layout">
                        <!-- 如果有布局图片，显示图片 -->
                        <c:if test="${room.layoutImageUrl != null}">
                            <img src="${room.layoutImageUrl}" alt="教室布局图" class="img-fluid" style="max-height: 500px;">
                        </c:if>
                        
                        <!-- 如果没有布局图片，显示默认布局 -->
                        <c:if test="${room.layoutImageUrl == null}">
                            <!-- 默认布局背景 -->
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: #f8f9fa; border: 1px solid #ddd;">
                                <!-- 教室轮廓 -->
                                <div style="position: absolute; top: 50px; left: 50px; width: calc(100% - 100px); height: calc(100% - 100px); border: 2px solid #343a40; background-color: #fff;">
                                    <!-- 门 -->
                                    <div style="position: absolute; top: 0; left: 50%; width: 80px; height: 10px; background-color: #6c757d; transform: translateX(-50%);">
                                        <div style="text-align: center; color: #fff; font-size: 10px;">门</div>
                                    </div>
                                    
                                    <!-- 讲台 -->
                                    <div style="position: absolute; top: 50px; left: 50%; width: 150px; height: 40px; background-color: #6c757d; transform: translateX(-50%);">
                                        <div style="text-align: center; color: #fff; padding-top: 10px;">讲台</div>
                                    </div>
                                    
                                    <!-- 设备位置 -->
                                    <c:forEach items="${room.devices}" var="device" varStatus="status">
                                        <div class="device-item ${device.status == '故障' ? 'fault' : device.status == '维护中' ? 'maintenance' : ''}" 
                                            style="left: ${50 + (status.index % 5) * 100}px; top: ${150 + Math.floor(status.index / 5) * 100}px;">
                                            <c:choose>
                                                <c:when test="${device.type == '照明'}">
                                                    <i class="bi bi-lightbulb device-icon"></i>
                                                </c:when>
                                                <c:when test="${device.type == '空调'}">
                                                    <i class="bi bi-thermometer-half device-icon"></i>
                                                </c:when>
                                                <c:when test="${device.type == '监控'}">
                                                    <i class="bi bi-camera-video device-icon"></i>
                                                </c:when>
                                                <c:when test="${device.type == '投影仪'}">
                                                    <i class="bi bi-projector device-icon"></i>
                                                </c:when>
                                                <c:when test="${device.type == '电脑'}">
                                                    <i class="bi bi-pc-display device-icon"></i>
                                                </c:when>
                                                <c:otherwise>
                                                    <i class="bi bi-gear device-icon"></i>
                                                </c:otherwise>
                                            </c:choose>
                                            
                                            <!-- 设备提示框 -->
                                            <div class="device-tooltip">
                                                <h6>${device.name}</h6>
                                                <p><strong>类型：</strong> ${device.type}</p>
                                                <p><strong>状态：</strong> ${device.status}</p>
                                                <p><strong>型号：</strong> ${device.model}</p>
                                                <div class="mt-2">
                                                    <button class="btn btn-sm btn-primary device-control-btn" data-device-id="${device.id}" data-action="on">开启</button>
                                                    <button class="btn btn-sm btn-danger device-control-btn" data-device-id="${device.id}" data-action="off">关闭</button>
                                                </div>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </div>
                        </c:if>
                    </div>
                    
                    <!-- 图例 -->
                    <div class="mt-3">
                        <div class="d-flex align-items-center mb-2">
                            <div style="width: 20px; height: 20px; background-color: rgba(0, 123, 255, 0.1); border: 2px solid #007bff; border-radius: 50%; margin-right: 10px;"></div>
                            <span>正常设备</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div style="width: 20px; height: 20px; background-color: rgba(220, 53, 69, 0.1); border: 2px solid #dc3545; border-radius: 50%; margin-right: 10px;"></div>
                            <span>故障设备</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <div style="width: 20px; height: 20px; background-color: rgba(255, 193, 7, 0.1); border: 2px solid #ffc107; border-radius: 50%; margin-right: 10px;"></div>
                            <span>维护中设备</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 设备列表 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">设备列表</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>设备名称</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>型号</th>
                                    <th>最后维护日期</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach items="${room.devices}" var="device">
                                    <tr>
                                        <td>${device.name}</td>
                                        <td>${device.type}</td>
                                        <td>
                                            <span class="badge ${device.status == '故障' ? 'bg-danger' : device.status == '维护中' ? 'bg-warning' : 'bg-success'}">
                                                ${device.status}
                                            </span>
                                        </td>
                                        <td>${device.model}</td>
                                        <td>${device.lastMaintenanceDate}</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary device-control-btn" data-device-id="${device.id}" data-action="on">开启</button>
                                            <button class="btn btn-sm btn-danger device-control-btn" data-device-id="${device.id}" data-action="off">关闭</button>
                                            <a href="${pageContext.request.contextPath}/device/detail?id=${device.id}" class="btn btn-sm btn-info">
                                                <i class="bi bi-eye"></i> 详情
                                            </a>
                                        </td>
                                    </tr>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
