package com.building.servlet;

import com.building.dao.DeviceDao;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@WebServlet("/device/stats")
public class DeviceStatsServlet extends HttpServlet {
    private DeviceDao deviceDao;
    private ObjectMapper objectMapper;

    @Override
    public void init() throws ServletException {
        deviceDao = new DeviceDao();
        objectMapper = new ObjectMapper();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        Map<String, Integer> stats = deviceDao.getDeviceStats();
        
        response.setContentType("application/json;charset=UTF-8");
        objectMapper.writeValue(response.getWriter(), stats);
    }
} 