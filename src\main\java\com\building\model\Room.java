package com.building.model;

import java.util.List;

/**
 * 房间实体类
 * 对应数据库中的room表
 */
public class Room {
    private Integer id;          // 房间ID
    private String roomNumber;   // 房间编号
    private Integer floorNumber; // 楼层号
    private String roomType;     // 房间类型
    private Double area;         // 面积
    private String status;       // 状态（空闲、使用中、维护中）
    private String description;  // 描述
    private String createTime;   // 创建时间
    private String updateTime;   // 更新时间

    // 布局相关属性
    private Integer capacity;    // 容纳人数
    private String shape;        // 教室形状（矩形、L形等）
    private Integer width;       // 宽度（厘米）
    private Integer length;      // 长度（厘米）
    private String position;     // 在楼层中的位置（坐标，JSON格式）
    private String layoutImageUrl; // 布局图片URL

    // 非持久化字段
    private List<Device> devices;  // 教室内的设备列表
    private RoomLayout layout;     // 教室布局
    private Integer deviceCount;   // 设备数量
    private Integer onlineDeviceCount; // 在线设备数量
    private Boolean hasCameraDevice;   // 是否有摄像头设备
    private Boolean cameraOnline;      // 摄像头是否在线
    private Integer currentPersonCount; // 当前人数

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRoomNumber() {
        return roomNumber;
    }

    public void setRoomNumber(String roomNumber) {
        this.roomNumber = roomNumber;
    }

    public Integer getFloorNumber() {
        return floorNumber;
    }

    public void setFloorNumber(Integer floorNumber) {
        this.floorNumber = floorNumber;
    }

    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getCapacity() {
        return capacity;
    }

    public void setCapacity(Integer capacity) {
        this.capacity = capacity;
    }

    public String getShape() {
        return shape;
    }

    public void setShape(String shape) {
        this.shape = shape;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getLayoutImageUrl() {
        return layoutImageUrl;
    }

    public void setLayoutImageUrl(String layoutImageUrl) {
        this.layoutImageUrl = layoutImageUrl;
    }

    public List<Device> getDevices() {
        return devices;
    }

    public void setDevices(List<Device> devices) {
        this.devices = devices;
    }

    public RoomLayout getLayout() {
        return layout;
    }

    public void setLayout(RoomLayout layout) {
        this.layout = layout;
    }

    public Integer getDeviceCount() {
        return deviceCount;
    }

    public void setDeviceCount(Integer deviceCount) {
        this.deviceCount = deviceCount;
    }

    public Integer getOnlineDeviceCount() {
        return onlineDeviceCount;
    }

    public void setOnlineDeviceCount(Integer onlineDeviceCount) {
        this.onlineDeviceCount = onlineDeviceCount;
    }

    public Boolean getHasCameraDevice() {
        return hasCameraDevice;
    }

    public void setHasCameraDevice(Boolean hasCameraDevice) {
        this.hasCameraDevice = hasCameraDevice;
    }

    public Boolean getCameraOnline() {
        return cameraOnline;
    }

    public void setCameraOnline(Boolean cameraOnline) {
        this.cameraOnline = cameraOnline;
    }

    public Integer getCurrentPersonCount() {
        return currentPersonCount;
    }

    public void setCurrentPersonCount(Integer currentPersonCount) {
        this.currentPersonCount = currentPersonCount;
    }
}