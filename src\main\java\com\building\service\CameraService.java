package com.building.service;

import java.util.List;
import java.util.Map;

import com.building.model.Camera;
import com.building.model.PersonRecord;

/**
 * 摄像头服务接口
 * 定义摄像头相关的业务逻辑
 */
public interface CameraService {
    
    /**
     * 获取所有摄像头
     * @return 摄像头列表
     */
    List<Camera> getAllCameras();
    
    /**
     * 根据ID获取摄像头
     * @param id 摄像头ID
     * @return 摄像头对象
     */
    Camera getCameraById(int id);
    
    /**
     * 根据房间ID获取摄像头列表
     * @param roomId 房间ID
     * @return 摄像头列表
     */
    List<Camera> getCamerasByRoomId(int roomId);
    
    /**
     * 添加摄像头
     * @param camera 摄像头对象
     * @return 是否添加成功
     */
    boolean addCamera(Camera camera);
    
    /**
     * 更新摄像头
     * @param camera 摄像头对象
     * @return 是否更新成功
     */
    boolean updateCamera(Camera camera);
    
    /**
     * 删除摄像头
     * @param id 摄像头ID
     * @return 是否删除成功
     */
    boolean deleteCamera(int id);
    
    /**
     * 更新摄像头状态
     * @param id 摄像头ID
     * @param status 状态：0-离线 1-在线
     * @return 是否更新成功
     */
    boolean updateCameraStatus(int id, int status);
    
    /**
     * 获取摄像头统计信息
     * @return 统计信息Map
     */
    Map<String, Integer> getCameraStats();
    
    /**
     * 添加人员记录
     * @param record 人员记录对象
     * @return 是否添加成功
     */
    boolean addPersonRecord(PersonRecord record);
    
    /**
     * 获取指定房间的最新人员记录
     * @param roomId 房间ID
     * @return 人员记录对象
     */
    PersonRecord getLatestPersonRecord(int roomId);
    
    /**
     * 获取指定房间的人员记录历史
     * @param roomId 房间ID
     * @param limit 限制记录数量
     * @return 人员记录列表
     */
    List<PersonRecord> getPersonRecordHistory(int roomId, int limit);
    
    /**
     * 获取指定时间范围内的人员记录
     * @param roomId 房间ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 人员记录列表
     */
    List<PersonRecord> getPersonRecordsByTimeRange(int roomId, String startTime, String endTime);
}
