-- 系统设置表创建脚本
-- 创建日期: 2024年6月12日
-- 描述: 用于存储系统各项设置，包括安全设置、邮件设置、备份设置和通知设置


-- 创建系统设置表
CREATE TABLE `system_settings` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- 安全设置
  `min_password_length` INT NOT NULL DEFAULT 8 COMMENT '最小密码长度',
  `require_uppercase` INT NOT NULL DEFAULT 1 COMMENT '是否要求大写字母',
  `require_numbers` INT NOT NULL DEFAULT 1 COMMENT '是否要求数字',
  `require_special_chars` INT NOT NULL DEFAULT 0 COMMENT '是否要求特殊字符',
  `max_login_attempts` INT NOT NULL DEFAULT 5 COMMENT '最大登录尝试次数',
  
  -- 邮件设置
  `smtp_server` VARCHAR(255) DEFAULT NULL COMMENT 'SMTP服务器地址',
  `smtp_port` INT DEFAULT 587 COMMENT 'SMTP服务器端口',
  `sender_email` VARCHAR(255) DEFAULT NULL COMMENT '发送者邮箱',
  `email_password` VARCHAR(255) DEFAULT NULL COMMENT '邮箱密码（应加密存储）',
  
  -- 备份设置
  `auto_backup` INT NOT NULL DEFAULT 0 COMMENT '是否自动备份',
  `backup_frequency` VARCHAR(20) DEFAULT 'daily' COMMENT '备份频率（daily/weekly/monthly）',
  `backup_retention` INT DEFAULT 30 COMMENT '备份保留天数',
  `backup_path` VARCHAR(255) DEFAULT NULL COMMENT '备份存储路径',
  `last_backup_time` DATETIME DEFAULT NULL COMMENT '上次备份时间',
  
  -- 通知设置
  `system_notifications` INT NOT NULL DEFAULT 1 COMMENT '是否启用系统通知',
  `email_notifications` INT NOT NULL DEFAULT 0 COMMENT '是否启用邮件通知',
  `notify_reservation` INT NOT NULL DEFAULT 1 COMMENT '是否通知预约活动',
  `notify_maintenance` INT NOT NULL DEFAULT 1 COMMENT '是否通知维护活动',
  `notify_system` INT NOT NULL DEFAULT 1 COMMENT '是否通知系统事件',
  
  -- 时间戳
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- 插入默认设置
INSERT INTO `system_settings` (
  `min_password_length`, `require_uppercase`, `require_numbers`, `require_special_chars`, `max_login_attempts`,
  `smtp_port`, `backup_frequency`, `backup_retention`,
  `system_notifications`, `notify_reservation`, `notify_maintenance`, `notify_system`
) VALUES (
  8, 1, 1, 0, 5,
  587, 'daily', 30,
  1, 1, 1, 1
); 