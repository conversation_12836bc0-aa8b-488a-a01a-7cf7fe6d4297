<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><c:out value="${param.title}"/> - 产教融合大楼数字化管理系统</title>
    <link href="${pageContext.request.contextPath}/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
    <style>
        html, body {
            height: 100%;
            margin: 0;
        }
        /* 导航栏高度 */
        body {
            padding-top: 90px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        /* 导航栏样式 */
        .navbar {
            height: 90px;
            z-index: 1030;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,.08);
        }
        /* 品牌logo样式 */
        .navbar-brand {
            padding-top: .75rem;
            padding-bottom: .75rem;
        }
        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 90px;
            bottom: 0;
            left: 0;
            z-index: 1000;
            padding: 0;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
            width: 240px;
            background: white;
        }
        /* 侧边栏粘性样式 */
        .sidebar-sticky {
            position: sticky;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: 1rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        /* 侧边栏链接样式 */
        .nav-link {
            padding: 0.8rem 1.5rem;
            color: #333;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        /* 侧边栏链接悬停样式 */
        .nav-link:hover {
            background-color: #e9ecef;
            color: #007bff;
        }
        /* 侧边栏链接激活样式 */
        .nav-link.active {
            color: #007bff;
            background-color: #e9ecef;
            border-right: 3px solid #007bff;
        }
        /* 侧边栏链接图标样式 */
        .nav-link i {
            margin-right: 0.8rem;
            width: 1.2rem;
            text-align: center;
        }
        /* 主要内容区域样式 */
        .content {
            margin-left: 240px;/*侧边栏宽度*/
            padding: 2rem;/*内边距*/
            flex: 1;/*弹性布局*/
            overflow-y: auto;/*垂直滚动*/
            overflow-x: hidden;/*水平滚动*/
            min-width: 0;/*最小宽度*/
            min-height: calc(100vh - 90px);/*最小高度*/
            background-color: #fff;/*背景色*/
        }
        /* 容器样式 */
        .container-fluid {
            height: calc(100% - 48px);/*高度*/
            padding: 0;/*内边距*/
        }
        /* 行样式 */
        .row {
            /* height: 20%; */
            /*第一行*/
            .row:first-child {
                height: 5%;/*高度*/
            }
            /*第二行*/
            .row:nth-child(2) {
                height: 15%;/*高度*/
            }
            /*第三行*/
            .row:nth-child(3) {
                height: 70%;/*高度*/
            }
            margin: 0;/*外边距*/
        }
        /* 控制面板卡片样式 */
        .dashboard-card {
            background: #fff;/*背景色*/
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }
        /* 控制面板卡片悬停样式 */
        .dashboard-card:hover {
            transform: translateY(-2px);
        }
        /* 控制面板统计卡片样式 */
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        /* 控制面板统计卡片样式 */
        .stat-card {
            padding: 1.5rem;
            border-radius: 8px;
            color: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        /* 控制面板统计卡片蓝色样式 */
        .stat-card.blue {
            background: linear-gradient(45deg, #4e73df, #224abe);
        }
        /* 控制面板统计卡片绿色样式 */
        .stat-card.green {
            background: linear-gradient(45deg, #1cc88a, #13855c);
        }

        /* 控制面板统计卡片黄色样式 */
        .stat-card.yellow {
            background: linear-gradient(45deg, #f6c23e, #dda20a);
        }
        /* 控制面板统计卡片数字样式 */
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }
        /* 控制面板统计卡片标签样式 */
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* 表格样式优化 */
        .table-responsive {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,.05);
            padding: 1rem;
        }
        /* 表格样式优化 */
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        /* 表格样式优化 */
        ${param.additionalStyles}
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <jsp:include page="/WEB-INF/views/common/header.jsp" />

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <jsp:include page="/WEB-INF/views/common/sidebar.jsp" />

            <!-- 主要内容区域 -->
            <main class="content">
                <jsp:include page="${param.content}" />
            </main>
        </div>
    </div>

    <script src="${pageContext.request.contextPath}/static/js/jquery.min.js"></script>
    <script src="${pageContext.request.contextPath}/static/js/bootstrap.bundle.min.js"></script>
    ${param.scripts}
</body>
</html>
