package com.building.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数据库备份DAO接口
 */
public interface BackupDao {
    
    /**
     * 记录备份日志
     * 
     * @param backupTime 备份时间
     * @param backupFile 备份文件名
     * @param backupPath 备份文件路径
     * @param backupSize 备份文件大小
     * @param backupType 备份类型
     * @param status 备份状态
     * @param errorMessage 错误信息
     * @param duration 备份耗时(秒)
     * @return 是否成功
     */
    boolean logBackup(Date backupTime, String backupFile, String backupPath, 
                    long backupSize, String backupType, String status, 
                    String errorMessage, int duration);
    
    /**
     * 获取备份日志列表
     * 
     * @param limit 限制数量
     * @return 备份日志列表
     */
    List<Map<String, Object>> getBackupLogs(int limit);
    
    /**
     * 清理过期备份日志
     * 
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    int cleanupBackupLogs(int retentionDays);
    
    /**
     * 获取备份调度任务列表
     * 
     * @return 调度任务列表
     */
    List<Map<String, Object>> getBackupSchedules();
    
    /**
     * 获取调度任务详情
     * 
     * @param scheduleId 调度任务ID
     * @return 调度任务详情
     */
    Map<String, Object> getBackupScheduleById(int scheduleId);
    
    /**
     * 更新调度任务
     * 
     * @param scheduleId 调度任务ID
     * @param active 是否激活
     * @param scheduleType 调度类型
     * @param dayOfWeek 星期几
     * @param dayOfMonth 每月第几天
     * @param hour 小时
     * @param minute 分钟
     * @param retentionDays 保留天数
     * @param backupPath 备份路径
     * @return 是否成功
     */
    boolean updateSchedule(int scheduleId, boolean active, String scheduleType,
                        Integer dayOfWeek, Integer dayOfMonth, int hour,
                        int minute, int retentionDays, String backupPath);
    
    /**
     * 添加调度任务
     * 
     * @param scheduleName 调度名称
     * @param active 是否激活
     * @param scheduleType 调度类型
     * @param dayOfWeek 星期几
     * @param dayOfMonth 每月第几天
     * @param hour 小时
     * @param minute 分钟
     * @param retentionDays 保留天数
     * @param backupPath 备份路径
     * @return 新任务ID
     */
    int addSchedule(String scheduleName, boolean active, String scheduleType,
                 Integer dayOfWeek, Integer dayOfMonth, int hour,
                 int minute, int retentionDays, String backupPath);
    
    /**
     * 删除调度任务
     * 
     * @param scheduleId 调度任务ID
     * @return 是否成功
     */
    boolean deleteSchedule(int scheduleId);
    
    /**
     * 更新调度任务的下次运行时间
     * 
     * @param scheduleId 调度任务ID
     * @param lastRunTime 上次运行时间
     * @param nextRunTime 下次运行时间
     * @return 是否成功
     */
    boolean updateScheduleRunTime(int scheduleId, Date lastRunTime, Date nextRunTime);
    
    /**
     * 获取到期的调度任务
     * 
     * @param currentTime 当前时间
     * @return 到期的调度任务列表
     */
    List<Map<String, Object>> getDueSchedules(Date currentTime);
} 