package com.building.servlet;

import java.io.IOException;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.dao.DeviceDao;
import com.building.dao.RoomDao;
import com.building.dao.RoomLayoutDao;
import com.building.model.Device;
import com.building.model.Room;
import com.building.model.RoomLayout;

/**
 * 教室详情Servlet
 * 用于处理教室详情页面的请求
 */
@WebServlet("/room/detail")
public class RoomDetailServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private RoomDao roomDao;
    private DeviceDao deviceDao;
    private RoomLayoutDao roomLayoutDao;
    
    @Override
    public void init() throws ServletException {
        roomDao = new RoomDao();
        deviceDao = new DeviceDao();
        roomLayoutDao = new RoomLayoutDao();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取教室ID
        String idParam = request.getParameter("id");
        if (idParam == null || idParam.isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/room/list");
            return;
        }
        
        try {
            int roomId = Integer.parseInt(idParam);
            
            // 获取教室信息
            Room room = roomDao.getRoomById(roomId);
            if (room == null) {
                response.sendRedirect(request.getContextPath() + "/room/list");
                return;
            }
            
            // 获取教室布局
            RoomLayout layout = roomLayoutDao.getRoomLayoutByRoomId(roomId);
            if (layout != null) {
                room.setLayout(layout);
            }
            
            // 获取教室内的设备
            List<Device> devices = deviceDao.getDevicesByRoomId(roomId);
            room.setDevices(devices);
            
            // 设置请求属性
            request.setAttribute("room", room);
            
            // 转发到教室详情页面
            request.getRequestDispatcher("/WEB-INF/views/room/detail.jsp").forward(request, response);
            
        } catch (NumberFormatException e) {
            response.sendRedirect(request.getContextPath() + "/room/list");
        }
    }
}
