<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="视频流" />
    <jsp:param name="content" value="/WEB-INF/views/camera/stream-content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 视频流页面样式 */
        .stream-container {
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            height: calc(100vh - 200px);
            min-height: 500px;
        }
        .stream-container img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        .stream-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .stream-controls {
            position: absolute;
            bottom: 20px;
            left: 0;
            width: 100%;
            padding: 0 20px;
            display: flex;
            justify-content: center;
            z-index: 10;
        }
        .stream-controls .btn {
            margin: 0 5px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
        }
        .stream-controls .btn:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        .stream-controls .btn-primary {
            background-color: rgba(13, 110, 253, 0.7);
        }
        .stream-controls .btn-primary:hover {
            background-color: rgba(13, 110, 253, 0.9);
        }
        .stream-info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 10;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 10px 15px;
            border-radius: 5px;
        }
        .camera-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .camera-online {
            background-color: #28a745;
        }
        .camera-offline {
            background-color: #dc3545;
        }
    " />
    <jsp:param name="scripts" value="
        <script>
            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                // 获取视频流URL
                const cameraId = '${camera.id}';
                
                // 如果摄像头在线，加载视频流
                if (${camera.status} === 1) {
                    loadVideoStream(cameraId);
                }
                
                // 控制按钮点击事件
                document.querySelectorAll('.control-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const action = this.getAttribute('data-action');
                        controlCamera(cameraId, action);
                    });
                });
                
                // 连接/断开摄像头按钮点击事件
                const connectBtn = document.getElementById('connectBtn');
                if (connectBtn) {
                    connectBtn.addEventListener('click', function() {
                        const action = this.getAttribute('data-action');
                        
                        fetch('${pageContext.request.contextPath}/camera/control', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'cameraId=' + cameraId + '&action=' + action
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert(data.message);
                                // 刷新页面
                                window.location.reload();
                            } else {
                                alert('操作失败: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('操作失败，请稍后重试');
                        });
                    });
                }
                
                // 全屏按钮点击事件
                const fullscreenBtn = document.getElementById('fullscreenBtn');
                if (fullscreenBtn) {
                    fullscreenBtn.addEventListener('click', function() {
                        const streamContainer = document.querySelector('.stream-container');
                        if (streamContainer) {
                            if (streamContainer.requestFullscreen) {
                                streamContainer.requestFullscreen();
                            } else if (streamContainer.webkitRequestFullscreen) { /* Safari */
                                streamContainer.webkitRequestFullscreen();
                            } else if (streamContainer.msRequestFullscreen) { /* IE11 */
                                streamContainer.msRequestFullscreen();
                            }
                        }
                    });
                }
            });
            
            // 加载视频流
            function loadVideoStream(cameraId) {
                // 获取视频流URL
                fetch('${pageContext.request.contextPath}/camera/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=getStreamUrl'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('RTSP URL:', data.rtspUrl);
                        // 这里应该使用WebRTC或其他技术来显示RTSP流
                        // 由于浏览器限制，直接显示RTSP流需要特殊处理
                        // 这里仅显示模拟视频
                        document.getElementById('streamImage').src = '${pageContext.request.contextPath}/static/images/camera-stream.jpg';
                    } else {
                        console.error('获取视频流失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
            
            // 控制摄像头
            function controlCamera(cameraId, action) {
                fetch('${pageContext.request.contextPath}/camera/control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=' + action
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('控制成功:', data.message);
                    } else {
                        console.error('控制失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
        </script>
    " />
</jsp:include>
