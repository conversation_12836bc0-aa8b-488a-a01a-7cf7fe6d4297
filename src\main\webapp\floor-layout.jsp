<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产教融合大楼一楼布局图</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .layout-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 20px auto;
            max-width: 1400px;
        }
        
        .layout-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        
        .layout-header h1 {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .layout-header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .floor-plan-container {
            position: relative;
            display: flex;
            justify-content: center;
            margin: 30px 0;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
        }
        
        /* 楼层平面图主容器 */
        .floor-plan {
            position: relative;
            width: 1000px;
            height: 700px;
            background: #ffffff;
            border: 3px solid #333;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        /* 房间基础样式 */
        .room {
            position: absolute;
            border: 2px solid #333;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            text-align: center;
            line-height: 1.2;
        }
        
        .room:hover {
            background: rgba(102, 126, 234, 0.3);
            border-color: #667eea;
            transform: scale(1.02);
            z-index: 10;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        /* 不同房间类型的样式 */
        .room-研发中心 {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
            border-color: #e65100;
            color: #e65100;
        }
        
        .room-教室 {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-color: #1976d2;
            color: #1976d2;
        }
        
        .room-实验室 {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            border-color: #7b1fa2;
            color: #7b1fa2;
        }
        
        .room-办公室 {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-color: #388e3c;
            color: #388e3c;
        }
        
        .room-会议室 {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
            border-color: #f57c00;
            color: #f57c00;
        }
        
        .room-多媒体教室 {
            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
            border-color: #c2185b;
            color: #c2185b;
        }
        
        .room-储藏室 {
            background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
            border-color: #689f38;
            color: #689f38;
        }
        
        .room-公共区域 {
            background: linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%);
            border-color: #00796b;
            color: #00796b;
        }
        
        .room-公共设施 {
            background: linear-gradient(135deg, #f9fbe7 0%, #f0f4c3 100%);
            border-color: #827717;
            color: #827717;
        }
        
        .room-通道 {
            background: linear-gradient(135deg, #efebe9 0%, #d7ccc8 100%);
            border-color: #5d4037;
            color: #5d4037;
        }
        
        /* 走廊和通道样式 */
        .corridor {
            background: linear-gradient(135deg, #fafafa 0%, #eeeeee 100%);
            border: 1px solid #bdbdbd;
        }
        
        /* 墙体样式 */
        .wall {
            background: #333;
            border: none;
            pointer-events: none;
        }
        
        /* 门的样式 */
        .door {
            background: #8d6e63;
            border: 1px solid #5d4037;
            pointer-events: none;
        }
        
        /* 楼梯样式 */
        .stairs {
            background: repeating-linear-gradient(
                45deg,
                #e0e0e0,
                #e0e0e0 5px,
                #bdbdbd 5px,
                #bdbdbd 10px
            );
            border: 2px solid #757575;
        }
        
        /* 电梯样式 */
        .elevator {
            background: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%);
            border: 2px solid #0277bd;
            color: #0277bd;
        }
        
        .room-info-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            border-left: 5px solid #667eea;
        }
        
        .room-info-panel h4 {
            color: #667eea;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .room-details {
            display: none;
        }
        
        .room-details.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .room-type-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            margin-right: 10px;
        }
        
        .room-type-教室 { background: #e3f2fd; color: #1976d2; }
        .room-type-实验室 { background: #f3e5f5; color: #7b1fa2; }
        .room-type-办公室 { background: #e8f5e8; color: #388e3c; }
        .room-type-会议室 { background: #fff3e0; color: #f57c00; }
        .room-type-多媒体教室 { background: #fce4ec; color: #c2185b; }
        .room-type-储藏室 { background: #f1f8e9; color: #689f38; }
        .room-type-公共区域 { background: #e0f2f1; color: #00796b; }
        .room-type-公共设施 { background: #f9fbe7; color: #827717; }
        .room-type-通道 { background: #efebe9; color: #5d4037; }
        .room-type-研发中心 { background: #fff8e1; color: #e65100; }
        
        .legend {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .legend h5 {
            color: #333;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .legend-item {
            display: inline-block;
            margin: 5px 10px 5px 0;
        }
        
        .back-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        @media (max-width: 1200px) {
            .floor-plan {
                width: 800px;
                height: 560px;
                transform: scale(0.8);
                transform-origin: center;
            }
        }
        
        @media (max-width: 768px) {
            .layout-container {
                margin: 10px;
                padding: 20px;
            }
            
            .floor-plan {
                width: 600px;
                height: 420px;
                transform: scale(0.6);
                transform-origin: center;
            }
            
            .room {
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="layout-container">
            <!-- 页面头部 -->
            <div class="layout-header">
                <h1><i class="fas fa-building"></i> 产教融合大楼一楼布局图</h1>
                <p>点击房间查看详细信息</p>
            </div>
            
            <!-- 返回按钮 -->
            <div class="mb-3">
                <a href="${pageContext.request.contextPath}/main.jsp" class="back-btn">
                    <i class="fas fa-arrow-left"></i> 返回主页
                </a>
            </div>
            
            <!-- 楼层平面图 -->
            <div class="floor-plan-container">
                <div class="floor-plan" id="floorPlan">
                    <!-- 外墙 -->
                    <div class="wall" style="top: 0; left: 0; width: 100%; height: 6px;"></div>
                    <div class="wall" style="bottom: 0; left: 0; width: 100%; height: 6px;"></div>
                    <div class="wall" style="top: 0; left: 0; width: 6px; height: 100%;"></div>
                    <div class="wall" style="top: 0; right: 0; width: 6px; height: 100%;"></div>
                    
                    <!-- 研发中心区域 - 分成两排 -->
                    <div class="room room-研发中心" 
                         style="top: 50px; left: 50px; width: 180px; height: 80px;"
                         data-room='{"roomNumber": "研发中心1", "roomType": "研发中心", "description": "产教融合研发基地"}'>
                        研发中心1<br>
                        <small>产教融合研发</small>
                    </div>
                    
                    <div class="room room-研发中心" 
                         style="top: 150px; left: 50px; width: 180px; height: 80px;"
                         data-room='{"roomNumber": "研发中心2", "roomType": "研发中心", "description": "创新技术研发"}'>
                        研发中心2<br>
                        <small>创新技术研发</small>
                    </div>
                    
                    <!-- 中间教室区域 - 填充101/102左侧空白 -->
                    <div class="room room-教室" 
                         style="top: 50px; left: 250px; width: 190px; height: 80px;"
                         data-room='{"roomNumber": "103", "roomType": "教室", "description": "智能教室"}'>
                        103<br>
                        <small>教室</small>
                    </div>
                    
                    <div class="room room-教室" 
                         style="top: 150px; left: 250px; width: 190px; height: 80px;"
                         data-room='{"roomNumber": "104", "roomType": "教室", "description": "实训教室"}'>
                        104<br>
                        <small>教室</small>
                    </div>
                    
                    <!-- 左侧房间区域 -->
                    <div class="room room-教室" 
                         style="top: 50px; left: 460px; width: 140px; height: 80px;"
                         data-room='{"roomNumber": "101", "roomType": "教室", "description": "多媒体教室"}'>
                        101<br>
                        <small>教室</small>
                    </div>
                    
                    <div class="room room-教室" 
                         style="top: 150px; left: 460px; width: 140px; height: 80px;"
                         data-room='{"roomNumber": "102", "roomType": "教室", "description": "理论教学教室"}'>
                        102<br>
                        <small>教室</small>
                    </div>
                    
                    <!-- 实验室区域 - 分成两排 -->
                    <div class="room room-实验室" 
                         style="top: 50px; left: 620px; width: 120px; height: 80px;"
                         data-room='{"roomNumber": "实验室1", "roomType": "实验室", "description": "综合实验室"}'>
                        实验室1<br>
                        <small>综合实验室</small>
                    </div>
                    
                    <div class="room room-实验室" 
                         style="top: 150px; left: 620px; width: 120px; height: 80px;"
                         data-room='{"roomNumber": "实验室2", "roomType": "实验室", "description": "专业实验室"}'>
                        实验室2<br>
                        <small>专业实验室</small>
                    </div>
                    
                    <!-- 中央走廊 -->
                    <div class="corridor" style="top: 250px; left: 50px; width: 900px; height: 30px;"></div>
                    
                    <!-- 下方房间区域 -->
                    <!-- 办公室1区域 - 分成上下两排 -->
                    <div class="room room-办公室" 
                         style="top: 460px; left: 50px; width: 150px; height: 80px;"
                         data-room='{"roomNumber": "办公室1-1", "roomType": "办公室", "description": "教师办公室"}'>
                        办公室1-1<br>
                        <small>教师办公</small>
                    </div>
                    
                    <div class="room room-办公室" 
                         style="top: 560px; left: 50px; width: 150px; height: 80px;"
                         data-room='{"roomNumber": "办公室1-2", "roomType": "办公室", "description": "教师办公室"}'>
                        办公室1-2<br>
                        <small>教师办公</small>
                    </div>
                    
                    <!-- 办公室2区域 - 分成上下两排 -->
                    <div class="room room-办公室" 
                         style="top: 460px; left: 220px; width: 150px; height: 80px;"
                         data-room='{"roomNumber": "办公室2-1", "roomType": "办公室", "description": "行政办公室"}'>
                        办公室2-1<br>
                        <small>行政办公</small>
                    </div>
                    
                    <div class="room room-办公室" 
                         style="top: 560px; left: 220px; width: 150px; height: 80px;"
                         data-room='{"roomNumber": "办公室2-2", "roomType": "办公室", "description": "行政办公室"}'>
                        办公室2-2<br>
                        <small>行政办公</small>
                    </div>
                    
                    <!-- 会议室区域 - 分成上下两排 -->
                    <div class="room room-会议室" 
                         style="top: 460px; left: 390px; width: 180px; height: 80px;"
                         data-room='{"roomNumber": "会议室1", "roomType": "会议室", "description": "大型会议室"}'>
                        会议室1<br>
                        <small>大型会议</small>
                    </div>
                    
                    <div class="room room-会议室" 
                         style="top: 560px; left: 390px; width: 180px; height: 80px;"
                         data-room='{"roomNumber": "会议室2", "roomType": "会议室", "description": "中型会议室"}'>
                        会议室2<br>
                        <small>中型会议</small>
                    </div>
                    
                    <!-- 多媒体室区域 - 分成上下两排 -->
                    <div class="room room-多媒体教室" 
                         style="top: 460px; left: 590px; width: 150px; height: 80px;"
                         data-room='{"roomNumber": "多媒体室1", "roomType": "多媒体教室", "description": "演示教室"}'>
                        多媒体室1<br>
                        <small>演示教学</small>
                    </div>
                    
                    <div class="room room-多媒体教室" 
                         style="top: 560px; left: 590px; width: 150px; height: 80px;"
                         data-room='{"roomNumber": "多媒体室2", "roomType": "多媒体教室", "description": "互动教室"}'>
                        多媒体室2<br>
                        <small>互动教学</small>
                    </div>
                    
                    <!-- 右侧功能区域 -->
                    <div class="room room-公共设施" 
                         style="top: 50px; left: 760px; width: 190px; height: 80px;"
                         data-room='{"roomNumber": "男卫生间", "roomType": "公共设施", "description": "男卫生间"}'>
                        男卫生间
                    </div>
                    
                    <div class="room room-公共设施" 
                         style="top: 150px; left: 760px; width: 190px; height: 80px;"
                         data-room='{"roomNumber": "女卫生间", "roomType": "公共设施", "description": "女卫生间"}'>
                        女卫生间
                    </div>
                    
                    <div class="room stairs room-通道" 
                         style="top: 250px; left: 760px; width: 190px; height: 190px;"
                         data-room='{"roomNumber": "楼梯间1", "roomType": "通道", "description": "主楼梯"}'>
                        楼梯间1<br>
                        <small>主楼梯</small>
                    </div>
                    
                    <div class="room elevator" 
                         style="top: 460px; left: 760px; width: 190px; height: 80px;"
                         data-room='{"roomNumber": "电梯", "roomType": "通道", "description": "客用电梯"}'>
                        电梯<br>
                        <small>客用</small>
                    </div>
                    
                    <div class="room room-储藏室" 
                         style="top: 560px; left: 760px; width: 190px; height: 80px;"
                         data-room='{"roomNumber": "储藏室", "roomType": "储藏室", "description": "设备存放"}'>
                        储藏室<br>
                        <small>设备存放</small>
                    </div>
                    
                    <!-- 内部分隔墙 -->
                    <div class="wall" style="top: 250px; left: 450px; width: 4px; height: 200px;"></div>
                    <div class="wall" style="top: 250px; left: 750px; width: 4px; height: 200px;"></div>
                    <div class="wall" style="top: 430px; left: 50px; width: 900px; height: 4px;"></div>
                </div>
            </div>
            
            <!-- 房间信息面板 -->
            <div class="room-info-panel">
                <h4><i class="fas fa-info-circle"></i> 房间信息</h4>
                <div id="defaultInfo">
                    <p class="text-muted">请点击平面图中的房间查看详细信息</p>
                </div>
                <div id="roomDetails" class="room-details">
                    <!-- 房间详细信息将通过JavaScript动态填充 -->
                </div>
            </div>
            
            <!-- 图例 -->
            <div class="legend">
                <h5><i class="fas fa-palette"></i> 房间类型图例</h5>
                <div id="legendItems">
                    <div class="legend-item">
                        <span class="room-type-badge room-type-研发中心">研发中心</span>
                    </div>
                    <div class="legend-item">
                        <span class="room-type-badge room-type-教室">教室</span>
                    </div>
                    <div class="legend-item">
                        <span class="room-type-badge room-type-实验室">实验室</span>
                    </div>
                    <div class="legend-item">
                        <span class="room-type-badge room-type-办公室">办公室</span>
                    </div>
                    <div class="legend-item">
                        <span class="room-type-badge room-type-会议室">会议室</span>
                    </div>
                    <div class="legend-item">
                        <span class="room-type-badge room-type-多媒体教室">多媒体教室</span>
                    </div>
                    <div class="legend-item">
                        <span class="room-type-badge room-type-储藏室">储藏室</span>
                    </div>
                    <div class="legend-item">
                        <span class="room-type-badge room-type-公共设施">公共设施</span>
                    </div>
                    <div class="legend-item">
                        <span class="room-type-badge room-type-通道">通道</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupRoomInteractions();
        });
        
        // 设置房间交互
        function setupRoomInteractions() {
            const rooms = document.querySelectorAll('.room[data-room]');
            
            rooms.forEach(room => {
                room.addEventListener('click', function() {
                    const roomData = JSON.parse(this.dataset.room);
                    showRoomDetails(roomData);
                });
            });
        }
        
        // 显示房间详细信息
        function showRoomDetails(room) {
            const defaultInfo = document.getElementById('defaultInfo');
            const roomDetails = document.getElementById('roomDetails');
            
            defaultInfo.style.display = 'none';
            roomDetails.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fas fa-door-open"></i> ${room.roomNumber}</h5>
                        <p><strong>房间类型:</strong> 
                            <span class="room-type-badge room-type-${room.roomType}">${room.roomType}</span>
                        </p>
                        <p><strong>描述:</strong> ${room.description}</p>
                        <p><strong>状态:</strong> 
                            <span class="badge bg-success">可用</span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-info-circle"></i> 功能信息</h6>
                        <p><small class="text-muted">
                            该房间为${room.roomType}，主要用于${room.description}。<br>
                            点击房间可查看详细信息和使用状态。
                        </small></p>
                    </div>
                </div>
            `;
            roomDetails.classList.add('active');
        }
    </script>
</body>
</html> 