package com.building.service;

import java.util.List;
import com.building.model.Device;

/**
 * 设备服务接口
 */
public interface DeviceService {
    
    /**
     * 获取所有设备列表
     * @return 设备列表
     */
    List<Device> getAllDevices();
    
    /**
     * 根据ID获取设备信息
     * @param id 设备ID
     * @return 设备对象，如果不存在返回null
     */
    Device getDeviceById(int id);
    
    /**
     * 获取设备统计信息
     * @return 包含总数、使用中和空闲数量的数组
     */
    int[] getDeviceStatistics();
    
    /**
     * 更新设备状态
     * @param id 设备ID
     * @param status 新状态
     * @return 更新是否成功
     */
    boolean updateDeviceStatus(int id, String status);
} 