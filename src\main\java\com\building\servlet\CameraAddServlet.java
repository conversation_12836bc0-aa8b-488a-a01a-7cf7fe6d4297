package com.building.servlet;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.building.model.Camera;
import com.building.service.CameraService;
import com.building.service.impl.CameraServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 摄像头添加Servlet
 * 用于处理摄像头添加的请求
 */
@WebServlet("/camera/add")
public class CameraAddServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private CameraService cameraService;
    private ObjectMapper objectMapper;
    
    @Override
    public void init() throws ServletException {
        cameraService = new CameraServiceImpl();
        objectMapper = new ObjectMapper();
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 设置响应类型
        response.setContentType("application/json;charset=UTF-8");
        
        // 创建结果Map
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取请求参数
            String name = request.getParameter("name");
            String ipAddress = request.getParameter("ipAddress");
            String portStr = request.getParameter("port");
            String username = request.getParameter("username");
            String password = request.getParameter("password");
            String rtspUrl = request.getParameter("rtspUrl");
            String location = request.getParameter("location");
            String brand = request.getParameter("brand");
            String model = request.getParameter("model");
            String roomIdStr = request.getParameter("roomId");
            
            // 参数验证
            if (name == null || name.trim().isEmpty() || 
                ipAddress == null || ipAddress.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "摄像头名称和IP地址不能为空");
                objectMapper.writeValue(response.getWriter(), result);
                return;
            }
            
            // 创建摄像头对象
            Camera camera = new Camera();
            camera.setName(name);
            camera.setIpAddress(ipAddress);
            
            // 设置可选参数
            if (portStr != null && !portStr.trim().isEmpty()) {
                try {
                    int port = Integer.parseInt(portStr);
                    camera.setPort(port);
                } catch (NumberFormatException e) {
                    camera.setPort(554); // 默认RTSP端口
                }
            } else {
                camera.setPort(554); // 默认RTSP端口
            }
            
            camera.setUsername(username);
            camera.setPassword(password);
            camera.setRtspUrl(rtspUrl);
            camera.setLocation(location);
            camera.setBrand(brand);
            camera.setModel(model);
            camera.setStatus(0); // 默认离线
            
            if (roomIdStr != null && !roomIdStr.trim().isEmpty()) {
                try {
                    int roomId = Integer.parseInt(roomIdStr);
                    camera.setRoomId(roomId);
                } catch (NumberFormatException e) {
                    // 忽略无效的房间ID
                }
            }
            
            // 添加摄像头
            boolean success = cameraService.addCamera(camera);
            
            if (success) {
                result.put("success", true);
                result.put("message", "摄像头添加成功");
            } else {
                result.put("success", false);
                result.put("message", "摄像头添加失败");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "处理请求时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 返回JSON结果
        objectMapper.writeValue(response.getWriter(), result);
    }
}
