<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!-- 侧边栏 -->
<nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
    <div class="sidebar-sticky">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link ${pageContext.request.servletPath == '/main.jsp' ? 'active' : ''}"
                   href="${pageContext.request.contextPath}/main.jsp">
                    <i class="bi bi-house"></i> 首页
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link ${pageContext.request.servletPath == '/WEB-INF/views/room/list.jsp' ? 'active' : ''}"
                   href="${pageContext.request.contextPath}/room/list">
                    <i class="bi bi-building"></i> 房间管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link ${pageContext.request.servletPath == '/WEB-INF/views/floor/layout.jsp' ? 'active' : ''}"
                   href="${pageContext.request.contextPath}/floor/layout">
                    <i class="bi bi-grid-3x3"></i> 楼层布局
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link ${pageContext.request.servletPath == '/WEB-INF/views/device/list.jsp' ? 'active' : ''}"
                   href="${pageContext.request.contextPath}/device/list">
                    <i class="bi bi-tools"></i> 设备管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link ${pageContext.request.servletPath == '/WEB-INF/views/device/monitor.jsp' ? 'active' : ''}"
                   href="${pageContext.request.contextPath}/device/monitor">
                    <i class="bi bi-display"></i> 设备监控
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link ${pageContext.request.servletPath == '/WEB-INF/views/camera/list.jsp' ? 'active' : ''}"
                   href="${pageContext.request.contextPath}/camera/list">
                    <i class="bi bi-camera-video"></i> 摄像头管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link ${pageContext.request.servletPath == '/WEB-INF/views/reservation/list.jsp' ? 'active' : ''}"
                   href="${pageContext.request.contextPath}/reservation/list">
                    <i class="bi bi-calendar-check"></i> 预约管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link ${pageContext.request.servletPath == '/WEB-INF/views/production/list.jsp' ? 'active' : ''}"
                   href="${pageContext.request.contextPath}/production/list">
                    <i class="bi bi-diagram-3"></i> 产线管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link ${pageContext.request.servletPath == '/WEB-INF/views/user/list.jsp' ? 'active' : ''}"
                   href="${pageContext.request.contextPath}/user/list">
                    <i class="bi bi-people"></i> 用户管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link ${pageContext.request.servletPath == '/WEB-INF/views/system/settings.jsp' ? 'active' : ''}"
                   href="${pageContext.request.contextPath}/system/settings">
                    <i class="bi bi-gear"></i> 系统设置
                </a>
            </li>
        </ul>
    </div>
</nav>