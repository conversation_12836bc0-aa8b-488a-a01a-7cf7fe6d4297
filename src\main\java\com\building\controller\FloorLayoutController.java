package com.building.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 楼层布局图控制器
 */
@Controller
@RequestMapping("/layout")
public class FloorLayoutController {

    /**
     * 显示一楼布局图页面
     */
    @GetMapping("/floor1")
    public String showFloor1Layout(Model model) {
        model.addAttribute("title", "产教融合大楼一楼布局图");
        return "layout/floor1";
    }

    /**
     * 获取一楼房间信息数据
     */
    @GetMapping("/floor1/rooms")
    @ResponseBody
    public List<Map<String, Object>> getFloor1Rooms() {
        List<Map<String, Object>> rooms = new ArrayList<>();
        
        // 根据实际平面图调整房间坐标
        // 注意：这些坐标需要根据您的实际平面图进行精确调整
        
        // 左侧教学区域
        addRoom(rooms, "苏州古森研发中心", "研发中心", "产教融合研发基地", 80, 120, 180, 120);
        addRoom(rooms, "101", "教室", "多媒体教室", 80, 260, 120, 80);
        addRoom(rooms, "102", "教室", "理论教学教室", 80, 360, 120, 80);
        addRoom(rooms, "103", "实验室", "实训实验室", 80, 460, 120, 80);
        
        // 中央大厅区域
        addRoom(rooms, "大厅", "公共区域", "主入口大厅", 220, 200, 200, 200);
        
        // 右侧办公区域
        addRoom(rooms, "办公室1", "办公室", "教师办公室", 440, 120, 100, 80);
        addRoom(rooms, "办公室2", "办公室", "行政办公室", 560, 120, 100, 80);
        addRoom(rooms, "会议室", "会议室", "小型会议室", 440, 220, 120, 80);
        addRoom(rooms, "多媒体室", "多媒体教室", "演示教室", 440, 320, 120, 80);
        addRoom(rooms, "储藏室", "储藏室", "设备存放", 580, 220, 80, 60);
        
        // 辅助设施
        addRoom(rooms, "男卫生间", "公共设施", "男卫生间", 680, 120, 60, 40);
        addRoom(rooms, "女卫生间", "公共设施", "女卫生间", 680, 180, 60, 40);
        addRoom(rooms, "楼梯间1", "通道", "主楼梯", 680, 240, 60, 80);
        addRoom(rooms, "楼梯间2", "通道", "消防楼梯", 680, 340, 60, 80);
        addRoom(rooms, "电梯", "通道", "客用电梯", 680, 440, 60, 60);
        
        return rooms;
    }
    
    /**
     * 添加房间信息的辅助方法
     */
    private void addRoom(List<Map<String, Object>> rooms, String roomNumber, 
                        String roomType, String description, int x, int y, int width, int height) {
        Map<String, Object> room = new HashMap<>();
        room.put("roomNumber", roomNumber);
        room.put("roomType", roomType);
        room.put("description", description);
        room.put("x", x);
        room.put("y", y);
        room.put("width", width);
        room.put("height", height);
        rooms.add(room);
    }
} 