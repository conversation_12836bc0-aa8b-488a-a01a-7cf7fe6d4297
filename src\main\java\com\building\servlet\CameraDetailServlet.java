package com.building.servlet;

import java.io.IOException;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.model.Camera;
import com.building.model.PersonRecord;
import com.building.service.CameraService;
import com.building.service.impl.CameraServiceImpl;

/**
 * 摄像头详情Servlet
 * 用于处理摄像头详情页面的请求
 */
@WebServlet("/camera/detail")
public class CameraDetailServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private CameraService cameraService;
    
    @Override
    public void init() throws ServletException {
        cameraService = new CameraServiceImpl();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取摄像头ID
        String idParam = request.getParameter("id");
        if (idParam == null || idParam.trim().isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/camera/list");
            return;
        }
        
        try {
            int cameraId = Integer.parseInt(idParam);
            
            // 获取摄像头信息
            Camera camera = cameraService.getCameraById(cameraId);
            if (camera == null) {
                response.sendRedirect(request.getContextPath() + "/camera/list");
                return;
            }
            
            // 如果摄像头属于某个房间，获取该房间的人员记录历史
            if (camera.getRoomId() > 0) {
                List<PersonRecord> records = cameraService.getPersonRecordHistory(camera.getRoomId(), 10);
                request.setAttribute("personRecords", records);
                
                // 获取最新的人员记录
                PersonRecord latestRecord = cameraService.getLatestPersonRecord(camera.getRoomId());
                request.setAttribute("latestRecord", latestRecord);
            }
            
            // 设置请求属性
            request.setAttribute("camera", camera);
            
            // 转发到摄像头详情页面
            request.getRequestDispatcher("/WEB-INF/views/camera/detail.jsp").forward(request, response);
            
        } catch (NumberFormatException e) {
            response.sendRedirect(request.getContextPath() + "/camera/list");
        }
    }
}
