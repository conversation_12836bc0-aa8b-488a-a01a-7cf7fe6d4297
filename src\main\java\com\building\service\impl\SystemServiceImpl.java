package com.building.service.impl;

import com.building.dao.BackupDao;
import com.building.dao.SystemSettingsDao;
import com.building.model.SystemSettings;
import com.building.service.SystemService;
import com.building.util.DatabaseBackupUtil;
import com.building.util.DatabaseBackupUtil.BackupResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.mail.Authenticator;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * 系统设置服务实现类
 */
@Service
public class SystemServiceImpl implements SystemService {
    private static final Logger logger = LoggerFactory.getLogger(SystemServiceImpl.class);
    
    @Autowired
    private SystemSettingsDao systemSettingsDao;
    
    @Autowired
    private BackupDao backupDao;
    
    /**
     * 系统启动时初始化备份调度任务的下次运行时间
     */
    @PostConstruct
    public void initializeBackupSchedules() {
        try {
            logger.info("初始化备份调度任务...");
            List<Map<String, Object>> schedules = backupDao.getBackupSchedules();
            
            for (Map<String, Object> schedule : schedules) {
                if ((Boolean) schedule.get("active")) {
                    // 获取调度参数
                    int scheduleId = (Integer) schedule.get("id");
                    String scheduleType = (String) schedule.get("scheduleType");
                    Integer dayOfWeek = (Integer) schedule.get("dayOfWeek");
                    Integer dayOfMonth = (Integer) schedule.get("dayOfMonth");
                    int hour = (Integer) schedule.get("hour");
                    int minute = (Integer) schedule.get("minute");
                    
                    // 计算下次运行时间
                    Date nextRunTime = DatabaseBackupUtil.calculateNextRunTime(
                            scheduleType, dayOfWeek, dayOfMonth, hour, minute);
                    
                    // 更新下次运行时间
                    backupDao.updateScheduleRunTime(scheduleId, null, nextRunTime);
                    logger.info("已初始化调度任务[{}]，下次运行时间：{}", scheduleId, nextRunTime);
                }
            }
        } catch (Exception e) {
            logger.error("初始化备份调度任务失败", e);
        }
    }
    
    /**
     * 每分钟检查是否有到期的备份任务
     */
    @Scheduled(cron = "0 * * * * ?")
    public void checkBackupSchedules() {
        try {
            Date now = new Date();
            List<Map<String, Object>> dueSchedules = backupDao.getDueSchedules(now);
            
            for (Map<String, Object> schedule : dueSchedules) {
                int scheduleId = (Integer) schedule.get("id");
                String scheduleName = (String) schedule.get("scheduleName");
                String backupPath = (String) schedule.get("backupPath");
                int retentionDays = (Integer) schedule.get("retentionDays");
                
                logger.info("执行自动备份任务[{}]：{}", scheduleId, scheduleName);
                
                // 执行备份
                executeScheduledBackup(scheduleId, scheduleName, backupPath, retentionDays);
            }
        } catch (Exception e) {
            logger.error("检查备份调度任务异常", e);
        }
    }
    
    /**
     * 执行自动备份任务
     */
    private void executeScheduledBackup(int scheduleId, String scheduleName, String backupPath, int retentionDays) {
        try {
            // 执行备份
            BackupResult result = DatabaseBackupUtil.backup(backupPath);
            Date backupTime = new Date();
            
            // 记录备份日志
            if (result.isSuccess()) {
                backupDao.logBackup(backupTime, result.getBackupFile(), result.getBackupPath(), 
                        result.getBackupSize(), "scheduled", "success", null, result.getDuration());
                
                // 清理过期备份
                DatabaseBackupUtil.cleanupBackupFiles(backupPath, retentionDays);
                backupDao.cleanupBackupLogs(retentionDays);
                
                logger.info("自动备份任务[{}]执行成功", scheduleId);
            } else {
                backupDao.logBackup(backupTime, null, backupPath, 0, "scheduled", 
                        "failed", result.getErrorMessage(), result.getDuration());
                logger.error("自动备份任务[{}]执行失败：{}", scheduleId, result.getErrorMessage());
            }
            
            // 获取调度任务信息
            Map<String, Object> schedule = backupDao.getBackupScheduleById(scheduleId);
            if (schedule != null) {
                // 计算下次运行时间
                String scheduleType = (String) schedule.get("scheduleType");
                Integer dayOfWeek = (Integer) schedule.get("dayOfWeek");
                Integer dayOfMonth = (Integer) schedule.get("dayOfMonth");
                int hour = (Integer) schedule.get("hour");
                int minute = (Integer) schedule.get("minute");
                
                Date nextRunTime = DatabaseBackupUtil.calculateNextRunTime(
                        scheduleType, dayOfWeek, dayOfMonth, hour, minute);
                
                // 更新上次运行时间和下次运行时间
                backupDao.updateScheduleRunTime(scheduleId, backupTime, nextRunTime);
                logger.info("更新调度任务[{}]下次运行时间：{}", scheduleId, nextRunTime);
            }
        } catch (Exception e) {
            logger.error("执行自动备份任务[{}]异常", scheduleId, e);
        }
    }
    
    @Override
    public SystemSettings getCurrentSettings() {
        try {
            // 从DAO获取设置
            return systemSettingsDao.getSettings();
        } catch (Exception e) {
            logger.error("获取系统设置失败", e);
            return new SystemSettings(); // 返回默认设置
        }
    }
    
    @Override
    public boolean updateSecuritySettings(int minPasswordLength, boolean requireUppercase, boolean requireNumbers, 
                                       boolean requireSpecialChars, int maxLoginAttempts) {
        try {
            // 获取当前设置
            SystemSettings settings = systemSettingsDao.getSettings();
            
            // 更新设置
            settings.setMinPasswordLength(minPasswordLength);
            settings.setRequireUppercase(requireUppercase);
            settings.setRequireNumbers(requireNumbers);
            settings.setRequireSpecialChars(requireSpecialChars);
            settings.setMaxLoginAttempts(maxLoginAttempts);
            settings.setUpdateTime(new Date());
            
            // 保存更新后的设置
            systemSettingsDao.updateSettings(settings);
            
            logger.info("安全设置更新成功");
            return true;
        } catch (Exception e) {
            logger.error("更新安全设置失败", e);
            return false;
        }
    }
    
    @Override
    public boolean updateEmailSettings(String smtpServer, int smtpPort, String senderEmail, String emailPassword) {
        try {
            // 获取当前设置
            SystemSettings settings = systemSettingsDao.getSettings();
            
            // 更新设置
            settings.setSmtpServer(smtpServer);
            settings.setSmtpPort(smtpPort);
            settings.setSenderEmail(senderEmail);
            
            // 只有在提供了密码的情况下才更新密码（考虑到安全性，密码应该加密存储）
            if (emailPassword != null && !emailPassword.trim().isEmpty()) {
                // 实际应用中应该对密码进行加密
                settings.setEmailPassword(emailPassword);
            }
            
            settings.setUpdateTime(new Date());
            
            // 保存更新后的设置
            systemSettingsDao.updateSettings(settings);
            
            logger.info("邮件设置更新成功");
            return true;
        } catch (Exception e) {
            logger.error("更新邮件设置失败", e);
            return false;
        }
    }
    
    @Override
    public boolean updateBackupSettings(boolean autoBackup, String backupFrequency, int backupRetention, String backupPath) {
        try {
            // 获取当前设置
            SystemSettings settings = systemSettingsDao.getSettings();
            boolean previousAutoBackup = settings.isAutoBackup();
            
            // 更新设置
            settings.setAutoBackup(autoBackup);
            settings.setBackupFrequency(backupFrequency);
            settings.setBackupRetention(backupRetention);
            settings.setBackupPath(backupPath);
            settings.setUpdateTime(new Date());
            
            // 保存更新后的设置
            systemSettingsDao.updateSettings(settings);
            
            // 如果自动备份设置发生变化，更新调度任务
            if (autoBackup != previousAutoBackup) {
                updateDefaultBackupSchedule(autoBackup, backupFrequency, backupRetention, backupPath);
            }
            
            logger.info("备份设置更新成功");
            return true;
        } catch (Exception e) {
            logger.error("更新备份设置失败", e);
            return false;
        }
    }
    
    /**
     * 更新默认备份调度任务
     */
    private void updateDefaultBackupSchedule(boolean active, String frequency, int retentionDays, String backupPath) {
        try {
            // 查找默认调度任务
            List<Map<String, Object>> schedules = backupDao.getBackupSchedules();
            int scheduleId = -1;
            
            for (Map<String, Object> schedule : schedules) {
                String name = (String) schedule.get("scheduleName");
                if ("每日凌晨备份".equals(name)) {
                    scheduleId = (Integer) schedule.get("id");
                    break;
                }
            }
            
            if (scheduleId != -1) {
                // 存在默认任务，更新它
                Integer dayOfWeek = null;
                Integer dayOfMonth = null;
                int hour = 2; // 凌晨2点
                int minute = 0;
                
                switch (frequency) {
                    case "weekly":
                        dayOfWeek = 2; // 周一
                        break;
                    case "monthly":
                        dayOfMonth = 1; // 每月1号
                        break;
                }
                
                backupDao.updateSchedule(scheduleId, active, frequency, dayOfWeek, dayOfMonth, 
                        hour, minute, retentionDays, backupPath);
                
                // 如果激活了任务，计算并更新下次运行时间
                if (active) {
                    Date nextRunTime = DatabaseBackupUtil.calculateNextRunTime(
                            frequency, dayOfWeek, dayOfMonth, hour, minute);
                    backupDao.updateScheduleRunTime(scheduleId, null, nextRunTime);
                }
                
                logger.info("更新默认备份调度任务成功");
            } else {
                // 不存在默认任务，创建一个
                Integer dayOfWeek = null;
                Integer dayOfMonth = null;
                int hour = 2; // 凌晨2点
                int minute = 0;
                
                switch (frequency) {
                    case "weekly":
                        dayOfWeek = 2; // 周一
                        break;
                    case "monthly":
                        dayOfMonth = 1; // 每月1号
                        break;
                }
                
                int newId = backupDao.addSchedule("每日凌晨备份", active, frequency, dayOfWeek, 
                        dayOfMonth, hour, minute, retentionDays, backupPath);
                
                // 如果激活了任务，计算并更新下次运行时间
                if (active && newId != -1) {
                    Date nextRunTime = DatabaseBackupUtil.calculateNextRunTime(
                            frequency, dayOfWeek, dayOfMonth, hour, minute);
                    backupDao.updateScheduleRunTime(newId, null, nextRunTime);
                }
                
                logger.info("创建默认备份调度任务成功");
            }
        } catch (Exception e) {
            logger.error("更新默认备份调度任务失败", e);
        }
    }
    
    @Override
    public boolean updateNotificationSettings(boolean systemNotifications, boolean emailNotifications, 
                                           boolean notifyReservation, boolean notifyMaintenance, boolean notifySystem) {
        try {
            // 获取当前设置
            SystemSettings settings = systemSettingsDao.getSettings();
            
            // 更新设置
            settings.setSystemNotifications(systemNotifications);
            settings.setEmailNotifications(emailNotifications);
            settings.setNotifyReservation(notifyReservation);
            settings.setNotifyMaintenance(notifyMaintenance);
            settings.setNotifySystem(notifySystem);
            settings.setUpdateTime(new Date());
            
            // 保存更新后的设置
            systemSettingsDao.updateSettings(settings);
            
            logger.info("通知设置更新成功");
            return true;
        } catch (Exception e) {
            logger.error("更新通知设置失败", e);
            return false;
        }
    }
    
    @Override
    public boolean testEmailConnection(String smtpServer, int smtpPort, String senderEmail, String emailPassword) {
        try {
            // 设置邮件服务器属性
            Properties props = new Properties();
            props.put("mail.smtp.host", smtpServer);
            props.put("mail.smtp.port", smtpPort);
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            
            // 创建认证器
            Authenticator auth = new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(senderEmail, emailPassword);
                }
            };
            
            // 创建会话
            Session session = Session.getInstance(props, auth);
            
            // 测试连接
            Transport transport = session.getTransport("smtp");
            transport.connect(smtpServer, smtpPort, senderEmail, emailPassword);
            transport.close();
            
            logger.info("邮件服务器连接测试成功");
            return true;
        } catch (Exception e) {
            logger.error("邮件服务器连接测试失败", e);
            return false;
        }
    }
    
    @Override
    public boolean executeBackup() {
        try {
            // 获取当前设置
            SystemSettings settings = systemSettingsDao.getSettings();
            
            // 获取备份路径
            String backupPath = settings.getBackupPath();
            if (backupPath == null || backupPath.trim().isEmpty()) {
                logger.error("备份失败：未指定备份路径");
                return false;
            }
            
            // 执行实际备份
            BackupResult result = DatabaseBackupUtil.backup(backupPath);
            Date backupTime = new Date();
            
            // 记录备份日志
            if (result.isSuccess()) {
                backupDao.logBackup(backupTime, result.getBackupFile(), result.getBackupPath(), 
                        result.getBackupSize(), "manual", "success", null, result.getDuration());
                
                // 更新上次备份时间并保存设置
                settings.setLastBackupTime(backupTime);
                systemSettingsDao.updateSettings(settings);
                
                // 清理过期备份
                int retentionDays = settings.getBackupRetention();
                DatabaseBackupUtil.cleanupBackupFiles(backupPath, retentionDays);
                backupDao.cleanupBackupLogs(retentionDays);
                
                logger.info("系统备份执行成功，备份文件: {}", result.getBackupPath());
                return true;
            } else {
                backupDao.logBackup(backupTime, null, backupPath, 0, "manual", 
                        "failed", result.getErrorMessage(), result.getDuration());
                
                logger.error("系统备份执行失败: {}", result.getErrorMessage());
                return false;
            }
        } catch (Exception e) {
            logger.error("执行系统备份失败", e);
            return false;
        }
    }
} 