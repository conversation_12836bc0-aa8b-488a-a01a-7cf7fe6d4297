package com.building.servlet;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.model.Camera;
import com.building.service.CameraService;
import com.building.service.impl.CameraServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 视频流Servlet
 * 用于处理视频流页面的请求
 */
@WebServlet("/camera/stream")
public class VideoStreamServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private CameraService cameraService;
    private ObjectMapper objectMapper;
    
    @Override
    public void init() throws ServletException {
        cameraService = new CameraServiceImpl();
        objectMapper = new ObjectMapper();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取摄像头ID
        String idParam = request.getParameter("id");
        if (idParam == null || idParam.trim().isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/camera/list");
            return;
        }
        
        try {
            int cameraId = Integer.parseInt(idParam);
            
            // 获取摄像头信息
            Camera camera = cameraService.getCameraById(cameraId);
            if (camera == null) {
                response.sendRedirect(request.getContextPath() + "/camera/list");
                return;
            }
            
            // 设置请求属性
            request.setAttribute("camera", camera);
            
            // 转发到视频流页面
            request.getRequestDispatcher("/WEB-INF/views/camera/stream.jsp").forward(request, response);
            
        } catch (NumberFormatException e) {
            response.sendRedirect(request.getContextPath() + "/camera/list");
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 设置响应类型
        response.setContentType("application/json;charset=UTF-8");
        
        // 创建结果Map
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取请求参数
            String cameraIdStr = request.getParameter("cameraId");
            String action = request.getParameter("action");
            
            // 参数验证
            if (cameraIdStr == null || cameraIdStr.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "摄像头ID不能为空");
                objectMapper.writeValue(response.getWriter(), result);
                return;
            }
            
            int cameraId = Integer.parseInt(cameraIdStr);
            
            // 获取摄像头信息
            Camera camera = cameraService.getCameraById(cameraId);
            if (camera == null) {
                result.put("success", false);
                result.put("message", "摄像头不存在");
                objectMapper.writeValue(response.getWriter(), result);
                return;
            }
            
            // 根据不同的操作执行不同的逻辑
            if ("getStreamUrl".equals(action)) {
                // 返回摄像头的RTSP URL
                result.put("success", true);
                result.put("rtspUrl", camera.getRtspUrl());
                result.put("cameraName", camera.getName());
                result.put("cameraStatus", camera.getStatus());
            } else if ("updateStatus".equals(action)) {
                // 更新摄像头状态
                String statusStr = request.getParameter("status");
                if (statusStr != null && !statusStr.trim().isEmpty()) {
                    int status = Integer.parseInt(statusStr);
                    boolean success = cameraService.updateCameraStatus(cameraId, status);
                    result.put("success", success);
                    result.put("message", success ? "状态更新成功" : "状态更新失败");
                } else {
                    result.put("success", false);
                    result.put("message", "状态参数不能为空");
                }
            } else {
                result.put("success", false);
                result.put("message", "不支持的操作: " + action);
            }
            
        } catch (NumberFormatException e) {
            result.put("success", false);
            result.put("message", "无效的参数格式");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "处理请求时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 返回JSON结果
        objectMapper.writeValue(response.getWriter(), result);
    }
}
