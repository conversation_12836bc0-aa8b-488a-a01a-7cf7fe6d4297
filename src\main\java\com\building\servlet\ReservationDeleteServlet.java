package com.building.servlet;

import java.io.IOException;
import java.io.PrintWriter;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.model.ApiResponse;
import com.building.model.User;
import com.building.service.ReservationService;
import com.building.service.impl.ReservationServiceImpl;
import com.building.util.JsonUtil;

/**
 * 预约删除控制器
 * 处理预约的删除
 */
@WebServlet("/reservation/delete")
public class ReservationDeleteServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    private ReservationService reservationService;
    
    @Override
    public void init() throws ServletException {
        reservationService = new ReservationServiceImpl();
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 设置响应类型
        response.setContentType("application/json;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        // 检查用户是否登录
        HttpSession session = request.getSession();
        User user = (User) session.getAttribute("user");
        if (user == null) {
            ApiResponse apiResponse = new ApiResponse(false, "用户未登录");
            out.print(JsonUtil.toJson(apiResponse));
            return;
        }
        
        // 获取预约ID
        String idStr = request.getParameter("id");
        if (idStr == null || idStr.trim().isEmpty()) {
            ApiResponse apiResponse = new ApiResponse(false, "预约ID不能为空");
            out.print(JsonUtil.toJson(apiResponse));
            return;
        }
        
        try {
            int id = Integer.parseInt(idStr);
            
            // 检查用户权限
            if (!"admin".equals(user.getRole())) {
                // 普通用户只能删除自己的预约
                if (reservationService.getReservationById(id) == null || 
                    reservationService.getReservationById(id).getUserId() != user.getId()) {
                    ApiResponse apiResponse = new ApiResponse(false, "权限不足，只能删除自己的预约");
                    out.print(JsonUtil.toJson(apiResponse));
                    return;
                }
            }
            
            // 删除预约
            boolean success = reservationService.deleteReservation(id);
            
            if (success) {
                ApiResponse apiResponse = new ApiResponse(true, "预约删除成功");
                out.print(JsonUtil.toJson(apiResponse));
            } else {
                ApiResponse apiResponse = new ApiResponse(false, "预约删除失败");
                out.print(JsonUtil.toJson(apiResponse));
            }
        } catch (NumberFormatException e) {
            ApiResponse apiResponse = new ApiResponse(false, "预约ID格式不正确");
            out.print(JsonUtil.toJson(apiResponse));
        } catch (Exception e) {
            e.printStackTrace();
            ApiResponse apiResponse = new ApiResponse(false, "系统错误：" + e.getMessage());
            out.print(JsonUtil.toJson(apiResponse));
        }
    }
} 