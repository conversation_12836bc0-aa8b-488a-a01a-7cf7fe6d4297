<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<style>
:root {
    --primary-color: #4e73df;
    --secondary-color: #6c757d;
    --success-color: #1cc88a;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --info-color: #36b9cc;
    --light-color: #f8f9fa;
    --dark-color: #5a5c69;
}

/* 卡片样式 */
.reservation-card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    margin-bottom: 1.5rem;
    height: 100%;
}

.reservation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
}

/* 状态标签样式 */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 30px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
}

.status-pending {
    background-color: rgba(246, 194, 62, 0.15);
    color: var(--warning-color);
    border: 1px solid rgba(246, 194, 62, 0.3);
}

.status-approved {
    background-color: rgba(28, 200, 138, 0.15);
    color: var(--success-color);
    border: 1px solid rgba(28, 200, 138, 0.3);
}

.status-rejected {
    background-color: rgba(231, 74, 59, 0.15);
    color: var(--danger-color);
    border: 1px solid rgba(231, 74, 59, 0.3);
}

.status-completed {
    background-color: rgba(54, 185, 204, 0.15);
    color: var(--info-color);
    border: 1px solid rgba(54, 185, 204, 0.3);
}

.status-cancelled {
    background-color: rgba(108, 117, 125, 0.15);
    color: var(--secondary-color);
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.status-available {
    background-color: rgba(28, 200, 138, 0.15);
    color: var(--success-color);
    border: 1px solid rgba(28, 200, 138, 0.3);
}

.status-occupied {
    background-color: rgba(231, 74, 59, 0.15);
    color: var(--danger-color);
    border: 1px solid rgba(231, 74, 59, 0.3);
}

/* 图标样式 */
.reservation-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

/* 统计卡片样式 */
.stats-card {
    padding: 1.5rem;
    border-radius: 0.75rem;
    color: white;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
    position: relative;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 180px;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: skewX(-30deg) translateX(50%);
}

.stats-number {
    font-size: 2.2rem;
    font-weight: 700;
    margin-top: 0.5rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.stats-label {
    font-size: 1rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.9;
}

.stats-icon {
    position: absolute;
    bottom: 1rem;
    right: 1.5rem;
    font-size: 3.5rem;
    opacity: 0.3;
}

/* 卡片内容样式 */
.card-title {
    font-weight: 600;
    color: var(--dark-color);
}

.reservation-details {
    margin-bottom: 1.2rem;
}

.detail-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.8rem;
    color: #5a5c69;
}

.detail-icon {
    width: 1.5rem;
    color: var(--primary-color);
    margin-right: 0.5rem;
}

/* 按钮样式 */
.action-btn {
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
}

/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.page-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0;
}

/* 动画效果 */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* 筛选控件样式 */
.filter-container {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
}

.filter-input {
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
}

.filter-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.filter-label {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* 加载中效果 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s, visibility 0.3s;
}

.loading-overlay.show {
    visibility: visible;
    opacity: 1;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<!-- 加载中效果 -->
<div id="loadingOverlay" class="loading-overlay">
    <div class="loading-spinner"></div>
</div>

<div class="container-fluid">
    <!-- 页面标题和添加按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-4 animate-fade-in">
        <h1 class="page-title"><i class="bi bi-calendar2-check me-2"></i>预约管理</h1>
        <a href="${pageContext.request.contextPath}/reservation/add" class="btn btn-primary action-btn">
            <i class="bi bi-plus-lg"></i> 新增预约
        </a>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-container animate-fade-in">
        <div class="row g-3">
            <div class="col-md-3">
                <label for="statusFilter" class="filter-label">状态筛选</label>
                <select id="statusFilter" class="form-select filter-input">
                    <option value="">全部状态</option>
                    <option value="待审核">待审核</option>
                    <option value="已批准">已批准</option>
                    <option value="已拒绝">已拒绝</option>
                    <option value="已完成">已完成</option>
                    <option value="已取消">已取消</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="typeFilter" class="filter-label">预约类型</label>
                <select id="typeFilter" class="form-select filter-input">
                    <option value="">全部类型</option>
                    <option value="room">房间预约</option>
                    <option value="device">设备预约</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="searchInput" class="filter-label">搜索</label>
                <input type="text" id="searchInput" class="form-control filter-input" placeholder="输入关键词搜索...">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button id="resetFilters" class="btn btn-secondary w-100">重置筛选</button>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4 animate-slide-up">
        <div class="col-md-4">
            <div class="stats-card" style="background: linear-gradient(45deg, #4e73df, #224abe);">
                <div>
                    <span class="stats-label">总预约数</span>
                    <h2 class="stats-number">${totalReservations}</h2>
                </div>
                <i class="bi bi-calendar-check stats-icon"></i>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card" style="background: linear-gradient(45deg, #1cc88a, #13855c);">
                <div>
                    <span class="stats-label">已批准</span>
                    <h2 class="stats-number">${approvedReservations}</h2>
                </div>
                <i class="bi bi-check-circle stats-icon"></i>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card" style="background: linear-gradient(45deg, #f6c23e, #dda20a);">
                <div>
                    <span class="stats-label">待审核</span>
                    <h2 class="stats-number">${pendingReservations}</h2>
                </div>
                <i class="bi bi-hourglass-split stats-icon"></i>
            </div>
        </div>
    </div>

    <!-- 预约列表 -->
    <div class="row reservation-list">
        <c:forEach items="${reservations}" var="reservation" varStatus="status">
            <div class="col-md-6 mb-4 reservation-item animate-fade-in" 
                 data-status="${reservation.status}" 
                 data-type="${not empty reservation.roomId ? 'room' : 'device'}">
                <div class="card reservation-card h-100" style="animation-delay: ${status.index * 0.1}s">
                    <div class="card-header bg-transparent border-0">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <c:choose>
                                    <c:when test="${not empty reservation.roomId}">
                                        <i class="bi bi-building me-2 text-primary"></i>房间预约
                                    </c:when>
                                    <c:when test="${not empty reservation.deviceId}">
                                        <i class="bi bi-tools me-2 text-primary"></i>设备预约
                                    </c:when>
                                    <c:otherwise>
                                        <i class="bi bi-question-circle me-2 text-primary"></i>未知预约
                                    </c:otherwise>
                                </c:choose>
                            </h5>
                            <span class="status-badge status-${reservation.status == '待审核' ? 'pending' :
                                reservation.status == '已批准' ? 'approved' :
                                reservation.status == '已拒绝' ? 'rejected' :
                                reservation.status == '已完成' ? 'completed' : 'cancelled'}">
                                <i class="bi ${reservation.status == '待审核' ? 'bi-hourglass-split' :
                                    reservation.status == '已批准' ? 'bi-check-circle-fill' :
                                    reservation.status == '已拒绝' ? 'bi-x-circle-fill' :
                                    reservation.status == '已完成' ? 'bi-check-all' : 'bi-slash-circle-fill'}"></i>
                                ${reservation.status}
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="reservation-details">
                            <c:if test="${not empty reservation.roomId}">
                                <div class="detail-item">
                                    <i class="bi bi-door-open detail-icon"></i>
                                    <div>
                                        <strong>预约房间：</strong>${reservation.roomNumber}
                                    </div>
                                </div>
                            </c:if>
                            <c:if test="${not empty reservation.deviceId}">
                                <div class="detail-item">
                                    <i class="bi bi-cpu detail-icon"></i>
                                    <div>
                                        <strong>预约设备：</strong>${not empty reservation.deviceName ? reservation.deviceName : '未知设备'}
                                    </div>
                                </div>
                            </c:if>
                            <div class="detail-item">
                                <i class="bi bi-person detail-icon"></i>
                                <div>
                                    <strong>预约人：</strong>${reservation.userName}
                                </div>
                            </div>
                            <div class="detail-item">
                                <i class="bi bi-clock detail-icon"></i>
                                <div>
                                    <strong>开始时间：</strong>${reservation.startTime}
                                </div>
                            </div>
                            <div class="detail-item">
                                <i class="bi bi-clock-history detail-icon"></i>
                                <div>
                                    <strong>结束时间：</strong>${reservation.endTime}
                                </div>
                            </div>
                            <div class="detail-item">
                                <i class="bi bi-card-text detail-icon"></i>
                                <div>
                                    <strong>用途：</strong>${reservation.purpose}
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end gap-2">
                            <c:if test="${sessionScope.user.role == 'admin' && reservation.status == '待审核'}">
                                <button class="btn btn-success action-btn approve-btn" onclick="updateStatus(${reservation.id}, '已批准')">
                                    <i class="bi bi-check-lg"></i> 批准
                                </button>
                                <button class="btn btn-danger action-btn reject-btn" onclick="updateStatus(${reservation.id}, '已拒绝')">
                                    <i class="bi bi-x-lg"></i> 拒绝
                                </button>
                            </c:if>
                            <c:if test="${sessionScope.user.id == reservation.userId && (reservation.status == '待审核' || reservation.status == '已批准')}">
                                <button class="btn btn-warning action-btn cancel-btn" onclick="updateStatus(${reservation.id}, '已取消')">
                                    <i class="bi bi-x-circle"></i> 取消
                                </button>
                            </c:if>
                            <c:if test="${sessionScope.user.role == 'admin'}">
                                <button class="btn btn-danger action-btn delete-btn" onclick="deleteReservation(${reservation.id})">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                            </c:if>
                        </div>
                    </div>
                </div>
            </div>
        </c:forEach>
    </div>

    <!-- 没有预约时的提示 -->
    <div id="noReservations" class="text-center mt-5 d-none">
        <i class="bi bi-calendar-x text-secondary" style="font-size: 4rem;"></i>
        <h3 class="mt-3 text-secondary">没有找到预约记录</h3>
        <p class="text-muted">尝试修改筛选条件或添加新的预约</p>
    </div>
</div>

<script>
// 显示加载中效果
function showLoading() {
    document.getElementById('loadingOverlay').classList.add('show');
}

// 隐藏加载中效果
function hideLoading() {
    document.getElementById('loadingOverlay').classList.remove('show');
}

// 使用SweetAlert2进行提示
function showAlert(icon, title, text) {
    Swal.fire({
        icon: icon,
        title: title,
        text: text,
        confirmButtonColor: '#4e73df'
    });
}

// 使用SweetAlert2进行确认对话框
function showConfirm(title, text, confirmText, cancelText, confirmCallback) {
    Swal.fire({
        title: title,
        text: text,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#4e73df',
        cancelButtonColor: '#6c757d',
        confirmButtonText: confirmText,
        cancelButtonText: cancelText
    }).then((result) => {
        if (result.isConfirmed) {
            confirmCallback();
        }
    });
}

// 更新预约状态
function updateStatus(id, status) {
    let statusText = '';
    switch(status) {
        case '已批准':
            statusText = '批准';
            break;
        case '已拒绝':
            statusText = '拒绝';
            break;
        case '已取消':
            statusText = '取消';
            break;
        default:
            statusText = '更新状态为"' + status + '"';
    }
    
    showConfirm(
        '确认操作',
        '确定要' + statusText + '这条预约吗？',
        '确定',
        '取消',
        function() {
            showLoading();
            
            fetch('${pageContext.request.contextPath}/reservation/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: 'id=' + id + '&status=' + encodeURIComponent(status)
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert('success', '操作成功', '预约状态已更新');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('error', '操作失败', data.message || '状态更新失败');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error:', error);
                showAlert('error', '系统错误', '请稍后重试');
            });
        }
    );
}

// 删除预约
function deleteReservation(id) {
    showConfirm(
        '确认删除',
        '确定要删除这条预约记录吗？此操作无法撤销！',
        '删除',
        '取消',
        function() {
            showLoading();
            
            fetch('${pageContext.request.contextPath}/reservation/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: 'id=' + id
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert('success', '删除成功', '预约记录已删除');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('error', '删除失败', data.message || '操作未完成');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error:', error);
                showAlert('error', '系统错误', '请稍后重试');
            });
        }
    );
}

// 筛选功能
document.addEventListener('DOMContentLoaded', function() {
    const statusFilter = document.getElementById('statusFilter');
    const typeFilter = document.getElementById('typeFilter');
    const searchInput = document.getElementById('searchInput');
    const resetButton = document.getElementById('resetFilters');
    const reservationItems = document.querySelectorAll('.reservation-item');
    const noReservationsDiv = document.getElementById('noReservations');

    function applyFilters() {
        const status = statusFilter.value.toLowerCase();
        const type = typeFilter.value.toLowerCase();
        const searchText = searchInput.value.toLowerCase();
        
        let visibleCount = 0;
        
        reservationItems.forEach(item => {
            const itemStatus = item.getAttribute('data-status').toLowerCase();
            const itemType = item.getAttribute('data-type').toLowerCase();
            const itemText = item.textContent.toLowerCase();
            
            const statusMatch = status === '' || itemStatus === status;
            const typeMatch = type === '' || itemType === type;
            const searchMatch = searchText === '' || itemText.includes(searchText);
            
            if (statusMatch && typeMatch && searchMatch) {
                item.classList.remove('d-none');
                visibleCount++;
            } else {
                item.classList.add('d-none');
            }
        });
        
        if (visibleCount === 0) {
            noReservationsDiv.classList.remove('d-none');
        } else {
            noReservationsDiv.classList.add('d-none');
        }
    }
    
    statusFilter.addEventListener('change', applyFilters);
    typeFilter.addEventListener('change', applyFilters);
    searchInput.addEventListener('input', applyFilters);
    
    resetButton.addEventListener('click', function() {
        statusFilter.value = '';
        typeFilter.value = '';
        searchInput.value = '';
        applyFilters();
    });
});
</script>
