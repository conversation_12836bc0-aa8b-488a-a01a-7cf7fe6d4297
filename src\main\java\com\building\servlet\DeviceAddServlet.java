package com.building.servlet;

import com.building.dao.DeviceDao;
import com.building.model.Device;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@WebServlet("/device/add")
public class DeviceAddServlet extends HttpServlet {
    private DeviceDao deviceDao;
    private ObjectMapper objectMapper;

    @Override
    public void init() throws ServletException {
        deviceDao = new DeviceDao();
        objectMapper = new ObjectMapper();
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");
        Map<String, Object> result = new HashMap<>();
        
        try {
            Device device = new Device();
            device.setName(request.getParameter("name"));
            device.setType(request.getParameter("type"));
            device.setLocation(request.getParameter("location"));
            device.setStatus(request.getParameter("status"));
            device.setManufacturer(request.getParameter("manufacturer"));
            device.setModel(request.getParameter("model"));
            device.setSerialNumber(request.getParameter("serialNumber"));
            device.setDescription(request.getParameter("description"));
            
            boolean success = deviceDao.addDevice(device);
            
            result.put("success", success);
            if (success) {
                result.put("message", "设备添加成功");
            } else {
                result.put("message", "设备添加失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        
        objectMapper.writeValue(response.getWriter(), result);
    }
} 