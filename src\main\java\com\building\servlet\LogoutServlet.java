package com.building.servlet;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

/**
 * 退出登录控制器
 * 清除用户会话并重定向到登录页面
 * 
 * <AUTHOR>
 * @date 2024-03-04
 */
@WebServlet("/logout")
public class LogoutServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    /**
     * 处理退出登录请求
     * 
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws ServletException 如果处理请求时发生错误
     * @throws IOException 如果发生I/O错误
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 获取会话并使其失效
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.invalidate();
        }
        
        // 重定向到登录页面
        response.sendRedirect("login.jsp");
    }
} 