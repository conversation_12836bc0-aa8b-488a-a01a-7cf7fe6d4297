package com.building.servlet;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.model.Camera;
import com.building.service.CameraService;
import com.building.service.impl.CameraServiceImpl;

/**
 * 摄像头列表Servlet
 * 用于处理摄像头列表页面的请求
 */
@WebServlet("/camera/list")
public class CameraListServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private CameraService cameraService;
    
    @Override
    public void init() throws ServletException {
        cameraService = new CameraServiceImpl();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取摄像头列表
        List<Camera> cameras = cameraService.getAllCameras();
        request.setAttribute("cameras", cameras);
        
        // 获取摄像头统计信息
        Map<String, Integer> stats = cameraService.getCameraStats();
        request.setAttribute("cameraStats", stats);
        
        // 转发到摄像头列表页面
        request.getRequestDispatcher("/WEB-INF/views/camera/list.jsp").forward(request, response);
    }
}
