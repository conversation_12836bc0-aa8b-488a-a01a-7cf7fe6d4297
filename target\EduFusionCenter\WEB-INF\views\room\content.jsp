<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4">
    <h1 class="h2"><i class="bi bi-building me-2 text-primary"></i>房间管理</h1>
    <div class="btn-toolbar mb-md-0">
        <div class="input-group me-2">
            <input type="text" class="form-control form-control-sm" placeholder="搜索房间..." id="roomSearchInput">
            <button class="btn btn-sm btn-outline-secondary" type="button">
                <i class="bi bi-search"></i>
            </button>
        </div>
        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addRoomModal">
            <i class="bi bi-plus-circle me-1"></i> 添加房间
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card stats-card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #4e73df, #224abe);">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1 text-white-50">房间总数</h6>
                        <h2 class="mt-3 mb-0 text-white fw-bold">${totalRooms}</h2>
                        <p class="text-white-50 mt-2 mb-0"><i class="bi bi-arrow-up-right"></i> 较上月增长 5%</p>
                    </div>
                    <div class="stats-icon-container bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="bi bi-building stats-icon text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card stats-card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #1cc88a, #13855c);">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1 text-white-50">已使用房间</h6>
                        <h2 class="mt-3 mb-0 text-white fw-bold">${usedRooms}</h2>
                        <p class="text-white-50 mt-2 mb-0"><i class="bi bi-arrow-up-right"></i> 使用率 65%</p>
                    </div>
                    <div class="stats-icon-container bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="bi bi-check-circle stats-icon text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card stats-card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #f6c23e, #dda20a);">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1 text-white-50">空闲房间</h6>
                        <h2 class="mt-3 mb-0 text-white fw-bold">${freeRooms}</h2>
                        <p class="text-white-50 mt-2 mb-0"><i class="bi bi-arrow-down-right"></i> 空闲率 35%</p>
                    </div>
                    <div class="stats-icon-container bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="bi bi-circle stats-icon text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选器 -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body p-4">
        <div class="row g-3">
            <div class="col-md-3">
                <label for="floorFilter" class="form-label">楼层</label>
                <select class="form-select" id="floorFilter">
                    <option value="">全部楼层</option>
                    <option value="1">1楼</option>
                    <option value="2">2楼</option>
                    <option value="3">3楼</option>
                    <option value="4">4楼</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="typeFilter" class="form-label">房间类型</label>
                <select class="form-select" id="typeFilter">
                    <option value="">全部类型</option>
                    <option value="教室">教室</option>
                    <option value="实验室">实验室</option>
                    <option value="办公室">办公室</option>
                    <option value="会议室">会议室</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="statusFilter" class="form-label">状态</label>
                <select class="form-select" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="空闲">空闲</option>
                    <option value="使用中">使用中</option>
                    <option value="维修中">维修中</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button class="btn btn-primary w-100" id="applyFilters">
                    <i class="bi bi-funnel-fill me-2"></i>应用筛选
                </button>
            </div>
        </div>
    </div>
</div>



<!-- 房间列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-0 pt-4 pb-3">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0 fw-bold"><i class="bi bi-list-ul text-success me-2"></i>房间列表</h5>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-outline-secondary">
                    <i class="bi bi-download"></i> 导出
                </button>
                <button type="button" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-grid"></i> 网格视图
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="bg-light">
                    <tr>
                        <th class="border-0 ps-4">房间号</th>
                        <th class="border-0">楼层</th>
                        <th class="border-0">类型</th>
                        <th class="border-0">面积(m²)</th>
                        <th class="border-0">状态</th>
                        <th class="border-0">描述</th>
                        <th class="border-0 text-end pe-4">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <c:forEach items="${rooms}" var="room">
                        <tr>
                            <td class="ps-4 fw-bold">${room.roomNumber}</td>
                            <td>
                                <span class="badge bg-light text-dark">${room.floorNumber}楼</span>
                            </td>
                            <td>
                                <span class="badge bg-info text-white">${room.roomType}</span>
                            </td>
                            <td>${room.area}</td>
                            <td>
                                <span class="badge ${room.status == '空闲中' ? 'bg-success' : room.status == '维修中' ? 'bg-warning' : 'bg-danger'} rounded-pill px-3">
                                    ${room.status == '空闲中' ? '空闲' : room.status == '维修中' ? '维修中' : '使用中'}
                                </span>
                            </td>
                            <td>
                                <span class="text-truncate d-inline-block" style="max-width: 150px;" title="${room.description}">
                                    ${room.description}
                                </span>
                            </td>
                            <td class="text-end pe-4">
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-outline-primary rounded-pill me-1" onclick="editRoom(${room.id})">
                                        <i class="bi bi-pencil"></i> 编辑
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger rounded-pill" onclick="deleteRoom(${room.id})">
                                        <i class="bi bi-trash"></i> 删除
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </c:forEach>
                </tbody>
            </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-3 border-top">
            <span class="text-muted">显示 1-10 条，共 ${rooms.size()} 条</span>
            <nav>
                <ul class="pagination pagination-sm mb-0">
                    <li class="page-item disabled"><a class="page-link" href="#">上一页</a></li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item"><a class="page-link" href="#">下一页</a></li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 添加房间模态框 -->
<div class="modal fade" id="addRoomModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="bi bi-plus-circle me-2"></i>添加新房间</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4">
                <form id="addRoomForm" action="${pageContext.request.contextPath}/room/add" method="post">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="roomNumber" name="roomNumber" placeholder="房间号" required>
                                <label for="roomNumber">房间号</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="number" class="form-control" id="floorNumber" name="floorNumber" placeholder="楼层" required>
                                <label for="floorNumber">楼层</label>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="roomType" name="roomType" required>
                                    <option value="办公室">办公室</option>
                                    <option value="会议室">会议室</option>
                                    <option value="实验室">实验室</option>
                                    <option value="教室">教室</option>
                                </select>
                                <label for="roomType">房间类型</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="number" step="0.01" class="form-control" id="area" name="area" placeholder="面积" required>
                                <label for="area">面积(m²)</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-floating mb-3">
                        <select class="form-select" id="status" name="status" required>
                            <option value="空闲中">空闲</option>
                            <option value="使用中">使用中</option>
                            <option value="维修中">维修中</option>
                        </select>
                        <label for="status">状态</label>
                    </div>

                    <div class="form-floating mb-3">
                        <textarea class="form-control" id="description" name="description" placeholder="描述" style="height: 100px"></textarea>
                        <label for="description">描述</label>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted small">房间图片（可选）</label>
                        <div class="input-group">
                            <input type="file" class="form-control" id="roomImage" name="roomImage">
                            <label class="input-group-text" for="roomImage">上传</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-light rounded-pill px-4" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-2"></i>取消
                </button>
                <button type="submit" form="addRoomForm" class="btn btn-primary rounded-pill px-4">
                    <i class="bi bi-check-circle me-2"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // 初始化筛选功能
    document.addEventListener('DOMContentLoaded', function() {
        // 搜索功能
        const searchInput = document.getElementById('roomSearchInput');
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchText = this.value.toLowerCase();
                const rows = document.querySelectorAll('table tbody tr');

                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    row.style.display = text.includes(searchText) ? '' : 'none';
                });
            });
        }

        // 筛选按钮
        const applyFiltersBtn = document.getElementById('applyFilters');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', function() {
                const floorFilter = document.getElementById('floorFilter').value;
                const typeFilter = document.getElementById('typeFilter').value;
                const statusFilter = document.getElementById('statusFilter').value;

                const rows = document.querySelectorAll('table tbody tr');

                rows.forEach(row => {
                    const floor = row.cells[1].textContent.trim();
                    const type = row.cells[2].textContent.trim();
                    const status = row.cells[4].textContent.trim();

                    const floorMatch = floorFilter === '' || floor.includes(floorFilter);
                    const typeMatch = typeFilter === '' || type.includes(typeFilter);
                    const statusMatch = statusFilter === '' || status.includes(statusFilter);

                    row.style.display = (floorMatch && typeMatch && statusMatch) ? '' : 'none';
                });
            });
        }
    });

    function editRoom(id) {
        window.location.href = "${pageContext.request.contextPath}/room/edit?id=" + id;
    }

    function deleteRoom(id) {
        if (confirm("确定要删除这个房间吗？")) {
            window.location.href = "${pageContext.request.contextPath}/room/delete?id=" + id;
        }
    }
</script>