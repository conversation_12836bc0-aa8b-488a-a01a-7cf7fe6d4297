<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="系统设置" />
    <jsp:param name="content" value="/WEB-INF/views/system/settings-content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 系统设置页面专用样式 */
        .page-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .settings-card {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            padding: 2rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .settings-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }
        
        .settings-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007bff, #0056b3);
        }
        
        .hidden-section {
            display: none;
        }
        
        .visible-section {
            display: block;
            animation: fadeInUp 0.5s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .badge-info {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }
        
        .setting-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .setting-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
            font-size: 1.5rem;
        }
        
        .setting-title {
            margin: 0;
            color: #2c3e50;
            font-weight: 600;
        }
        
        .setting-description {
            color: #6c757d;
            margin-bottom: 2rem;
            font-size: 0.95rem;
        }
        
        .required-field::after {
            content: ' *';
            color: #dc3545;
        }
        
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .password-field-container {
            position: relative;
        }
        
        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
            transition: color 0.3s ease;
        }
        
        .password-toggle:hover {
            color: #007bff;
        }
        
        .actions-container {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }
        
        .alert {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .form-check-input:checked {
            background-color: #007bff;
            border-color: #007bff;
        }
        
        .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .settings-card {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }
            
            .setting-header {
                flex-direction: column;
                text-align: center;
            }
            
            .setting-icon {
                margin-right: 0;
                margin-bottom: 1rem;
            }
            
            .actions-container {
                flex-direction: column;
            }
            
            .pagination-container {
                flex-direction: column;
                gap: 1rem;
            }
        }
    " />
    <jsp:param name="scripts" value="
        <script>
            // 系统设置页面JavaScript功能
            document.addEventListener('DOMContentLoaded', function() {
                // 初始化页面
                initializeSettings();
                
                // 绑定事件监听器
                bindEventListeners();
                
                // 加载现有设置
                loadCurrentSettings();
            });
            
            function initializeSettings() {
                // 显示第一个设置部分
                showSection('security-section');
            }
            
            function bindEventListeners() {
                // 保存按钮事件
                document.getElementById('saveSecurityBtn')?.addEventListener('click', saveSecuritySettings);
                document.getElementById('saveEmailBtn')?.addEventListener('click', saveEmailSettings);
                document.getElementById('saveBackupBtn')?.addEventListener('click', saveBackupSettings);
                document.getElementById('saveNotificationBtn')?.addEventListener('click', saveNotificationSettings);
                
                // 测试按钮事件
                document.getElementById('testEmailBtn')?.addEventListener('click', testEmailConnection);
                document.getElementById('executeBackupBtn')?.addEventListener('click', executeBackup);
                document.getElementById('browseBackupPathBtn')?.addEventListener('click', browseBackupPath);
            }
            
            function showSection(sectionId) {
                // 隐藏所有部分
                document.querySelectorAll('.settings-card').forEach(card => {
                    card.classList.remove('visible-section');
                    card.classList.add('hidden-section');
                });
                
                // 显示指定部分
                const targetSection = document.getElementById(sectionId);
                if (targetSection) {
                    targetSection.classList.remove('hidden-section');
                    targetSection.classList.add('visible-section');
                }
            }
            
            function togglePasswordVisibility(fieldName) {
                const field = document.querySelector('input[name=\"' + fieldName + '\"]');
                const toggle = field.parentNode.querySelector('.password-toggle i');
                
                if (field.type === 'password') {
                    field.type = 'text';
                    toggle.className = 'bi bi-eye-slash';
                } else {
                    field.type = 'password';
                    toggle.className = 'bi bi-eye';
                }
            }
            
            function showAlert(message, type = 'success') {
                const alertContainer = document.getElementById('alertContainer');
                const alert = document.createElement('div');
                alert.className = 'alert alert-' + type + ' alert-dismissible fade show';
                alert.innerHTML = message + '<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>';
                
                alertContainer.appendChild(alert);
                
                // 自动移除提示
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 5000);
            }
            
            function saveSecuritySettings() {
                const formData = new FormData(document.getElementById('securityForm'));
                
                fetch('${pageContext.request.contextPath}/system/settings/security', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('安全设置保存成功！', 'success');
                    } else {
                        showAlert('保存失败：' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('保存失败：网络错误', 'danger');
                });
            }
            
            function saveEmailSettings() {
                const formData = new FormData(document.getElementById('emailForm'));
                
                fetch('${pageContext.request.contextPath}/system/settings/email', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('邮件设置保存成功！', 'success');
                    } else {
                        showAlert('保存失败：' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('保存失败：网络错误', 'danger');
                });
            }
            
            function saveBackupSettings() {
                const formData = new FormData(document.getElementById('backupForm'));
                
                fetch('${pageContext.request.contextPath}/system/settings/backup', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('备份设置保存成功！', 'success');
                    } else {
                        showAlert('保存失败：' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('保存失败：网络错误', 'danger');
                });
            }
            
            function saveNotificationSettings() {
                const formData = new FormData(document.getElementById('notificationForm'));
                
                fetch('${pageContext.request.contextPath}/system/settings/notification', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('通知设置保存成功！', 'success');
                    } else {
                        showAlert('保存失败：' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('保存失败：网络错误', 'danger');
                });
            }
            
            function testEmailConnection() {
                const formData = new FormData(document.getElementById('emailForm'));
                const btn = document.getElementById('testEmailBtn');
                
                btn.disabled = true;
                btn.innerHTML = '<i class=\"bi bi-hourglass-split\"></i> 测试中...';
                
                fetch('${pageContext.request.contextPath}/system/settings/email/test', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('邮件连接测试成功！', 'success');
                    } else {
                        showAlert('连接测试失败：' + data.message, 'warning');
                    }
                })
                .catch(error => {
                    showAlert('测试失败：网络错误', 'danger');
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.innerHTML = '<i class=\"bi bi-envelope-check\"></i> 测试连接';
                });
            }
            
            function executeBackup() {
                const btn = document.getElementById('executeBackupBtn');
                
                btn.disabled = true;
                btn.innerHTML = '<i class=\"bi bi-hourglass-split\"></i> 备份中...';
                
                fetch('${pageContext.request.contextPath}/system/backup/execute', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('数据备份执行成功！', 'success');
                    } else {
                        showAlert('备份失败：' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('备份失败：网络错误', 'danger');
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.innerHTML = '<i class=\"bi bi-download\"></i> 立即备份';
                });
            }
            
            function browseBackupPath() {
                // 这里可以实现文件夹选择功能
                showAlert('文件夹选择功能需要后端支持', 'info');
            }
            
            function loadCurrentSettings() {
                // 加载当前系统设置
                fetch('${pageContext.request.contextPath}/system/settings/current')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        populateSettings(data.settings);
                    }
                })
                .catch(error => {
                    console.error('加载设置失败:', error);
                });
            }
            
            function populateSettings(settings) {
                // 填充安全设置
                if (settings.security) {
                    document.querySelector('input[name=\"minPasswordLength\"]').value = settings.security.minPasswordLength || 8;
                    document.querySelector('input[name=\"maxLoginAttempts\"]').value = settings.security.maxLoginAttempts || 5;
                    document.querySelector('input[name=\"requireUppercase\"]').checked = settings.security.requireUppercase || false;
                    document.querySelector('input[name=\"requireNumbers\"]').checked = settings.security.requireNumbers || false;
                    document.querySelector('input[name=\"requireSpecialChars\"]').checked = settings.security.requireSpecialChars || false;
                }
                
                // 填充邮件设置
                if (settings.email) {
                    document.querySelector('input[name=\"smtpServer\"]').value = settings.email.smtpServer || '';
                    document.querySelector('input[name=\"smtpPort\"]').value = settings.email.smtpPort || 587;
                    document.querySelector('input[name=\"senderEmail\"]').value = settings.email.senderEmail || '';
                }
                
                // 填充备份设置
                if (settings.backup) {
                    document.querySelector('input[name=\"autoBackup\"]').checked = settings.backup.autoBackup || false;
                    document.querySelector('select[name=\"backupFrequency\"]').value = settings.backup.backupFrequency || 'daily';
                    document.querySelector('input[name=\"backupRetention\"]').value = settings.backup.backupRetention || 30;
                    document.querySelector('input[name=\"backupPath\"]').value = settings.backup.backupPath || '';
                }
                
                // 填充通知设置
                if (settings.notification) {
                    document.querySelector('input[name=\"systemNotifications\"]').checked = settings.notification.systemNotifications !== false;
                    document.querySelector('input[name=\"emailNotifications\"]').checked = settings.notification.emailNotifications || false;
                    document.querySelector('input[name=\"notifyReservation\"]').checked = settings.notification.notifyReservation !== false;
                    document.querySelector('input[name=\"notifyMaintenance\"]').checked = settings.notification.notifyMaintenance !== false;
                    document.querySelector('input[name=\"notifySystem\"]').checked = settings.notification.notifySystem !== false;
                }
            }
        </script>
    " />
</jsp:include>