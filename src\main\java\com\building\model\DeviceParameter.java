package com.building.model;

/**
 * 设备参数实体类
 * 对应数据库中的device_parameter_history表
 */
public class DeviceParameter {
    private int id;
    private int deviceId;
    private String parameterName;
    private String parameterValue;
    private String recordTime;
    
    // 非持久化字段
    private Device device;
    
    /**
     * 默认构造函数
     */
    public DeviceParameter() {
    }
    
    /**
     * 带参数的构造函数
     * @param deviceId 设备ID
     * @param parameterName 参数名称
     * @param parameterValue 参数值
     */
    public DeviceParameter(int deviceId, String parameterName, String parameterValue) {
        this.deviceId = deviceId;
        this.parameterName = parameterName;
        this.parameterValue = parameterValue;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public int getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(int deviceId) {
        this.deviceId = deviceId;
    }
    
    public String getParameterName() {
        return parameterName;
    }
    
    public void setParameterName(String parameterName) {
        this.parameterName = parameterName;
    }
    
    public String getParameterValue() {
        return parameterValue;
    }
    
    public void setParameterValue(String parameterValue) {
        this.parameterValue = parameterValue;
    }
    
    public String getRecordTime() {
        return recordTime;
    }
    
    public void setRecordTime(String recordTime) {
        this.recordTime = recordTime;
    }
    
    public Device getDevice() {
        return device;
    }
    
    public void setDevice(Device device) {
        this.device = device;
    }
}
