<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<style>
.stats-card {
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    transition: transform 0.2s;
    margin-bottom: 20px;
}
.stats-card:hover {
    transform: translateY(-5px);
}
.stats-icon {
    font-size: 2rem;
    opacity: 0.8;
}
</style>

<div class="card stats-card ${param.bgColor} text-white">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h6 class="card-title mb-0">${param.title}</h6>
                <h2 class="mt-2 mb-0">${param.value}</h2>
            </div>
            <i class="bi ${param.icon} stats-icon"></i>
        </div>
    </div>
</div> 