<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="楼层布局" />
    <jsp:param name="content" value="/WEB-INF/views/floor/content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 楼层布局样式 */
        .floor-layout-container {
            position: relative;
            margin-bottom: 30px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.05);
        }
        .floor-layout {
            position: relative;
            width: 100%;
            height: 700px;
            background-color: #f8f9fa;
            background-image:
                linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
            background-size: 20px 20px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .floor-layout::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.7) 0%, rgba(255,255,255,0) 100%);
            pointer-events: none;
        }
        .floor-layout-header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            padding: 15px 20px;
            background: rgba(255,255,255,0.9);
            border-bottom: 1px solid rgba(0,0,0,0.05);
            z-index: 10;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .floor-layout-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 10px 20px;
            background: rgba(255,255,255,0.9);
            border-top: 1px solid rgba(0,0,0,0.05);
            z-index: 10;
            font-size: 0.8rem;
            color: #6c757d;
            text-align: center;
        }
        .floor-layout-legend {
            position: absolute;
            top: 70px;
            right: 20px;
            background: white;
            border-radius: 8px;
            padding: 10px 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            z-index: 10;
            font-size: 0.85rem;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            margin-right: 8px;
        }
        .legend-available {
            background-color: rgba(0, 123, 255, 0.2);
            border: 2px solid #007bff;
        }
        .legend-occupied {
            background-color: rgba(220, 53, 69, 0.2);
            border: 2px solid #dc3545;
        }
        .legend-maintenance {
            background-color: rgba(255, 193, 7, 0.2);
            border: 2px solid #ffc107;
        }
        .room-item {
            position: absolute;
            border: 2px solid #007bff;
            background-color: rgba(0, 123, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            z-index: 5;
        }
        .room-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background-color: #007bff;
            transition: all 0.3s ease;
        }
        .room-item:hover {
            background-color: rgba(0, 123, 255, 0.15);
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            z-index: 6;
        }
        .room-item.occupied {
            border-color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
        }
        .room-item.occupied::before {
            background-color: #dc3545;
        }
        .room-item.occupied:hover {
            background-color: rgba(220, 53, 69, 0.15);
        }
        .room-item.maintenance {
            border-color: #ffc107;
            background-color: rgba(255, 193, 7, 0.1);
        }
        .room-item.maintenance::before {
            background-color: #ffc107;
        }
        .room-item.maintenance:hover {
            background-color: rgba(255, 193, 7, 0.15);
        }
        .room-number {
            font-weight: bold;
            font-size: 1.3rem;
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .room-type {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 8px;
        }
        .room-status {
            font-size: 0.85rem;
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px dashed rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .room-devices {
            display: flex;
            gap: 5px;
            margin-top: 5px;
        }
        .device-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            color: #6c757d;
        }
        .device-icon.online {
            background-color: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }
        .device-icon.offline {
            background-color: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 2px 8px;
            border-radius: 20px;
            background-color: #e9ecef;
            color: #495057;
        }
        .status-badge.available {
            background-color: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }
        .status-badge.occupied {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }
        .status-badge.maintenance {
            background-color: rgba(255, 193, 7, 0.1);
            color: #856404;
        }
        /* 楼层选择器样式 */
        .floor-selector {
            margin-bottom: 30px;
            display: flex;
            justify-content: center;
            perspective: 1000px;
        }
        .floor-selector-inner {
            display: flex;
            background-color: #f8f9fa;
            border-radius: 50px;
            padding: 5px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transform-style: preserve-3d;
            transition: transform 0.5s ease;
        }
        .floor-selector:hover .floor-selector-inner {
            transform: rotateX(10deg);
        }
        .floor-btn {
            padding: 10px 25px;
            margin: 0 5px;
            border-radius: 30px;
            border: none;
            background-color: transparent;
            color: #495057;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .floor-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        .floor-btn:hover::before {
            transform: translateY(0);
        }
        .floor-btn.active {
            background-color: #007bff;
            color: white;
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
            transform: translateY(-2px);
        }
        /* 楼层统计卡片 */
        .floor-stats-card {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0,0,0,0.05);
            margin-bottom: 30px;
            border: none;
            transition: all 0.3s ease;
        }
        .floor-stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        .floor-stats-header {
            background: linear-gradient(to right, #f8f9fa, #ffffff);
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        .floor-stats-body {
            padding: 1.5rem;
        }
        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
        }
        .stats-item {
            text-align: center;
            flex: 1;
            padding: 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .stats-item:hover {
            background-color: rgba(0,0,0,0.02);
            transform: translateY(-3px);
        }
        .stats-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #007bff;
        }
        .stats-label {
            font-size: 0.875rem;
            color: #6c757d;
        }
        .stats-item.available .stats-value {
            color: #28a745;
        }
        .stats-item.occupied .stats-value {
            color: #dc3545;
        }
        .stats-item.maintenance .stats-value {
            color: #ffc107;
        }
        /* 房间列表样式 */
        .room-list-card {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0,0,0,0.05);
            margin-bottom: 30px;
            border: none;
        }
        .room-list-header {
            background: linear-gradient(to right, #f8f9fa, #ffffff);
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .room-list-body {
            padding: 1.5rem;
        }
        .room-list-item {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .room-list-item:hover {
            background-color: rgba(0,0,0,0.02);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        .room-list-info {
            flex: 1;
        }
        .room-list-number {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
        }
        .room-list-type {
            font-size: 0.85rem;
            color: #6c757d;
        }
        .room-list-status {
            margin-left: 1rem;
        }
    " />
    <jsp:param name="scripts" value="
        <script src='https://cdn.jsdelivr.net/npm/chart.js'></script>
        <script>
            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                // 初始化楼层统计图表
                initFloorStatsChart();

                // 楼层切换事件
                document.querySelectorAll('.floor-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        // 添加点击动画效果
                        this.classList.add('clicked');
                        setTimeout(() => {
                            this.classList.remove('clicked');
                        }, 300);

                        const floor = this.getAttribute('data-floor');
                        window.location.href = '${pageContext.request.contextPath}/floor/layout?floor=' + floor;
                    });
                });

                // 房间点击事件
                document.querySelectorAll('.room-item').forEach(room => {
                    room.addEventListener('click', function() {
                        const roomId = this.getAttribute('data-id');
                        window.location.href = '${pageContext.request.contextPath}/room/detail?id=' + roomId;
                    });
                });

                // 房间搜索功能
                const roomSearchInput = document.getElementById('roomSearchInput');
                if (roomSearchInput) {
                    roomSearchInput.addEventListener('keyup', function() {
                        const searchText = this.value.toLowerCase();
                        const roomItems = document.querySelectorAll('.room-list-item');

                        roomItems.forEach(item => {
                            const roomNumber = item.querySelector('.room-list-number').textContent.toLowerCase();
                            const roomType = item.querySelector('.room-list-type').textContent.toLowerCase();

                            if (roomNumber.includes(searchText) || roomType.includes(searchText)) {
                                item.style.display = '';
                            } else {
                                item.style.display = 'none';
                            }
                        });
                    });
                }

                // 房间状态筛选
                document.querySelectorAll('.room-filter-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        // 移除所有按钮的active类
                        document.querySelectorAll('.room-filter-btn').forEach(b => {
                            b.classList.remove('active');
                        });

                        // 添加当前按钮的active类
                        this.classList.add('active');

                        const filter = this.getAttribute('data-filter');
                        const roomItems = document.querySelectorAll('.room-item');

                        if (filter === 'all') {
                            roomItems.forEach(item => {
                                item.style.display = '';
                            });
                        } else {
                            roomItems.forEach(item => {
                                if (item.classList.contains(filter)) {
                                    item.style.display = '';
                                } else {
                                    item.style.display = 'none';
                                }
                            });
                        }
                    });
                });

                // 添加动画效果
                const roomItems = document.querySelectorAll('.room-item');
                roomItems.forEach((item, index) => {
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, 100 + index * 50);
                });

                // 统计卡片动画
                const statsItems = document.querySelectorAll('.stats-item');
                statsItems.forEach((item, index) => {
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, 100 + index * 100);
                });
            });

            // 初始化楼层统计图表
            function initFloorStatsChart() {
                const ctx = document.getElementById('floorStatsChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['可用', '占用', '维护中'],
                            datasets: [{
                                data: [
                                    parseInt(document.getElementById('availableRooms').textContent),
                                    parseInt(document.getElementById('occupiedRooms').textContent),
                                    parseInt(document.getElementById('maintenanceRooms').textContent)
                                ],
                                backgroundColor: [
                                    'rgba(40, 167, 69, 0.7)',
                                    'rgba(220, 53, 69, 0.7)',
                                    'rgba(255, 193, 7, 0.7)'
                                ],
                                borderColor: [
                                    'rgba(40, 167, 69, 1)',
                                    'rgba(220, 53, 69, 1)',
                                    'rgba(255, 193, 7, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            },
                            cutout: '70%',
                            animation: {
                                animateScale: true,
                                animateRotate: true
                            }
                        }
                    });
                }
            }
        </script>
    " />
</jsp:include>
