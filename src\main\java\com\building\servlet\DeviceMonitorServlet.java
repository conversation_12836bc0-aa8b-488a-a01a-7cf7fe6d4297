package com.building.servlet;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.dao.DeviceAlertDao;
import com.building.dao.DeviceDao;
import com.building.dao.DeviceParameterDao;
import com.building.model.Device;
import com.building.model.DeviceAlert;
import com.building.model.DeviceParameter;
import com.google.gson.Gson;

/**
 * 设备监控Servlet
 * 用于处理设备监控相关的请求
 */
@WebServlet("/device/monitor")
public class DeviceMonitorServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private DeviceDao deviceDao;
    private DeviceParameterDao parameterDao;
    private DeviceAlertDao alertDao;
    private Gson gson;
    
    @Override
    public void init() throws ServletException {
        deviceDao = new DeviceDao();
        parameterDao = new DeviceParameterDao();
        alertDao = new DeviceAlertDao();
        gson = new Gson();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取在线设备列表
        List<Device> onlineDevices = deviceDao.getOnlineDevices();
        
        // 获取未解决的告警
        List<DeviceAlert> unresolvedAlerts = alertDao.getUnresolvedAlerts();
        
        // 设置请求属性
        request.setAttribute("onlineDevices", onlineDevices);
        request.setAttribute("unresolvedAlerts", unresolvedAlerts);
        
        // 转发到设备监控页面
        request.getRequestDispatcher("/WEB-INF/views/device/monitor.jsp").forward(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return;
        }
        
        // 设置响应类型
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        // 获取请求参数
        String action = request.getParameter("action");
        
        Map<String, Object> result = new HashMap<>();
        
        if (action == null) {
            result.put("success", false);
            result.put("message", "参数不完整");
            response.getWriter().write(gson.toJson(result));
            return;
        }
        
        try {
            if ("getDeviceStatus".equals(action)) {
                // 获取设备ID
                int deviceId = Integer.parseInt(request.getParameter("deviceId"));
                
                // 获取设备信息
                Device device = deviceDao.getDeviceById(deviceId);
                if (device == null) {
                    result.put("success", false);
                    result.put("message", "设备不存在");
                } else {
                    result.put("success", true);
                    result.put("device", device);
                }
            } else if ("getDeviceParameters".equals(action)) {
                // 获取设备ID
                int deviceId = Integer.parseInt(request.getParameter("deviceId"));
                
                // 获取设备参数历史
                List<DeviceParameter> parameters = parameterDao.getLatestParametersByDeviceId(deviceId);
                
                result.put("success", true);
                result.put("parameters", parameters);
            } else if ("getDeviceAlerts".equals(action)) {
                // 获取设备ID
                int deviceId = Integer.parseInt(request.getParameter("deviceId"));
                
                // 获取设备告警
                List<DeviceAlert> alerts = alertDao.getAlertsByDeviceId(deviceId);
                
                result.put("success", true);
                result.put("alerts", alerts);
            } else if ("resolveAlert".equals(action)) {
                // 获取告警ID
                int alertId = Integer.parseInt(request.getParameter("alertId"));
                
                // 解决告警
                boolean success = alertDao.resolveAlert(alertId);
                
                result.put("success", success);
                if (success) {
                    result.put("message", "告警已解决");
                } else {
                    result.put("message", "解决告警失败");
                }
            } else {
                result.put("success", false);
                result.put("message", "不支持的操作类型");
            }
        } catch (NumberFormatException e) {
            result.put("success", false);
            result.put("message", "参数格式错误");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作异常: " + e.getMessage());
        }
        
        response.getWriter().write(gson.toJson(result));
    }
}
