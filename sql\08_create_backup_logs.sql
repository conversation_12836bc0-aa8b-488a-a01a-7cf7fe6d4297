-- 备份日志表创建脚本
-- 创建日期: 2024年6月12日
-- 描述: 用于记录系统备份的历史记录，包括备份时间、文件名、状态等信息


-- 创建备份日志表
CREATE TABLE `backup_logs` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `backup_time` DATETIME NOT NULL COMMENT '备份时间',
  `backup_file` VARCHAR(255) NOT NULL COMMENT '备份文件名',
  `backup_path` VARCHAR(255) NOT NULL COMMENT '备份文件路径',
  `backup_size` BIGINT DEFAULT NULL COMMENT '备份文件大小(字节)',
  `backup_type` VARCHAR(20) NOT NULL COMMENT '备份类型(manual/scheduled)',
  `status` VARCHAR(20) NOT NULL COMMENT '备份状态(success/failed)',
  `error_message` TEXT DEFAULT NULL COMMENT '错误信息(如果失败)',
  `duration` INT DEFAULT NULL COMMENT '备份耗时(秒)',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_backup_time` (`backup_time`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='备份日志表'; 