package com.building.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.building.model.Reservation;
import com.building.model.Room;
import com.building.model.User;
import com.building.util.DBUtil;

/**
 * 预约数据访问对象
 * 负责预约相关的数据库操作
 */
public class ReservationDao {
    
    /**
     * 获取所有预约列表
     * @return 预约列表
     */
    public List<Reservation> getAllReservations() {
        List<Reservation> reservations = new ArrayList<>();
        String sql = "SELECT r.*, " +
                    "rm.room_number, " +
                    "d.name as device_name, " +
                    "d.id as device_id, " +
                    "u.real_name " +
                    "FROM reservation r " +
                    "LEFT JOIN room rm ON r.room_id = rm.id " +
                    "LEFT JOIN device d ON r.device_id = d.id " +
                    "LEFT JOIN user u ON r.user_id = u.id " +
                    "ORDER BY r.start_time DESC";
                    
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                Reservation reservation = new Reservation();
                reservation.setId(rs.getInt("id"));
                
                // 设置房间ID（如果有）
                Object roomId = rs.getObject("room_id");
                if (roomId != null) {
                    reservation.setRoomId((Integer) roomId);
                    reservation.setRoomNumber(rs.getString("room_number"));
                }
                
                // 设置设备ID（如果有）
                Object deviceId = rs.getObject("device_id");
                if (deviceId != null) {
                    reservation.setDeviceId((Integer) deviceId);
                    reservation.setDeviceName(rs.getString("device_name"));
                }
                
                reservation.setUserId(rs.getInt("user_id"));
                reservation.setStartTime(rs.getString("start_time"));
                reservation.setEndTime(rs.getString("end_time"));
                reservation.setPurpose(rs.getString("purpose"));
                reservation.setStatus(rs.getString("status"));
                reservation.setUserName(rs.getString("real_name"));
                
                reservations.add(reservation);
            }
        } catch (SQLException e) {
            System.err.println("获取预约列表时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        return reservations;
    }
    
    /**
     * 根据ID获取预约信息
     * @param id 预约ID
     * @return 预约对象，如果不存在返回null
     */
    public Reservation getReservationById(int id) {
        String sql = "SELECT r.*, rm.room_number, u.real_name, d.name as device_name FROM reservation r " +
                    "LEFT JOIN room rm ON r.room_id = rm.id " +
                    "LEFT JOIN user u ON r.user_id = u.id " +
                    "LEFT JOIN device d ON r.device_id = d.id " +
                    "WHERE r.id = ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                Reservation reservation = new Reservation();
                reservation.setId(rs.getInt("id"));
                reservation.setRoomId(rs.getObject("room_id") != null ? rs.getInt("room_id") : null);
                reservation.setDeviceId(rs.getObject("device_id") != null ? rs.getInt("device_id") : null);
                reservation.setUserId(rs.getInt("user_id"));
                reservation.setStartTime(rs.getString("start_time"));
                reservation.setEndTime(rs.getString("end_time"));
                reservation.setPurpose(rs.getString("purpose"));
                reservation.setStatus(rs.getString("status"));
                reservation.setRoomNumber(rs.getString("room_number"));
                reservation.setUserName(rs.getString("real_name"));
                reservation.setDeviceName(rs.getString("device_name"));
                
                System.out.println("获取到预约信息：");
                System.out.println("ID: " + reservation.getId());
                System.out.println("设备ID: " + reservation.getDeviceId());
                System.out.println("房间ID: " + reservation.getRoomId());
                System.out.println("状态: " + reservation.getStatus());
                
                return reservation;
            }
        } catch (SQLException e) {
            System.err.println("获取预约信息时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * 获取用户的所有预约
     * @param userId 用户ID
     * @return 预约列表
     */
    public List<Reservation> getUserReservations(int userId) {
        List<Reservation> reservations = new ArrayList<>();
        String sql = "SELECT r.*, " +
                    "rm.room_number, " +
                    "d.name as device_name, " +
                    "d.id as device_id, " +
                    "u.real_name " +
                    "FROM reservation r " +
                    "LEFT JOIN room rm ON r.room_id = rm.id " +
                    "LEFT JOIN device d ON r.device_id = d.id " +
                    "LEFT JOIN user u ON r.user_id = u.id " +
                    "WHERE r.user_id = ? " +
                    "ORDER BY r.start_time DESC";
                    
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, userId);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                Reservation reservation = new Reservation();
                reservation.setId(rs.getInt("id"));
                
                // 设置房间ID（如果有）
                Object roomId = rs.getObject("room_id");
                if (roomId != null) {
                    reservation.setRoomId((Integer) roomId);
                    reservation.setRoomNumber(rs.getString("room_number"));
                }
                
                // 设置设备ID（如果有）
                Object deviceId = rs.getObject("device_id");
                if (deviceId != null) {
                    reservation.setDeviceId((Integer) deviceId);
                    reservation.setDeviceName(rs.getString("device_name"));
                }
                
                reservation.setUserId(rs.getInt("user_id"));
                reservation.setStartTime(rs.getString("start_time"));
                reservation.setEndTime(rs.getString("end_time"));
                reservation.setPurpose(rs.getString("purpose"));
                reservation.setStatus(rs.getString("status"));
                reservation.setUserName(rs.getString("real_name"));
                
                reservations.add(reservation);
            }
        } catch (SQLException e) {
            System.err.println("获取用户预约列表时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        return reservations;
    }
    
    /**
     * 获取房间的所有预约
     * @param roomId 房间ID
     * @return 预约列表
     */
    public List<Reservation> getReservationsByRoomId(int roomId) {
        List<Reservation> reservations = new ArrayList<>();
        String sql = "SELECT r.*, rm.room_number, d.name as device_name, u.real_name FROM reservation r " +
                    "LEFT JOIN room rm ON r.room_id = rm.id " +
                    "LEFT JOIN device d ON r.device_id = d.id " +
                    "LEFT JOIN user u ON r.user_id = u.id " +
                    "WHERE r.room_id = ? " +
                    "ORDER BY r.start_time DESC";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, roomId);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                Reservation reservation = new Reservation();
                reservation.setId(rs.getInt("id"));
                reservation.setRoomId(rs.getObject("room_id") != null ? rs.getInt("room_id") : null);
                reservation.setDeviceId(rs.getObject("device_id") != null ? rs.getInt("device_id") : null);
                reservation.setUserId(rs.getInt("user_id"));
                reservation.setStartTime(rs.getString("start_time"));
                reservation.setEndTime(rs.getString("end_time"));
                reservation.setPurpose(rs.getString("purpose"));
                reservation.setStatus(rs.getString("status"));
                reservation.setRoomNumber(rs.getString("room_number"));
                reservation.setDeviceName(rs.getString("device_name"));
                reservation.setUserName(rs.getString("real_name"));
                reservations.add(reservation);
            }
        } catch (SQLException e) {
            System.err.println("获取房间预约列表时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        return reservations;
    }
    
    /**
     * 检查房间在指定时间段内是否已被预约
     * @param roomId 房间ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 如果已被预约返回true，否则返回false
     */
    public boolean isRoomReserved(int roomId, String startTime, String endTime) {
        String sql = "SELECT COUNT(*) FROM reservation WHERE room_id = ? AND status != '已拒绝' AND status != '已取消' " +
                    "AND ((start_time <= ? AND end_time >= ?) OR " +
                    "(start_time >= ? AND end_time <= ?))";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, roomId);
            pstmt.setString(2, endTime);
            pstmt.setString(3, startTime);
            pstmt.setString(4, startTime);
            pstmt.setString(5, endTime);
            
            ResultSet rs = pstmt.executeQuery();
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * 检查设备在指定时间段内是否已被预约
     * @param deviceId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 如果已被预约返回true，否则返回false
     */
    public boolean isDeviceReserved(int deviceId, String startTime, String endTime) {
        String sql = "SELECT COUNT(*) FROM reservation WHERE device_id = ? AND status != '已拒绝' AND status != '已取消' " +
                    "AND ((start_time <= ? AND end_time >= ?) OR " +
                    "(start_time >= ? AND end_time <= ?))";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, deviceId);
            pstmt.setString(2, endTime);
            pstmt.setString(3, startTime);
            pstmt.setString(4, startTime);
            pstmt.setString(5, endTime);
            
            ResultSet rs = pstmt.executeQuery();
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * 添加预约
     * @param reservation 预约对象
     * @return 添加是否成功
     */
    public boolean addReservation(Reservation reservation) {
        String sql = "INSERT INTO reservation (room_id, device_id, user_id, start_time, end_time, purpose, status) " +
                    "VALUES (?, ?, ?, ?, ?, ?, '待审核')";
                    
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            if (reservation.getRoomId() != null) {
                stmt.setInt(1, reservation.getRoomId());
                stmt.setNull(2, java.sql.Types.INTEGER);
            } else {
                stmt.setNull(1, java.sql.Types.INTEGER);
                stmt.setInt(2, reservation.getDeviceId());
            }
            
            stmt.setInt(3, reservation.getUserId());
            stmt.setString(4, reservation.getStartTime());
            stmt.setString(5, reservation.getEndTime());
            stmt.setString(6, reservation.getPurpose());
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新预约
     * @param reservation 预约对象
     * @return 更新是否成功
     */
    public boolean updateReservation(Reservation reservation) {
        String sql = "UPDATE reservation SET user_id = ?, room_id = ?, start_time = ?, end_time = ?, " +
                    "purpose = ?, status = ?, update_time = NOW() WHERE id = ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, reservation.getUserId());
            pstmt.setInt(2, reservation.getRoomId());
            pstmt.setString(3, reservation.getStartTime());
            pstmt.setString(4, reservation.getEndTime());
            pstmt.setString(5, reservation.getPurpose());
            pstmt.setString(6, reservation.getStatus());
            pstmt.setInt(7, reservation.getId());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * 删除预约
     * @param id 预约ID
     * @return 删除是否成功
     */
    public boolean deleteReservation(int id) {
        Connection conn = null;
        try {
            conn = DBUtil.getConnection();
            conn.setAutoCommit(false); // 开启事务
            
            // 1. 获取预约信息
            Reservation reservation = getReservationById(id);
            if (reservation == null) {
                return false;
            }
            
            // 2. 如果预约状态是"已批准"，需要更新房间或设备状态为空闲
            if ("已批准".equals(reservation.getStatus())) {
                // 如果是房间预约
                if (reservation.getRoomId() != null) {
                    String updateRoomSql = "UPDATE room SET status = '空闲中' WHERE id = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(updateRoomSql)) {
                        stmt.setInt(1, reservation.getRoomId());
                        stmt.executeUpdate();
                    }
                }
                // 如果是设备预约
                if (reservation.getDeviceId() != null) {
                    String updateDeviceSql = "UPDATE device SET status = '空闲' WHERE id = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(updateDeviceSql)) {
                        stmt.setInt(1, reservation.getDeviceId());
                        stmt.executeUpdate();
                    }
                }
            }
            
            // 3. 删除预约记录
            String deleteReservationSql = "DELETE FROM reservation WHERE id = ?";
            try (PreparedStatement stmt = conn.prepareStatement(deleteReservationSql)) {
                stmt.setInt(1, id);
                if (stmt.executeUpdate() <= 0) {
                    conn.rollback();
                    return false;
                }
            }
            
            conn.commit();
            return true;
        } catch (SQLException e) {
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
            e.printStackTrace();
            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    
    /**
     * 更新预约状态
     * @param id 预约ID
     * @param status 新状态
     * @return 更新是否成功
     */
    public boolean updateReservationStatus(int id, String status) {
        Connection conn = null;
        try {
            conn = DBUtil.getConnection();
            conn.setAutoCommit(false); // 开启事务
            
            // 1. 获取预约信息
            Reservation reservation = getReservationById(id);
            if (reservation == null) {
                return false;
            }
            
            // 2. 更新预约状态
            String updateReservationSql = "UPDATE reservation SET status = ? WHERE id = ?";
            try (PreparedStatement stmt = conn.prepareStatement(updateReservationSql)) {
                stmt.setString(1, status);
                stmt.setInt(2, id);
                if (stmt.executeUpdate() <= 0) {
                    conn.rollback();
                    return false;
                }
            }
            
            // 3. 根据状态更新房间或设备状态
            if (status.equals("已批准")) {
                // 如果是房间预约
                if (reservation.getRoomId() != null) {
                    String updateRoomSql = "UPDATE room SET status = '使用中' WHERE id = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(updateRoomSql)) {
                        stmt.setInt(1, reservation.getRoomId());
                        stmt.executeUpdate();
                    }
                }
                // 如果是设备预约
                if (reservation.getDeviceId() != null) {
                    String updateDeviceSql = "UPDATE device SET status = '使用中' WHERE id = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(updateDeviceSql)) {
                        stmt.setInt(1, reservation.getDeviceId());
                        stmt.executeUpdate();
                    }
                }
            } else if (status.equals("已拒绝") || status.equals("已取消") || status.equals("已完成")) {
                // 如果是房间预约
                if (reservation.getRoomId() != null) {
                    String updateRoomSql = "UPDATE room SET status = '空闲' WHERE id = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(updateRoomSql)) {
                        stmt.setInt(1, reservation.getRoomId());
                        stmt.executeUpdate();
                    }
                }
                // 如果是设备预约
                if (reservation.getDeviceId() != null) {
                    String updateDeviceSql = "UPDATE device SET status = '空闲' WHERE id = ?";
                    try (PreparedStatement stmt = conn.prepareStatement(updateDeviceSql)) {
                        stmt.setInt(1, reservation.getDeviceId());
                        stmt.executeUpdate();
                    }
                }
            }
            
            conn.commit();
            return true;
        } catch (SQLException e) {
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
            e.printStackTrace();
            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    
    /**
     * 验证状态值是否有效
     * @param status 状态值
     * @return 是否有效
     */
    private boolean isValidStatus(String status) {
        return status.equals("待审核") || 
               status.equals("已批准") || 
               status.equals("已拒绝") || 
               status.equals("已完成") || 
               status.equals("已取消");
    }
    
    /**
     * 获取预约统计信息
     * @return 包含总数、待审核、已批准、已拒绝、已完成、已取消数量的数组
     */
    public int[] getReservationStatistics() {
        int[] stats = new int[6]; // 总数、待审核、已批准、已拒绝、已完成、已取消
        String sql = "SELECT status, COUNT(*) as count FROM reservation GROUP BY status";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String status = rs.getString("status");
                int count = rs.getInt("count");
                
                switch (status) {
                    case "待审核":
                        stats[1] = count;
                        break;
                    case "已批准":
                        stats[2] = count;
                        break;
                    case "已拒绝":
                        stats[3] = count;
                        break;
                    case "已完成":
                        stats[4] = count;
                        break;
                    case "已取消":
                        stats[5] = count;
                        break;
                }
                stats[0] += count; // 总数
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return stats;
    }
    
    /**
     * 将ResultSet映射到Reservation对象
     * @param rs ResultSet对象
     * @return Reservation对象
     * @throws SQLException SQL异常
     */
    private Reservation mapResultSetToReservation(ResultSet rs) throws SQLException {
        Reservation reservation = new Reservation();
        reservation.setId(rs.getInt("id"));
        reservation.setUserId(rs.getInt("user_id"));
        reservation.setRoomId(rs.getInt("room_id"));
        reservation.setStartTime(rs.getString("start_time"));
        reservation.setEndTime(rs.getString("end_time"));
        reservation.setPurpose(rs.getString("purpose"));
        reservation.setStatus(rs.getString("status"));
        reservation.setCreateTime(rs.getString("create_time"));
        reservation.setUpdateTime(rs.getString("update_time"));
        
        // 获取关联的用户和房间信息
        UserDao userDao = new UserDao();
        RoomDao roomDao = new RoomDao();
        
        User user = userDao.getUserById(reservation.getUserId());
        Room room = roomDao.getRoomById(reservation.getRoomId());
        
        reservation.setUser(user);
        reservation.setRoom(room);
        
        return reservation;
    }

    public boolean approveReservation(int id) {
        Connection conn = null;
        try {
            conn = DBUtil.getConnection();
            conn.setAutoCommit(false);  // 开启事务
            
            // 1. 更新预约状态
            String sql = "UPDATE reservation SET status = '已批准' WHERE id = ?";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setInt(1, id);
                int rows = stmt.executeUpdate();
                System.out.println("更新预约状态影响行数: " + rows);
                if (rows <= 0) {
                    conn.rollback();
                    return false;
                }
            }
            
            // 2. 获取预约的房间ID和设备ID
            String getReservationSql = "SELECT room_id, device_id FROM reservation WHERE id = ?";
            Integer roomId = null;
            Integer deviceId = null;
            try (PreparedStatement stmt = conn.prepareStatement(getReservationSql)) {
                stmt.setInt(1, id);
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    if (rs.getObject("room_id") != null) {
                        roomId = rs.getInt("room_id");
                        System.out.println("获取到房间ID: " + roomId);
                    }
                    if (rs.getObject("device_id") != null) {
                        deviceId = rs.getInt("device_id");
                        System.out.println("获取到设备ID: " + deviceId);
                    }
                } else {
                    System.out.println("未找到预约记录");
                    conn.rollback();
                    return false;
                }
            }
            
            // 3. 如果是房间预约，更新房间状态
            if (roomId != null) {
                String updateRoomSql = "UPDATE room SET status = '使用中' WHERE id = ?";
                try (PreparedStatement stmt = conn.prepareStatement(updateRoomSql)) {
                    stmt.setInt(1, roomId);
                    int rows = stmt.executeUpdate();
                    System.out.println("更新房间状态影响行数: " + rows);
                    if (rows <= 0) {
                        conn.rollback();
                        return false;
                    }
                }
            }
            
            // 4. 如果是设备预约，更新设备状态
            if (deviceId != null) {
                String updateDeviceSql = "UPDATE device SET status = '使用中' WHERE id = ?";
                try (PreparedStatement stmt = conn.prepareStatement(updateDeviceSql)) {
                    stmt.setInt(1, deviceId);
                    int rows = stmt.executeUpdate();
                    System.out.println("更新设备状态影响行数: " + rows);
                    if (rows <= 0) {
                        conn.rollback();
                        return false;
                    }
                }
            }
            
            conn.commit();  // 提交事务
            System.out.println("事务提交成功");
            return true;
            
        } catch (SQLException e) {
            System.err.println("处理预约批准时发生错误: " + e.getMessage());
            e.printStackTrace();
            if (conn != null) {
                try {
                    conn.rollback();
                    System.out.println("事务回滚完成");
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                    System.out.println("数据库连接已关闭");
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public boolean rejectReservation(int id) {
        String sql = "UPDATE reservation SET status = '已拒绝' WHERE id = ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, id);
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 结束预约，释放设备
     * @param id 预约ID
     * @return 是否成功
     */
    public boolean endReservation(int id) {
        Connection conn = null;
        try {
            conn = DBUtil.getConnection();
            conn.setAutoCommit(false);  // 开启事务
            
            // 1. 更新预约状态
            String sql = "UPDATE reservation SET status = '已完成' WHERE id = ?";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setInt(1, id);
                int rows = stmt.executeUpdate();
                System.out.println("更新预约状态影响行数: " + rows);
                if (rows <= 0) {
                    conn.rollback();
                    return false;
                }
            }
            
            // 2. 获取预约的设备ID
            String getDeviceIdSql = "SELECT device_id FROM reservation WHERE id = ?";
            int deviceId;
            try (PreparedStatement stmt = conn.prepareStatement(getDeviceIdSql)) {
                stmt.setInt(1, id);
                ResultSet rs = stmt.executeQuery();
                if (!rs.next() || rs.getObject("device_id") == null) {
                    System.out.println("未找到设备ID或设备ID为空");
                    conn.rollback();
                    return false;
                }
                deviceId = rs.getInt("device_id");
                System.out.println("获取到设备ID: " + deviceId);
            }
            
            // 3. 更新设备状态为空闲
            String updateDeviceSql = "UPDATE device SET status = '空闲' WHERE id = ?";
            try (PreparedStatement stmt = conn.prepareStatement(updateDeviceSql)) {
                stmt.setInt(1, deviceId);
                int rows = stmt.executeUpdate();
                System.out.println("更新设备状态影响行数: " + rows);
                if (rows <= 0) {
                    conn.rollback();
                    return false;
                }
            }
            
            conn.commit();  // 提交事务
            System.out.println("事务提交成功");
            return true;
            
        } catch (SQLException e) {
            System.err.println("处理预约结束时发生错误: " + e.getMessage());
            e.printStackTrace();
            if (conn != null) {
                try {
                    conn.rollback();
                    System.out.println("事务回滚完成");
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                    System.out.println("数据库连接已关闭");
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
