
-- 创建设备参数历史表
CREATE TABLE IF NOT EXISTS device_parameter_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    device_id INT NOT NULL COMMENT '设备ID',
    parameter_name VARCHAR(50) NOT NULL COMMENT '参数名称',
    parameter_value VARCHAR(255) NOT NULL COMMENT '参数值',
    record_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    FOREIGN KEY (device_id) REFERENCES device(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备参数历史表';

-- 创建设备告警表
CREATE TABLE IF NOT EXISTS device_alert (
    id INT PRIMARY KEY AUTO_INCREMENT,
    device_id INT NOT NULL COMMENT '设备ID',
    alert_type VARCHAR(50) NOT NULL COMMENT '告警类型',
    alert_level TINYINT NOT NULL COMMENT '告警级别：1-低 2-中 3-高',
    alert_message TEXT NOT NULL COMMENT '告警信息',
    is_resolved TINYINT DEFAULT 0 COMMENT '是否已解决：0-未解决 1-已解决',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    resolve_time TIMESTAMP NULL COMMENT '解决时间',
    FOREIGN KEY (device_id) REFERENCES device(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备告警表';

-- 创建设备维护记录表
CREATE TABLE IF NOT EXISTS device_maintenance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    device_id INT NOT NULL COMMENT '设备ID',
    maintenance_type VARCHAR(50) NOT NULL COMMENT '维护类型',
    maintenance_desc TEXT NOT NULL COMMENT '维护描述',
    maintenance_result VARCHAR(50) NOT NULL COMMENT '维护结果',
    maintenance_person VARCHAR(50) NOT NULL COMMENT '维护人员',
    maintenance_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '维护时间',
    FOREIGN KEY (device_id) REFERENCES device(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备维护记录表';
