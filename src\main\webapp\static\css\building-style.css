/* 产教融合大楼数字化管理系统通用样式 */

/* 基础设置 */
:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --secondary-light: #34495e;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f5f7fa;
    --dark-color: #212529;
    --gray-color: #6c757d;
    --light-gray: #e9ecef;
    --font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
    --border-radius: 8px;
    --box-shadow: 0 2px 12px rgba(0,0,0,.05);
    --transition: all 0.3s ease;
}

html, body {
    height: 100%;
    margin: 0;
    font-family: var(--font-family);
}

body {
    padding-top: 56px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--light-color);
    color: var(--dark-color);
}

/* 布局组件 */
.navbar {
    background-color: var(--secondary-color);
    box-shadow: 0 2px 10px rgba(0,0,0,.1);
}

.navbar-brand {
    font-weight: 600;
    color: white !important;
}

.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 20px 0;
    width: 200px;
    background: white;
    box-shadow: 0 0 10px rgba(0,0,0,.05);
    overflow-y: auto;
    transition: var(--transition);
}

.sidebar .nav-link {
    padding: 12px 20px;
    color: var(--dark-color);
    border-left: 3px solid transparent;
    transition: var(--transition);
    font-weight: 500;
}

.sidebar .nav-link:hover {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
}

.sidebar .nav-link.active {
    background-color: rgba(52, 152, 219, 0.15);
    color: var(--primary-color);
    border-left: 3px solid var(--primary-color);
}

.sidebar .nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.main-content {
    margin-left: 220px;
    padding: 20px;
    flex: 1;
    transition: var(--transition);
}

.page-title {
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--secondary-color);
    border-bottom: 1px solid #edf2f7;
    padding-bottom: 15px;
}

/* 卡片样式 */
.content-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 25px;
    margin-bottom: 20px;
    position: relative;
    transition: var(--transition);
}

.content-card:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,.08);
}

/* 设置卡片 */
.settings-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 25px;
    margin-bottom: 20px;
    position: relative;
}

.setting-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.setting-icon {
    background-color: rgba(52, 152, 219, 0.15);
    color: var(--primary-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.setting-title {
    font-weight: 600;
    color: var(--secondary-color);
    margin: 0;
}

.setting-description {
    color: var(--gray-color);
    margin-bottom: 20px;
}

/* 数据表格 */
.data-table {
    width: 100%;
    margin-bottom: 1rem;
    background-color: transparent;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid var(--light-gray);
}

.data-table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid var(--light-gray);
    background-color: rgba(52, 152, 219, 0.05);
    color: var(--secondary-color);
    font-weight: 600;
}

.data-table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* 表单样式 */
.form-control {
    max-width: 400px;
    border-radius: 4px;
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--secondary-color);
}

.form-text {
    font-size: 0.85rem;
    color: var(--gray-color);
}

.required-field::after {
    content: " *";
    color: var(--danger-color);
}

.actions-container {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.pagination-container {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

/* 按钮样式 */
.btn {
    border-radius: 4px;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* 通知和提示 */
.badge-info {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 0.8rem;
    background-color: var(--info-color);
}

.alert {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* 密码输入框 */
.password-toggle {
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
}

.password-field-container {
    position: relative;
    max-width: 400px;
}

/* 加载状态样式 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    border-radius: var(--border-radius);
}

/* 响应式布局 */
@media (max-width: 992px) {
    .sidebar {
        width: 180px;
    }
    .main-content {
        margin-left: 200px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 60px;
    }
    .sidebar .nav-link {
        padding: 12px 10px;
        text-align: center;
    }
    .sidebar .nav-link i {
        margin-right: 0;
        font-size: 1.2rem;
    }
    .sidebar .nav-link span {
        display: none;
    }
    .main-content {
        margin-left: 80px;
    }
    .form-control, .password-field-container {
        max-width: 100%;
    }
}

/* 打印样式 */
@media print {
    .sidebar, .navbar {
        display: none;
    }
    .main-content {
        margin-left: 0;
        padding: 0;
    }
    body {
        padding-top: 0;
    }
}