package com.building.dao.impl;

import com.building.dao.SystemSettingsDao;
import com.building.model.SystemSettings;
import com.building.util.DBUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import java.sql.*;
import java.util.Date;

/**
 * SystemSettingsDao的实现类
 * 从数据库读取并更新系统设置
 */
@Repository
public class SystemSettingsDaoImpl implements SystemSettingsDao {
    private static final Logger logger = LoggerFactory.getLogger(SystemSettingsDaoImpl.class);
    
    @Override
    public SystemSettings getSettings() {
        logger.info("从数据库获取系统设置");
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        SystemSettings settings = new SystemSettings();
        
        try {
            conn = DBUtil.getConnection();
            String sql = "SELECT * FROM system_settings ORDER BY id DESC LIMIT 1";
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            
            if (rs.next()) {
                settings.setMinPasswordLength(rs.getInt("min_password_length"));
                settings.setRequireUppercase(rs.getBoolean("require_uppercase"));
                settings.setRequireNumbers(rs.getBoolean("require_numbers"));
                settings.setRequireSpecialChars(rs.getBoolean("require_special_chars"));
                settings.setMaxLoginAttempts(rs.getInt("max_login_attempts"));
                
                settings.setSmtpServer(rs.getString("smtp_server"));
                settings.setSmtpPort(rs.getInt("smtp_port"));
                settings.setSenderEmail(rs.getString("sender_email"));
                settings.setEmailPassword(rs.getString("email_password"));
                
                settings.setAutoBackup(rs.getBoolean("auto_backup"));
                settings.setBackupFrequency(rs.getString("backup_frequency"));
                settings.setBackupRetention(rs.getInt("backup_retention"));
                settings.setBackupPath(rs.getString("backup_path"));
                
                Timestamp lastBackupTime = rs.getTimestamp("last_backup_time");
                if (lastBackupTime != null) {
                    settings.setLastBackupTime(new Date(lastBackupTime.getTime()));
                }
                
                settings.setSystemNotifications(rs.getBoolean("system_notifications"));
                settings.setEmailNotifications(rs.getBoolean("email_notifications"));
                settings.setNotifyReservation(rs.getBoolean("notify_reservation"));
                settings.setNotifyMaintenance(rs.getBoolean("notify_maintenance"));
                settings.setNotifySystem(rs.getBoolean("notify_system"));
                
                Timestamp createTime = rs.getTimestamp("create_time");
                if (createTime != null) {
                    settings.setCreateTime(new Date(createTime.getTime()));
                }
                
                Timestamp updateTime = rs.getTimestamp("update_time");
                if (updateTime != null) {
                    settings.setUpdateTime(new Date(updateTime.getTime()));
                }
            } else {
                logger.warn("数据库中没有系统设置记录，将返回默认设置");
                initializeDefaultSettings();
                return getSettings(); // 递归调用获取初始化后的设置
            }
            
            return settings;
        } catch (SQLException e) {
            logger.error("获取系统设置失败", e);
            return createDefaultSettings(); // 出错时返回默认设置
        } finally {
            closeResources(conn, stmt, rs);
        }
    }
    
    @Override
    public boolean updateSettings(SystemSettings settings) {
        logger.info("更新系统设置到数据库");
        Connection conn = null;
        PreparedStatement stmt = null;
        
        try {
            conn = DBUtil.getConnection();
            
            // 检查是否有记录存在
            String checkSql = "SELECT COUNT(*) FROM system_settings";
            PreparedStatement checkStmt = conn.prepareStatement(checkSql);
            ResultSet checkRs = checkStmt.executeQuery();
            checkRs.next();
            int count = checkRs.getInt(1);
            checkRs.close();
            checkStmt.close();
            
            if (count == 0) {
                // 如果没有记录，执行插入
                return insertSettings(conn, settings);
            } else {
                // 如果有记录，执行更新
                return updateExistingSettings(conn, settings);
            }
        } catch (SQLException e) {
            logger.error("更新系统设置失败", e);
            return false;
        } finally {
            closeResources(conn, stmt, null);
        }
    }
    
    /**
     * 插入新的系统设置记录
     */
    private boolean insertSettings(Connection conn, SystemSettings settings) throws SQLException {
        String sql = "INSERT INTO system_settings (" +
                "min_password_length, require_uppercase, require_numbers, require_special_chars, max_login_attempts, " +
                "smtp_server, smtp_port, sender_email, email_password, " +
                "auto_backup, backup_frequency, backup_retention, backup_path, last_backup_time, " +
                "system_notifications, email_notifications, notify_reservation, notify_maintenance, notify_system" +
                ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        PreparedStatement stmt = conn.prepareStatement(sql);
        int index = 1;
        
        // 安全设置
        stmt.setInt(index++, settings.getMinPasswordLength());
        stmt.setBoolean(index++, settings.isRequireUppercase());
        stmt.setBoolean(index++, settings.isRequireNumbers());
        stmt.setBoolean(index++, settings.isRequireSpecialChars());
        stmt.setInt(index++, settings.getMaxLoginAttempts());
        
        // 邮件设置
        stmt.setString(index++, settings.getSmtpServer());
        stmt.setInt(index++, settings.getSmtpPort());
        stmt.setString(index++, settings.getSenderEmail());
        stmt.setString(index++, settings.getEmailPassword());
        
        // 备份设置
        stmt.setBoolean(index++, settings.isAutoBackup());
        stmt.setString(index++, settings.getBackupFrequency());
        stmt.setInt(index++, settings.getBackupRetention());
        stmt.setString(index++, settings.getBackupPath());
        
        // 上次备份时间
        if (settings.getLastBackupTime() != null) {
            stmt.setTimestamp(index++, new Timestamp(settings.getLastBackupTime().getTime()));
        } else {
            stmt.setNull(index++, Types.TIMESTAMP);
        }
        
        // 通知设置
        stmt.setBoolean(index++, settings.isSystemNotifications());
        stmt.setBoolean(index++, settings.isEmailNotifications());
        stmt.setBoolean(index++, settings.isNotifyReservation());
        stmt.setBoolean(index++, settings.isNotifyMaintenance());
        stmt.setBoolean(index++, settings.isNotifySystem());
        
        int result = stmt.executeUpdate();
        stmt.close();
        
        return result > 0;
    }
    
    /**
     * 更新现有的系统设置记录
     */
    private boolean updateExistingSettings(Connection conn, SystemSettings settings) throws SQLException {
        String sql = "UPDATE system_settings SET " +
                "min_password_length = ?, require_uppercase = ?, require_numbers = ?, require_special_chars = ?, max_login_attempts = ?, " +
                "smtp_server = ?, smtp_port = ?, sender_email = ?, " +
                "auto_backup = ?, backup_frequency = ?, backup_retention = ?, backup_path = ?, " +
                "system_notifications = ?, email_notifications = ?, notify_reservation = ?, notify_maintenance = ?, notify_system = ? ";
        
        // 是否需要更新密码
        if (settings.getEmailPassword() != null && !settings.getEmailPassword().trim().isEmpty()) {
            sql += ", email_password = ? ";
        }
        
        // 是否需要更新上次备份时间
        if (settings.getLastBackupTime() != null) {
            sql += ", last_backup_time = ? ";
        }
        
        // 限制为第一条记录（通常只有一条记录）
        sql += "ORDER BY id DESC LIMIT 1";
        
        PreparedStatement stmt = conn.prepareStatement(sql);
        int index = 1;
        
        // 安全设置
        stmt.setInt(index++, settings.getMinPasswordLength());
        stmt.setBoolean(index++, settings.isRequireUppercase());
        stmt.setBoolean(index++, settings.isRequireNumbers());
        stmt.setBoolean(index++, settings.isRequireSpecialChars());
        stmt.setInt(index++, settings.getMaxLoginAttempts());
        
        // 邮件设置
        stmt.setString(index++, settings.getSmtpServer());
        stmt.setInt(index++, settings.getSmtpPort());
        stmt.setString(index++, settings.getSenderEmail());
        
        // 备份设置
        stmt.setBoolean(index++, settings.isAutoBackup());
        stmt.setString(index++, settings.getBackupFrequency());
        stmt.setInt(index++, settings.getBackupRetention());
        stmt.setString(index++, settings.getBackupPath());
        
        // 通知设置
        stmt.setBoolean(index++, settings.isSystemNotifications());
        stmt.setBoolean(index++, settings.isEmailNotifications());
        stmt.setBoolean(index++, settings.isNotifyReservation());
        stmt.setBoolean(index++, settings.isNotifyMaintenance());
        stmt.setBoolean(index++, settings.isNotifySystem());
        
        // 邮箱密码（如果有更新）
        if (settings.getEmailPassword() != null && !settings.getEmailPassword().trim().isEmpty()) {
            stmt.setString(index++, settings.getEmailPassword());
        }
        
        // 上次备份时间（如果有更新）
        if (settings.getLastBackupTime() != null) {
            stmt.setTimestamp(index++, new Timestamp(settings.getLastBackupTime().getTime()));
        }
        
        int result = stmt.executeUpdate();
        stmt.close();
        
        return result > 0;
    }
    
    /**
     * 初始化默认设置到数据库
     */
    private void initializeDefaultSettings() {
        logger.info("初始化默认系统设置到数据库");
        SystemSettings defaultSettings = createDefaultSettings();
        updateSettings(defaultSettings);
    }
    
    /**
     * 创建默认设置对象
     */
    private SystemSettings createDefaultSettings() {
        SystemSettings settings = new SystemSettings();
        
        // 安全设置
        settings.setMinPasswordLength(8);
        settings.setRequireUppercase(true);
        settings.setRequireNumbers(true);
        settings.setRequireSpecialChars(false);
        settings.setMaxLoginAttempts(5);
        
        // 邮件设置
        settings.setSmtpPort(587);
        
        // 备份设置
        settings.setBackupFrequency("daily");
        settings.setBackupRetention(30);
        
        // 通知设置
        settings.setSystemNotifications(true);
        settings.setNotifyReservation(true);
        settings.setNotifyMaintenance(true);
        settings.setNotifySystem(true);
        
        // 时间戳
        Date now = new Date();
        settings.setCreateTime(now);
        settings.setUpdateTime(now);
        
        return settings;
    }
    
    /**
     * 关闭数据库资源
     */
    private void closeResources(Connection conn, Statement stmt, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                logger.error("关闭ResultSet失败", e);
            }
        }
        
        if (stmt != null) {
            try {
                stmt.close();
            } catch (SQLException e) {
                logger.error("关闭Statement失败", e);
            }
        }
        
        if (conn != null) {
            DBUtil.closeConnection(conn);
        }
    }
} 