package com.building.dao;

import com.building.model.DeviceAlert;
import com.building.util.DBUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备告警数据访问对象
 * 用于处理设备告警相关的数据库操作
 */
public class DeviceAlertDao {
    
    /**
     * 添加设备告警
     * @param alert 设备告警对象
     * @return 是否添加成功
     */
    public boolean addAlert(DeviceAlert alert) {
        String sql = "INSERT INTO device_alert (device_id, alert_type, alert_level, alert_message, is_resolved) " +
                    "VALUES (?, ?, ?, ?, ?)";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, alert.getDeviceId());
            pstmt.setString(2, alert.getAlertType());
            pstmt.setInt(3, alert.getAlertLevel());
            pstmt.setString(4, alert.getAlertMessage());
            pstmt.setInt(5, alert.getIsResolved());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新告警状态为已解决
     * @param alertId 告警ID
     * @return 是否更新成功
     */
    public boolean resolveAlert(int alertId) {
        String sql = "UPDATE device_alert SET is_resolved = 1, resolve_time = NOW() WHERE id = ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, alertId);
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 获取所有未解决的告警
     * @return 未解决的告警列表
     */
    public List<DeviceAlert> getUnresolvedAlerts() {
        List<DeviceAlert> alerts = new ArrayList<>();
        String sql = "SELECT * FROM device_alert WHERE is_resolved = 0 ORDER BY alert_level DESC, create_time DESC";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                DeviceAlert alert = mapResultSetToAlert(rs);
                alerts.add(alert);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return alerts;
    }
    
    /**
     * 根据设备ID获取告警列表
     * @param deviceId 设备ID
     * @return 告警列表
     */
    public List<DeviceAlert> getAlertsByDeviceId(int deviceId) {
        List<DeviceAlert> alerts = new ArrayList<>();
        String sql = "SELECT * FROM device_alert WHERE device_id = ? ORDER BY create_time DESC";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, deviceId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    DeviceAlert alert = mapResultSetToAlert(rs);
                    alerts.add(alert);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return alerts;
    }
    
    /**
     * 获取所有告警
     * @return 告警列表
     */
    public List<DeviceAlert> getAllAlerts() {
        List<DeviceAlert> alerts = new ArrayList<>();
        String sql = "SELECT * FROM device_alert ORDER BY create_time DESC";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                DeviceAlert alert = mapResultSetToAlert(rs);
                alerts.add(alert);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return alerts;
    }
    
    /**
     * 将ResultSet映射为DeviceAlert对象
     * @param rs ResultSet对象
     * @return DeviceAlert对象
     * @throws SQLException 如果发生SQL异常
     */
    private DeviceAlert mapResultSetToAlert(ResultSet rs) throws SQLException {
        DeviceAlert alert = new DeviceAlert();
        alert.setId(rs.getInt("id"));
        alert.setDeviceId(rs.getInt("device_id"));
        alert.setAlertType(rs.getString("alert_type"));
        alert.setAlertLevel(rs.getInt("alert_level"));
        alert.setAlertMessage(rs.getString("alert_message"));
        alert.setIsResolved(rs.getInt("is_resolved"));
        alert.setCreateTime(rs.getString("create_time"));
        alert.setResolveTime(rs.getString("resolve_time"));
        return alert;
    }
}
