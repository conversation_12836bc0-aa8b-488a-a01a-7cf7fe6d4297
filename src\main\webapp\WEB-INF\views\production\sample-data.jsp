<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ page import="java.util.*" %>
<%@ page import="com.building.model.ProductionLine" %>

<%
    // 创建示例产线数据
    List<ProductionLine> sampleProductionLines = new ArrayList<>();
    
    ProductionLine line1 = new ProductionLine();
    line1.setId(1);
    line1.setName("智能制造产线A");
    line1.setStatus("运行中");
    line1.setLastUpdated("2024-01-15 14:30:25");
    sampleProductionLines.add(line1);
    
    ProductionLine line2 = new ProductionLine();
    line2.setId(2);
    line2.setName("自动化装配线B");
    line2.setStatus("维护中");
    line2.setLastUpdated("2024-01-15 13:45:10");
    sampleProductionLines.add(line2);
    
    ProductionLine line3 = new ProductionLine();
    line3.setId(3);
    line3.setName("精密加工产线C");
    line3.setStatus("运行中");
    line3.setLastUpdated("2024-01-15 15:20:18");
    sampleProductionLines.add(line3);
    
    ProductionLine line4 = new ProductionLine();
    line4.setId(4);
    line4.setName("质量检测线D");
    line4.setStatus("停止");
    line4.setLastUpdated("2024-01-15 12:15:30");
    sampleProductionLines.add(line4);
    
    ProductionLine line5 = new ProductionLine();
    line5.setId(5);
    line5.setName("包装生产线E");
    line5.setStatus("运行中");
    line5.setLastUpdated("2024-01-15 16:05:42");
    sampleProductionLines.add(line5);
    
    ProductionLine line6 = new ProductionLine();
    line6.setId(6);
    line6.setName("机器人焊接线F");
    line6.setStatus("维护中");
    line6.setLastUpdated("2024-01-15 11:30:55");
    sampleProductionLines.add(line6);
    
    ProductionLine line7 = new ProductionLine();
    line7.setId(7);
    line7.setName("数控加工中心G");
    line7.setStatus("运行中");
    line7.setLastUpdated("2024-01-15 14:50:12");
    sampleProductionLines.add(line7);
    
    ProductionLine line8 = new ProductionLine();
    line8.setId(8);
    line8.setName("表面处理线H");
    line8.setStatus("停止");
    line8.setLastUpdated("2024-01-15 10:25:08");
    sampleProductionLines.add(line8);
    
    // 将示例数据存储到request scope
    request.setAttribute("sampleProductionLines", sampleProductionLines);
    
    // 如果没有传入productionLines数据，则使用示例数据
    if (request.getAttribute("productionLines") == null) {
        request.setAttribute("productionLines", sampleProductionLines);
    }
%> 