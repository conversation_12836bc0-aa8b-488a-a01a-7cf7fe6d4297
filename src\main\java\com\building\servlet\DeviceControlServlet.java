package com.building.servlet;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.dao.DeviceDao;
import com.building.model.Device;
import com.google.gson.Gson;

/**
 * 设备控制Servlet
 * 用于处理设备控制请求
 */
@WebServlet("/device/control")
public class DeviceControlServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private DeviceDao deviceDao;
    private Gson gson;
    
    @Override
    public void init() throws ServletException {
        deviceDao = new DeviceDao();
        gson = new Gson();
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return;
        }
        
        // 设置响应类型
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        // 获取请求参数
        String deviceIdParam = request.getParameter("deviceId");
        String action = request.getParameter("action");
        
        Map<String, Object> result = new HashMap<>();
        
        if (deviceIdParam == null || action == null) {
            result.put("success", false);
            result.put("message", "参数不完整");
            response.getWriter().write(gson.toJson(result));
            return;
        }
        
        try {
            int deviceId = Integer.parseInt(deviceIdParam);
            
            // 获取设备信息
            Device device = deviceDao.getDeviceById(deviceId);
            if (device == null) {
                result.put("success", false);
                result.put("message", "设备不存在");
                response.getWriter().write(gson.toJson(result));
                return;
            }
            
            // 根据操作类型更新设备状态
            boolean success = false;
            if ("on".equals(action)) {
                success = deviceDao.updateDeviceStatus(deviceId, "使用中");
            } else if ("off".equals(action)) {
                success = deviceDao.updateDeviceStatus(deviceId, "空闲");
            } else if ("maintain".equals(action)) {
                success = deviceDao.updateDeviceStatus(deviceId, "维护中");
            } else {
                result.put("success", false);
                result.put("message", "不支持的操作类型");
                response.getWriter().write(gson.toJson(result));
                return;
            }
            
            if (success) {
                result.put("success", true);
                result.put("message", "操作成功");
            } else {
                result.put("success", false);
                result.put("message", "操作失败");
            }
            
        } catch (NumberFormatException e) {
            result.put("success", false);
            result.put("message", "设备ID格式错误");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作异常: " + e.getMessage());
        }
        
        response.getWriter().write(gson.toJson(result));
    }
}
