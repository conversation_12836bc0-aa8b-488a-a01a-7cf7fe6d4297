/* 产线管理页面样式 */

/* 统计卡片样式 */
.stats-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border-radius: 12px;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.stats-icon {
    font-size: 2.5rem;
}

.stats-icon-container {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* 表格样式 */
.table th {
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table td {
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 徽章样式 */
.badge {
    font-size: 0.8rem;
    padding: 0.6rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.badge i {
    margin-right: 0.3rem;
}

/* 按钮样式 */
.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.875rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.btn-sm:hover {
    transform: translateY(-1px);
}

.btn-outline-primary:hover {
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-outline-danger:hover {
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

/* 模态框样式 */
.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 8px 8px 0 0;
    padding: 1.5rem;
}

.modal-header .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

.modal-header .btn-close:hover {
    opacity: 1;
}

.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1.5rem 2rem;
    border-top: 1px solid #e9ecef;
}

/* 表单样式 */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 卡片样式 */
.card {
    border-radius: 12px;
    border: none;
    transition: box-shadow 0.2s ease;
}

.card-header {
    border-radius: 12px 12px 0 0;
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
}

.card-title {
    color: #495057;
    font-weight: 700;
}

/* 页面标题样式 */
.h2 {
    color: #495057;
    font-weight: 700;
    margin-bottom: 0;
}

.h2 i {
    color: #007bff;
}

/* 工具栏样式 */
.btn-toolbar .btn-group {
    margin-right: 0.5rem;
}

.btn-toolbar .btn-group:last-child {
    margin-right: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .btn-toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn-group {
        margin-bottom: 0.5rem;
        width: 100%;
    }
    
    .btn-group .btn {
        flex: 1;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
}

/* 加载动画 */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* 提示框样式 */
.alert {
    border-radius: 8px;
    border: none;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.alert i {
    margin-right: 0.5rem;
}

/* 数据表格分页样式 */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 6px;
    margin: 0 2px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #007bff;
    border-color: #007bff;
    color: white !important;
}

/* 搜索框样式 */
.dataTables_wrapper .dataTables_filter input {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
}

/* 表格信息样式 */
.dataTables_wrapper .dataTables_info {
    color: #6c757d;
    font-size: 0.875rem;
}

/* 表格长度选择样式 */
.dataTables_wrapper .dataTables_length select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.25rem 0.5rem;
} 