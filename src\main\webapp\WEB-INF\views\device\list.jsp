<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="设备管理" />
    <jsp:param name="content" value="/WEB-INF/views/device/content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 卡片样式 */
        .card {
            border-radius: 15px;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        .card:hover {
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        /* 设备卡片样式 */
        .device-card {
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s, box-shadow 0.3s;
            margin-bottom: 20px;
            border: none;
        }
        .device-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.12);
        }
        .device-icon {
            font-size: 2.5rem;
            opacity: 0.9;
        }

        /* 表格样式 */
        .table th {
            font-weight: 600;
            cursor: pointer;
            position: relative;
        }
        .table th:hover {
            background-color: rgba(0,0,0,0.03);
        }
        .table th.sort-asc::after {
            content: '▲';
            position: absolute;
            right: 8px;
            color: #007bff;
        }
        .table th.sort-desc::after {
            content: '▼';
            position: absolute;
            right: 8px;
            color: #007bff;
        }
        .table td {
            vertical-align: middle;
        }

        /* 按钮样式 */
        .btn-outline-primary, .btn-outline-success, .btn-outline-danger {
            border-width: 2px;
            font-weight: 500;
        }
        .btn-outline-primary:hover, .btn-outline-success:hover, .btn-outline-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        /* 徽章样式 */
        .badge {
            font-weight: 500;
            padding: 0.5em 0.8em;
        }

        /* 表单样式 */
        .form-control, .form-select {
            border-radius: 10px;
            padding: 0.6rem 1rem;
            border: 1px solid #dee2e6;
            transition: all 0.2s;
        }
        .form-control:focus, .form-select:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.15);
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
            animation: fadeIn 0.5s ease forwards;
        }
    " />
</jsp:include>