package com.building.servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.building.dao.RoomDao;
import com.building.model.Room;

@WebServlet("/room/stats/floor")
public class RoomFloorStatsServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    private RoomDao roomDao;
    
    @Override
    public void init() throws ServletException {
        roomDao = new RoomDao();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");
        
        try {
            List<Room> rooms = roomDao.getAllRooms();
            Map<Integer, Integer> floorStats = new TreeMap<>();
            
            // 统计每层的房间数量
            for (Room room : rooms) {
                int floor = room.getFloorNumber();
                floorStats.put(floor, floorStats.getOrDefault(floor, 0) + 1);
            }
            
            // 准备JSON数据
            List<String> labels = new ArrayList<>();
            List<Integer> values = new ArrayList<>();
            
            for (Map.Entry<Integer, Integer> entry : floorStats.entrySet()) {
                labels.add(entry.getKey() + "层");
                values.add(entry.getValue());
            }
            
            String json = String.format(
                "{\"labels\":%s,\"values\":%s}",
                formatStringList(labels),
                formatIntegerList(values)
            );
            
            PrintWriter out = response.getWriter();
            out.write(json);
            
        } catch (Exception e) {
            String json = String.format(
                "{\"error\":true,\"message\":\"获取楼层统计失败：%s\"}",
                e.getMessage()
            );
            
            PrintWriter out = response.getWriter();
            out.write(json);
        }
    }
    
    private String formatStringList(List<String> list) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) sb.append(",");
            sb.append("\"").append(list.get(i)).append("\"");
        }
        sb.append("]");
        return sb.toString();
    }
    
    private String formatIntegerList(List<Integer> list) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) sb.append(",");
            sb.append(list.get(i));
        }
        sb.append("]");
        return sb.toString();
    }
} 