package com.building.servlet;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.dao.DeviceDao;
import com.building.dao.CameraDao;
import com.building.dao.RoomDao;
import com.building.model.Device;
import com.building.model.Room;
import com.google.gson.Gson;

/**
 * 楼层布局Servlet
 * 用于处理楼层布局相关的请求
 */
@WebServlet("/floor/layout")
public class FloorLayoutServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private RoomDao roomDao;
    private DeviceDao deviceDao;
    private CameraDao cameraDao;
    private Gson gson;

    @Override
    public void init() throws ServletException {
        roomDao = new RoomDao();
        deviceDao = new DeviceDao();
        cameraDao = new CameraDao();
        gson = new Gson();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }

        // 获取楼层参数
        String floorParam = request.getParameter("floor");
        int floor = 1; // 默认显示1楼

        if (floorParam != null && !floorParam.isEmpty()) {
            try {
                floor = Integer.parseInt(floorParam);
            } catch (NumberFormatException e) {
                // 使用默认值
            }
        }

        // 获取所有房间
        List<Room> allRooms = roomDao.getAllRooms();

        // 按楼层分组
        Map<Integer, List<Room>> roomsByFloor = allRooms.stream()
                .collect(Collectors.groupingBy(Room::getFloorNumber));

        // 获取所有楼层
        List<Integer> floors = roomsByFloor.keySet().stream()
                .sorted()
                .collect(Collectors.toList());

        // 获取当前楼层的房间
        List<Room> currentFloorRooms = roomsByFloor.getOrDefault(floor, List.of());

        // 为每个房间设置设备数量和在线设备数量
        for (Room room : currentFloorRooms) {
            // 获取房间内的设备
            List<Device> devices = deviceDao.getDevicesByRoomId(room.getId());

            // 设置设备总数
            room.setDeviceCount(devices.size());

            // 设置在线设备数量
            int onlineCount = 0;
            for (Device device : devices) {
                if (device.getConnectionStatus() == 1) {
                    onlineCount++;
                }
            }
            room.setOnlineDeviceCount(onlineCount);

            // 设置是否有摄像头设备和摄像头是否在线
            boolean hasCameraDevice = false;
            boolean cameraOnline = false;
            for (Device device : devices) {
                if ("摄像头".equals(device.getType())) {
                    hasCameraDevice = true;
                    if (device.getConnectionStatus() == 1) {
                        cameraOnline = true;
                    }
                    break;
                }
            }
            room.setHasCameraDevice(hasCameraDevice);
            room.setCameraOnline(cameraOnline);

            // 设置当前人数（模拟数据，实际应从摄像头或其他传感器获取）
            if (Math.random() > 0.5) {
                room.setCurrentPersonCount((int)(Math.random() * 30));
            }
        }

        // 设置请求属性
        request.setAttribute("floors", floors);
        request.setAttribute("currentFloor", floor);
        request.setAttribute("rooms", currentFloorRooms);

        // 转发到楼层布局页面
        request.getRequestDispatcher("/WEB-INF/views/floor/layout.jsp").forward(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // 处理AJAX请求，返回JSON数据
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        String action = request.getParameter("action");

        if ("getRoomsByFloor".equals(action)) {
            int floor = Integer.parseInt(request.getParameter("floor"));
            List<Room> rooms = roomDao.getAllRooms().stream()
                    .filter(r -> r.getFloorNumber() == floor)
                    .collect(Collectors.toList());

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("rooms", rooms);

            response.getWriter().write(gson.toJson(result));
        } else {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "未知操作");

            response.getWriter().write(gson.toJson(result));
        }
    }
}
