package com.building.dao;

import com.building.model.RoomLayout;
import com.building.model.RoomLayout.DevicePosition;
import com.building.util.DBUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 教室布局数据访问对象
 * 用于处理教室布局相关的数据库操作
 */
public class RoomLayoutDao {
    
    private Gson gson = new Gson();
    
    /**
     * 根据教室ID获取教室布局
     * @param roomId 教室ID
     * @return 教室布局对象，如果不存在则返回null
     */
    public RoomLayout getRoomLayoutByRoomId(int roomId) {
        String sql = "SELECT * FROM room_layout WHERE room_id = ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, roomId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToRoomLayout(rs);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 添加教室布局
     * @param layout 教室布局对象
     * @return 是否添加成功
     */
    public boolean addRoomLayout(RoomLayout layout) {
        String sql = "INSERT INTO room_layout (room_id, layout_data, image_url) VALUES (?, ?, ?)";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, layout.getRoomId());
            pstmt.setString(2, layout.getLayoutData());
            pstmt.setString(3, layout.getImageUrl());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新教室布局
     * @param layout 教室布局对象
     * @return 是否更新成功
     */
    public boolean updateRoomLayout(RoomLayout layout) {
        String sql = "UPDATE room_layout SET layout_data = ?, image_url = ? WHERE room_id = ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, layout.getLayoutData());
            pstmt.setString(2, layout.getImageUrl());
            pstmt.setInt(3, layout.getRoomId());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 删除教室布局
     * @param roomId 教室ID
     * @return 是否删除成功
     */
    public boolean deleteRoomLayout(int roomId) {
        String sql = "DELETE FROM room_layout WHERE room_id = ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, roomId);
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 获取所有教室布局
     * @return 教室布局列表
     */
    public List<RoomLayout> getAllRoomLayouts() {
        String sql = "SELECT * FROM room_layout";
        List<RoomLayout> layouts = new ArrayList<>();
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                layouts.add(mapResultSetToRoomLayout(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return layouts;
    }
    
    /**
     * 将ResultSet映射为RoomLayout对象
     * @param rs ResultSet对象
     * @return RoomLayout对象
     * @throws SQLException 如果发生SQL异常
     */
    private RoomLayout mapResultSetToRoomLayout(ResultSet rs) throws SQLException {
        RoomLayout layout = new RoomLayout();
        layout.setId(rs.getInt("id"));
        layout.setRoomId(rs.getInt("room_id"));
        layout.setLayoutData(rs.getString("layout_data"));
        layout.setImageUrl(rs.getString("image_url"));
        layout.setCreateTime(rs.getString("create_time"));
        layout.setUpdateTime(rs.getString("update_time"));
        
        // 解析设备位置数据
        String layoutData = layout.getLayoutData();
        if (layoutData != null && !layoutData.isEmpty()) {
            try {
                List<DevicePosition> devicePositions = gson.fromJson(layoutData, 
                        new TypeToken<List<DevicePosition>>(){}.getType());
                layout.setDevicePositions(devicePositions);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        
        return layout;
    }
}
