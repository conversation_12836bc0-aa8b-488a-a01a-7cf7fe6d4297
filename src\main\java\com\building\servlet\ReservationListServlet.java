package com.building.servlet;

import java.io.IOException;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.model.Reservation;
import com.building.model.User;
import com.building.service.ReservationService;
import com.building.service.impl.ReservationServiceImpl;

/**
 * 预约列表控制器
 * 处理预约列表的显示和统计
 */
@WebServlet("/reservation/list")
public class ReservationListServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    private ReservationService reservationService;
    
    @Override
    public void init() throws ServletException {
        try {
            reservationService = new ReservationServiceImpl();
            System.out.println("ReservationListServlet 初始化成功");
        } catch (Exception e) {
            System.err.println("ReservationListServlet 初始化失败: " + e.getMessage());
            e.printStackTrace();
            throw new ServletException("初始化ReservationService失败", e);
        }
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        try {
            System.out.println("开始处理预约列表请求");
            
            // 检查用户是否登录
            HttpSession session = request.getSession();
            User user = (User) session.getAttribute("user");
            if (user == null) {
                System.out.println("用户未登录，重定向到登录页面");
                response.sendRedirect(request.getContextPath() + "/login.jsp");
                return;
            }
            
            // 获取预约列表
            List<Reservation> reservations;
            if ("admin".equals(user.getRole())) {
                System.out.println("管理员用户，获取所有预约");
                reservations = reservationService.getAllReservations();
            } else {
                System.out.println("普通用户，获取用户ID为 " + user.getId() + " 的预约");
                reservations = reservationService.getUserReservations(user.getId());
            }
            request.setAttribute("reservations", reservations);
            
            // 获取预约统计信息
            System.out.println("获取预约统计信息");
            int[] stats = reservationService.getReservationStatistics();
            request.setAttribute("totalReservations", stats[0]);
            request.setAttribute("pendingReservations", stats[1]);
            request.setAttribute("approvedReservations", stats[2]);
            request.setAttribute("rejectedReservations", stats[3]);
            request.setAttribute("completedReservations", stats[4]);
            request.setAttribute("cancelledReservations", stats[5]);
            
            // 转发到预约列表页面
            System.out.println("转发到预约列表页面");
            request.getRequestDispatcher("/WEB-INF/views/reservation/list.jsp").forward(request, response);
            
        } catch (Exception e) {
            System.err.println("处理预约列表请求时发生错误: " + e.getMessage());
            e.printStackTrace();
            throw new ServletException("处理预约列表请求失败", e);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doGet(request, response);
    }
} 