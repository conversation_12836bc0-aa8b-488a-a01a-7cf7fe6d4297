<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产教融合大楼一楼布局图</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .layout-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .layout-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        
        .layout-header h1 {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .layout-header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .floor-plan-container {
            position: relative;
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }
        
        .floor-plan {
            position: relative;
            max-width: 100%;
            height: auto;
            border: 3px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .floor-plan img {
            width: 100%;
            height: auto;
            border-radius: 7px;
        }
        
        .room-overlay {
            position: absolute;
            border: 2px solid transparent;
            background: rgba(102, 126, 234, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 5px;
        }
        
        .room-overlay:hover {
            background: rgba(102, 126, 234, 0.4);
            border-color: #667eea;
            transform: scale(1.05);
            z-index: 10;
        }
        
        .room-label {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            color: #333;
            pointer-events: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .room-info-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            border-left: 5px solid #667eea;
        }
        
        .room-info-panel h4 {
            color: #667eea;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .room-details {
            display: none;
        }
        
        .room-details.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .room-type-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            margin-right: 10px;
        }
        
        .room-type-教室 { background: #e3f2fd; color: #1976d2; }
        .room-type-实验室 { background: #f3e5f5; color: #7b1fa2; }
        .room-type-办公室 { background: #e8f5e8; color: #388e3c; }
        .room-type-会议室 { background: #fff3e0; color: #f57c00; }
        .room-type-多媒体教室 { background: #fce4ec; color: #c2185b; }
        .room-type-储藏室 { background: #f1f8e9; color: #689f38; }
        .room-type-公共区域 { background: #e0f2f1; color: #00796b; }
        .room-type-公共设施 { background: #f9fbe7; color: #827717; }
        .room-type-通道 { background: #efebe9; color: #5d4037; }
        .room-type-研发中心 { background: #fff8e1; color: #e65100; }
        
        .legend {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .legend h5 {
            color: #333;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .legend-item {
            display: inline-block;
            margin: 5px 10px 5px 0;
        }
        
        .back-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        @media (max-width: 768px) {
            .layout-container {
                margin: 10px;
                padding: 20px;
            }
            
            .room-label {
                font-size: 10px;
                padding: 2px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="layout-container">
            <!-- 页面头部 -->
            <div class="layout-header">
                <h1><i class="fas fa-building"></i> 产教融合大楼一楼布局图</h1>
                <p>点击房间查看详细信息</p>
            </div>
            
            <!-- 返回按钮 -->
            <div class="mb-3">
                <a href="${pageContext.request.contextPath}/main.jsp" class="back-btn">
                    <i class="fas fa-arrow-left"></i> 返回主页
                </a>
            </div>
            
            <!-- 楼层平面图 -->
            <div class="floor-plan-container">
                <div class="floor-plan" id="floorPlan">
                    <img src="${pageContext.request.contextPath}/static/images/floor1-layout.png" 
                         alt="一楼布局图" id="floorImage">
                    <!-- 房间覆盖层将通过JavaScript动态添加 -->
                </div>
            </div>
            
            <!-- 房间信息面板 -->
            <div class="room-info-panel">
                <h4><i class="fas fa-info-circle"></i> 房间信息</h4>
                <div id="defaultInfo">
                    <p class="text-muted">请点击平面图中的房间查看详细信息</p>
                </div>
                <div id="roomDetails" class="room-details">
                    <!-- 房间详细信息将通过JavaScript动态填充 -->
                </div>
            </div>
            
            <!-- 图例 -->
            <div class="legend">
                <h5><i class="fas fa-palette"></i> 房间类型图例</h5>
                <div id="legendItems">
                    <!-- 图例项将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let roomsData = [];
        let currentScale = 1;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadRoomsData();
            setupImageLoad();
        });
        
        // 加载房间数据
        function loadRoomsData() {
            fetch('${pageContext.request.contextPath}/layout/floor1/rooms')
                .then(response => response.json())
                .then(data => {
                    roomsData = data;
                    generateLegend();
                })
                .catch(error => {
                    console.error('加载房间数据失败:', error);
                });
        }
        
        // 设置图片加载事件
        function setupImageLoad() {
            const floorImage = document.getElementById('floorImage');
            floorImage.onload = function() {
                createRoomOverlays();
            };
            
            // 如果图片已经加载完成
            if (floorImage.complete) {
                createRoomOverlays();
            }
        }
        
        // 创建房间覆盖层
        function createRoomOverlays() {
            const floorPlan = document.getElementById('floorPlan');
            const floorImage = document.getElementById('floorImage');
            
            // 清除现有的覆盖层
            const existingOverlays = floorPlan.querySelectorAll('.room-overlay');
            existingOverlays.forEach(overlay => overlay.remove());
            
            // 计算缩放比例
            const imageRect = floorImage.getBoundingClientRect();
            const scaleX = imageRect.width / 800; // 假设原始图片宽度为800px
            const scaleY = imageRect.height / 600; // 假设原始图片高度为600px
            
            roomsData.forEach(room => {
                const overlay = document.createElement('div');
                overlay.className = 'room-overlay';
                overlay.style.left = (room.x * scaleX) + 'px';
                overlay.style.top = (room.y * scaleY) + 'px';
                overlay.style.width = (room.width * scaleX) + 'px';
                overlay.style.height = (room.height * scaleY) + 'px';
                
                // 添加房间标签
                const label = document.createElement('div');
                label.className = 'room-label';
                label.textContent = room.roomNumber;
                overlay.appendChild(label);
                
                // 添加点击事件
                overlay.addEventListener('click', () => showRoomDetails(room));
                
                floorPlan.appendChild(overlay);
            });
        }
        
        // 显示房间详细信息
        function showRoomDetails(room) {
            const defaultInfo = document.getElementById('defaultInfo');
            const roomDetails = document.getElementById('roomDetails');
            
            defaultInfo.style.display = 'none';
            roomDetails.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fas fa-door-open"></i> ${room.roomNumber}</h5>
                        <p><strong>房间类型:</strong> 
                            <span class="room-type-badge room-type-${room.roomType}">${room.roomType}</span>
                        </p>
                        <p><strong>描述:</strong> ${room.description}</p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-ruler-combined"></i> 位置信息</h6>
                        <p><small class="text-muted">
                            坐标: (${room.x}, ${room.y})<br>
                            尺寸: ${room.width} × ${room.height}
                        </small></p>
                    </div>
                </div>
            `;
            roomDetails.classList.add('active');
        }
        
        // 生成图例
        function generateLegend() {
            const legendItems = document.getElementById('legendItems');
            const roomTypes = [...new Set(roomsData.map(room => room.roomType))];
            
            legendItems.innerHTML = roomTypes.map(type => 
                `<div class="legend-item">
                    <span class="room-type-badge room-type-${type}">${type}</span>
                </div>`
            ).join('');
        }
        
        // 窗口大小改变时重新创建覆盖层
        window.addEventListener('resize', function() {
            setTimeout(createRoomOverlays, 100);
        });
    </script>
</body>
</html> 