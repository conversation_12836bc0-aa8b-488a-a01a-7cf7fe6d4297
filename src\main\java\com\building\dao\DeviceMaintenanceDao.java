package com.building.dao;

import com.building.model.DeviceMaintenance;
import com.building.util.DBUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备维护记录数据访问对象
 * 用于处理设备维护记录相关的数据库操作
 */
public class DeviceMaintenanceDao {
    
    /**
     * 添加设备维护记录
     * @param maintenance 设备维护记录对象
     * @return 是否添加成功
     */
    public boolean addMaintenance(DeviceMaintenance maintenance) {
        String sql = "INSERT INTO device_maintenance (device_id, maintenance_type, maintenance_desc, " +
                    "maintenance_result, maintenance_person) VALUES (?, ?, ?, ?, ?)";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, maintenance.getDeviceId());
            pstmt.setString(2, maintenance.getMaintenanceType());
            pstmt.setString(3, maintenance.getMaintenanceDesc());
            pstmt.setString(4, maintenance.getMaintenanceResult());
            pstmt.setString(5, maintenance.getMaintenancePerson());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 根据设备ID获取维护记录列表
     * @param deviceId 设备ID
     * @return 维护记录列表
     */
    public List<DeviceMaintenance> getMaintenanceByDeviceId(int deviceId) {
        List<DeviceMaintenance> maintenances = new ArrayList<>();
        String sql = "SELECT * FROM device_maintenance WHERE device_id = ? ORDER BY maintenance_time DESC";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, deviceId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    DeviceMaintenance maintenance = mapResultSetToMaintenance(rs);
                    maintenances.add(maintenance);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return maintenances;
    }
    
    /**
     * 获取所有维护记录
     * @return 维护记录列表
     */
    public List<DeviceMaintenance> getAllMaintenance() {
        List<DeviceMaintenance> maintenances = new ArrayList<>();
        String sql = "SELECT * FROM device_maintenance ORDER BY maintenance_time DESC";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                DeviceMaintenance maintenance = mapResultSetToMaintenance(rs);
                maintenances.add(maintenance);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return maintenances;
    }
    
    /**
     * 获取最近的维护记录
     * @param limit 记录数量
     * @return 维护记录列表
     */
    public List<DeviceMaintenance> getRecentMaintenance(int limit) {
        List<DeviceMaintenance> maintenances = new ArrayList<>();
        String sql = "SELECT * FROM device_maintenance ORDER BY maintenance_time DESC LIMIT ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, limit);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    DeviceMaintenance maintenance = mapResultSetToMaintenance(rs);
                    maintenances.add(maintenance);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return maintenances;
    }
    
    /**
     * 将ResultSet映射为DeviceMaintenance对象
     * @param rs ResultSet对象
     * @return DeviceMaintenance对象
     * @throws SQLException 如果发生SQL异常
     */
    private DeviceMaintenance mapResultSetToMaintenance(ResultSet rs) throws SQLException {
        DeviceMaintenance maintenance = new DeviceMaintenance();
        maintenance.setId(rs.getInt("id"));
        maintenance.setDeviceId(rs.getInt("device_id"));
        maintenance.setMaintenanceType(rs.getString("maintenance_type"));
        maintenance.setMaintenanceDesc(rs.getString("maintenance_desc"));
        maintenance.setMaintenanceResult(rs.getString("maintenance_result"));
        maintenance.setMaintenancePerson(rs.getString("maintenance_person"));
        maintenance.setMaintenanceTime(rs.getString("maintenance_time"));
        return maintenance;
    }
}
