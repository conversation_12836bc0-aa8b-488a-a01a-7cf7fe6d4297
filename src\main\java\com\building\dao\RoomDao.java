package com.building.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.building.model.Room;
import com.building.util.DBUtil;

/**
 * 房间数据访问对象
 * 负责房间相关的数据库操作
 */
public class RoomDao {

    /**
     * 获取所有房间列表
     * @return 房间列表
     */
    public List<Room> getAllRooms() {
        List<Room> rooms = new ArrayList<>();
        String sql = "SELECT * FROM room ORDER BY floor_number, room_number";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                Room room = new Room();
                room.setId(rs.getInt("id"));
                room.setRoomNumber(rs.getString("room_number"));
                room.setFloorNumber(rs.getInt("floor_number"));
                room.setRoomType(rs.getString("room_type"));
                room.setArea(rs.getDouble("area"));
                room.setStatus(rs.getString("status"));
                room.setDescription(rs.getString("description"));

                // 获取布局相关字段
                room.setCapacity(rs.getInt("capacity"));
                room.setShape(rs.getString("shape"));
                room.setWidth(rs.getInt("width"));
                room.setLength(rs.getInt("length"));
                room.setPosition(rs.getString("position"));
                room.setLayoutImageUrl(rs.getString("layout_image_url"));

                room.setCreateTime(rs.getString("create_time"));
                room.setUpdateTime(rs.getString("update_time"));
                rooms.add(room);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return rooms;
    }

    /**
     * 根据ID获取房间信息
     * @param id 房间ID
     * @return 房间对象，如果不存在返回null
     */
    public Room getRoomById(int id) {
        String sql = "SELECT * FROM room WHERE id = ?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    Room room = new Room();
                    room.setId(rs.getInt("id"));
                    room.setRoomNumber(rs.getString("room_number"));
                    room.setFloorNumber(rs.getInt("floor_number"));
                    room.setRoomType(rs.getString("room_type"));
                    room.setArea(rs.getDouble("area"));
                    room.setStatus(rs.getString("status"));
                    room.setDescription(rs.getString("description"));

                    // 获取布局相关字段
                    room.setCapacity(rs.getInt("capacity"));
                    room.setShape(rs.getString("shape"));
                    room.setWidth(rs.getInt("width"));
                    room.setLength(rs.getInt("length"));
                    room.setPosition(rs.getString("position"));
                    room.setLayoutImageUrl(rs.getString("layout_image_url"));

                    room.setCreateTime(rs.getString("create_time"));
                    room.setUpdateTime(rs.getString("update_time"));
                    return room;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 添加新房间
     * @param room 房间对象
     * @return 是否添加成功
     */
    public boolean addRoom(Room room) {
        String sql = "INSERT INTO room (room_number, floor_number, room_type, area, status, description, " +
                    "capacity, shape, width, length, position, layout_image_url) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, room.getRoomNumber());
            pstmt.setInt(2, room.getFloorNumber());
            pstmt.setString(3, room.getRoomType());
            pstmt.setDouble(4, room.getArea());
            pstmt.setString(5, room.getStatus() != null ? room.getStatus() : "空闲");
            pstmt.setString(6, room.getDescription());

            // 设置布局相关字段
            pstmt.setObject(7, room.getCapacity());
            pstmt.setString(8, room.getShape());
            pstmt.setObject(9, room.getWidth());
            pstmt.setObject(10, room.getLength());
            pstmt.setString(11, room.getPosition());
            pstmt.setString(12, room.getLayoutImageUrl());

            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 更新房间信息
     * @param room 房间对象
     * @return 是否更新成功
     */
    public boolean updateRoom(Room room) {
        String sql = "UPDATE room SET room_number=?, floor_number=?, room_type=?, area=?, status=?, description=?, " +
                    "capacity=?, shape=?, width=?, length=?, position=?, layout_image_url=? WHERE id=?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, room.getRoomNumber());
            pstmt.setInt(2, room.getFloorNumber());
            pstmt.setString(3, room.getRoomType());
            pstmt.setDouble(4, room.getArea());
            pstmt.setString(5, room.getStatus());
            pstmt.setString(6, room.getDescription());

            // 设置布局相关字段
            pstmt.setObject(7, room.getCapacity());
            pstmt.setString(8, room.getShape());
            pstmt.setObject(9, room.getWidth());
            pstmt.setObject(10, room.getLength());
            pstmt.setString(11, room.getPosition());
            pstmt.setString(12, room.getLayoutImageUrl());

            pstmt.setInt(13, room.getId());

            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除房间
     * @param id 房间ID
     * @return 是否删除成功
     */
    public boolean deleteRoom(int id) {
        String sql = "DELETE FROM room WHERE id = ?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取房间统计信息
     * @return 包含总数、已使用和空闲数量的数组
     */
    public int[] getRoomStats() {
        int[] stats = new int[3]; // [总数, 已使用, 空闲]
        String sql = "SELECT COUNT(*) as total, " +
                    "SUM(CASE WHEN status='使用中' THEN 1 ELSE 0 END) as used, " +
                    "SUM(CASE WHEN status='空闲中' THEN 1 ELSE 0 END) as free " +
                    "FROM room";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            if (rs.next()) {
                stats[0] = rs.getInt("total");
                stats[1] = rs.getInt("used");
                stats[2] = rs.getInt("free");
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return stats;
    }
}