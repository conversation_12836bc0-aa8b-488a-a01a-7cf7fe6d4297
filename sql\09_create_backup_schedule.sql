-- 备份任务调度表创建脚本
-- 创建日期: 2024年6月12日
-- 描述: 用于存储备份任务的调度信息，支持定时备份功能


-- 创建备份任务调度表
CREATE TABLE `backup_schedule` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `schedule_name` VARCHAR(100) NOT NULL COMMENT '调度任务名称',
  `active` INT NOT NULL DEFAULT 1 COMMENT '是否激活',
  `schedule_type` VARCHAR(20) NOT NULL COMMENT '调度类型(daily/weekly/monthly)',
  `day_of_week` INT DEFAULT NULL COMMENT '星期几(1-7，仅weekly类型使用)',
  `day_of_month` INT DEFAULT NULL COMMENT '每月第几天(1-31，仅monthly类型使用)',
  `hour` INT NOT NULL COMMENT '小时(0-23)',
  `minute` INT NOT NULL COMMENT '分钟(0-59)',
  `retention_days` INT NOT NULL DEFAULT 30 COMMENT '备份保留天数',
  `backup_path` VARCHAR(255) NOT NULL COMMENT '备份路径',
  `last_run_time` DATETIME DEFAULT NULL COMMENT '上次运行时间',
  `next_run_time` DATETIME DEFAULT NULL COMMENT '下次计划运行时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_schedule_name` (`schedule_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='备份任务调度表';

-- 插入默认的每日备份任务
INSERT INTO `backup_schedule` (
  `schedule_name`, `active`, `schedule_type`, 
  `hour`, `minute`, `retention_days`, `backup_path`
) VALUES (
  '每日凌晨备份', 0, 'daily', 
  2, 0, 30, 'C:/building/backup'
); 