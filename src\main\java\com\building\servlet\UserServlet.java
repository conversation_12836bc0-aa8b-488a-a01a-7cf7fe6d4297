package com.building.servlet;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.building.dao.UserDao;
import com.building.model.User;
import com.google.gson.Gson;

/**
 * 用户管理Servlet
 * 处理用户相关的HTTP请求
 */
@WebServlet("/user/api/*")
public class UserServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private UserDao userDao;
    private Gson gson;

    @Override
    public void init() throws ServletException {
        userDao = new UserDao();
        gson = new Gson();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String pathInfo = request.getPathInfo();
        
        if (pathInfo == null || pathInfo.equals("") || pathInfo.equals("/")) {
            // 重定向到用户列表页面
            response.sendRedirect(request.getContextPath() + "/user/list");
        } else if (pathInfo.equals("/get")) {
            // 获取单个用户信息
            handleGet(request, response);
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        String contextPath = request.getContextPath();
        String pathInfo = requestURI.substring(contextPath.length() + "/user/api".length());
        
        if (pathInfo.equals("") || pathInfo.equals("/")) {
            // 保存用户信息
            handleSave(request, response);
        } else if (pathInfo.equals("/delete")) {
            // 删除用户
            handleDelete(request, response);
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    /**
     * 处理获取用户列表的请求
     */
    private void handleList(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        try {
            // 获取用户列表
            List<User> users = userDao.getUsers(1, 10, null);
            int totalUsers = userDao.getTotalUsers(null);
            
            // 计算管理员和普通用户数量
            int adminCount = 0;
            int userCount = 0;
            for (User user : users) {
                if ("admin".equals(user.getRole())) {
                    adminCount++;
                } else {
                    userCount++;
                }
            }
            
            // 设置属性
            request.setAttribute("users", users);
            request.setAttribute("totalUsers", totalUsers);
            request.setAttribute("adminCount", adminCount);
            request.setAttribute("userCount", userCount);
            
            // 转发到JSP页面
            request.getRequestDispatcher("/WEB-INF/views/user/list.jsp").forward(request, response);
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "获取用户列表失败：" + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/error/500.jsp").forward(request, response);
        }
    }

    /**
     * 处理获取单个用户信息的请求
     */
    private void handleGet(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        try {
            int id = Integer.parseInt(request.getParameter("id"));
            User user = userDao.getUserById(id);

            Map<String, Object> result = new HashMap<>();
            if (user != null) {
                result.put("success", true);
                result.put("data", user);
            } else {
                result.put("success", false);
                result.put("message", "用户不存在");
            }

            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(gson.toJson(result));
        } catch (Exception e) {
            sendError(response, "获取用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 处理保存用户信息的请求
     */
    private void handleSave(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        try {
            StringBuilder body = new StringBuilder();
            String line;
            while ((line = request.getReader().readLine()) != null) {
                body.append(line);
            }

            User user = gson.fromJson(body.toString(), User.class);
            boolean success;

            if (user.getId() == 0) {
                // 添加新用户
                success = userDao.addUser(user);
            } else {
                // 更新用户
                success = userDao.updateUser(user);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            if (!success) {
                result.put("message", "保存用户信息失败");
            }

            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(gson.toJson(result));
        } catch (Exception e) {
            sendError(response, "保存用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 处理删除用户的请求
     */
    private void handleDelete(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        try {
            int id = Integer.parseInt(request.getParameter("id"));
            boolean success = userDao.deleteUser(id);

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            if (!success) {
                result.put("message", "删除用户失败");
            }

            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(gson.toJson(result));
        } catch (Exception e) {
            sendError(response, "删除用户失败：" + e.getMessage());
        }
    }

    /**
     * 发送错误响应
     */
    private void sendError(HttpServletResponse response, String message) throws IOException {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);

        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(gson.toJson(result));
    }
} 