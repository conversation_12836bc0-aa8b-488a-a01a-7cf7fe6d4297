package com.building.service.impl;

import java.util.List;
import java.sql.Connection;
import java.sql.SQLException;
import com.building.dao.ReservationDao;
import com.building.model.Reservation;
import com.building.service.ReservationService;
import com.building.service.RoomService;
import com.building.service.DeviceService;
import com.building.util.DBUtil;

/**
 * 预约服务实现类
 */
public class ReservationServiceImpl implements ReservationService {
    
    private ReservationDao reservationDao;
    
    public ReservationServiceImpl() {
        this.reservationDao = new ReservationDao();
    }
    
    @Override
    public List<Reservation> getAllReservations() {
        return reservationDao.getAllReservations();
    }
    
    @Override
    public List<Reservation> getUserReservations(int userId) {
        return reservationDao.getUserReservations(userId);
    }
    
    @Override
    public Reservation getReservationById(int id) {
        if (id <= 0) {
            return null;
        }
        return reservationDao.getReservationById(id);
    }
    
    @Override
    public List<Reservation> getReservationsByUserId(int userId) {
        if (userId <= 0) {
            return null;
        }
        return reservationDao.getUserReservations(userId);
    }
    
    @Override
    public List<Reservation> getReservationsByRoomId(int roomId) {
        if (roomId <= 0) {
            return null;
        }
        return reservationDao.getReservationsByRoomId(roomId);
    }
    
    @Override
    public boolean isRoomReserved(int roomId, String startTime, String endTime) {
        if (roomId <= 0 || startTime == null || startTime.trim().isEmpty() || 
            endTime == null || endTime.trim().isEmpty()) {
            return false;
        }
        return reservationDao.isRoomReserved(roomId, startTime, endTime);
    }
    
    @Override
    public boolean isDeviceReserved(int deviceId, String startTime, String endTime) {
        if (deviceId <= 0 || startTime == null || startTime.trim().isEmpty() || 
            endTime == null || endTime.trim().isEmpty()) {
            return false;
        }
        return reservationDao.isDeviceReserved(deviceId, startTime, endTime);
    }
    
    @Override
    public boolean addReservation(Reservation reservation) {
        if (reservation == null || reservation.getUserId() <= 0 || 
            reservation.getStartTime() == null || reservation.getStartTime().trim().isEmpty() || 
            reservation.getEndTime() == null || reservation.getEndTime().trim().isEmpty()) {
            return false;
        }
        
        // 验证预约类型
        boolean isRoomReservation = reservation.getRoomId() != null;
        boolean isDeviceReservation = reservation.getDeviceId() != null;
        
        if (!isRoomReservation && !isDeviceReservation) {
            return false;
        }
        
        if (isRoomReservation && isDeviceReservation) {
            return false;
        }
        
        // 检查房间或设备是否已被预约
        if (isRoomReservation) {
            if (isRoomReserved(reservation.getRoomId(), reservation.getStartTime(), reservation.getEndTime())) {
                return false;
            }
        } else {
            if (isDeviceReserved(reservation.getDeviceId(), reservation.getStartTime(), reservation.getEndTime())) {
                return false;
            }
        }
        
        // 设置默认状态为待审核
        if (reservation.getStatus() == null || reservation.getStatus().trim().isEmpty()) {
            reservation.setStatus("待审核");
        }
        
        return reservationDao.addReservation(reservation);
    }
    
    @Override
    public boolean updateReservation(Reservation reservation) {
        if (reservation == null || reservation.getId() <= 0 || reservation.getUserId() <= 0 || 
            reservation.getRoomId() <= 0 || reservation.getStartTime() == null || 
            reservation.getStartTime().trim().isEmpty() || reservation.getEndTime() == null || 
            reservation.getEndTime().trim().isEmpty()) {
            return false;
        }
        
        // 检查房间是否已被其他预约占用
        Reservation existingReservation = getReservationById(reservation.getId());
        if (existingReservation == null) {
            return false;
        }
        
        // 如果房间或时间有变化，需要检查冲突
        if (existingReservation.getRoomId() != reservation.getRoomId() || 
            !existingReservation.getStartTime().equals(reservation.getStartTime()) || 
            !existingReservation.getEndTime().equals(reservation.getEndTime())) {
            
            // 检查新时间段是否与其他预约冲突（排除当前预约）
            List<Reservation> roomReservations = getReservationsByRoomId(reservation.getRoomId());
            for (Reservation r : roomReservations) {
                if (r.getId() != reservation.getId() && 
                    r.getStatus() != "已拒绝" && r.getStatus() != "已取消") {
                    
                    // 检查时间冲突
                    if ((r.getStartTime().compareTo(reservation.getEndTime()) <= 0 && 
                         r.getEndTime().compareTo(reservation.getStartTime()) >= 0)) {
                        return false;
                    }
                }
            }
        }
        
        return reservationDao.updateReservation(reservation);
    }
    
    @Override
    public boolean deleteReservation(int id) {
        if (id <= 0) {
            return false;
        }
        return reservationDao.deleteReservation(id);
    }
    
    @Override
    public boolean updateReservationStatus(int id, String status) {
        System.out.println("开始更新预约状态，ID: " + id + ", 新状态: " + status);
        
        // 验证ID
        if (id <= 0) {
            System.out.println("预约ID无效: " + id);
            return false;
        }
        
        // 验证状态值
        if (status == null || status.trim().isEmpty()) {
            System.out.println("状态值为空");
            return false;
        }
        
        // 检查预约是否存在
        Reservation reservation = getReservationById(id);
        if (reservation == null) {
            System.out.println("预约不存在，ID: " + id);
            return false;
        }
        
        System.out.println("获取到预约信息：");
        System.out.println("设备ID: " + reservation.getDeviceId());
        System.out.println("房间ID: " + reservation.getRoomId());
        System.out.println("当前状态: " + reservation.getStatus());
        System.out.println("目标状态: " + status);
        
        // 检查状态是否有效
        String[] validStatuses = {"待审核", "已批准", "已拒绝", "已完成", "已取消"};
        boolean isValidStatus = false;
        for (String validStatus : validStatuses) {
            if (validStatus.equals(status.trim())) {
                isValidStatus = true;
                break;
            }
        }
        
        if (!isValidStatus) {
            System.out.println("无效的状态值: " + status);
            return false;
        }
        
        // 检查状态转换是否合法
        if (reservation.getStatus().equals("已完成") || 
            reservation.getStatus().equals("已取消")) {
            System.out.println("不能更新已完成或已取消的预约状态");
            return false;
        }
        
        System.out.println("所有验证通过，开始更新状态");
        
        // 如果是设备预约，直接调用approveReservation或endReservation
        if (reservation.getDeviceId() != null) {
            System.out.println("这是一个设备预约");
            if (status.equals("已批准")) {
                System.out.println("调用approveReservation方法");
                boolean result = reservationDao.approveReservation(id);
                System.out.println("approveReservation结果: " + result);
                return result;
            } else if (status.equals("已完成")) {
                System.out.println("调用endReservation方法");
                boolean result = reservationDao.endReservation(id);
                System.out.println("endReservation结果: " + result);
                return result;
            } else if (status.equals("已拒绝") || status.equals("已取消")) {
                System.out.println("仅更新预约状态为: " + status);
                boolean result = reservationDao.updateReservationStatus(id, status);
                System.out.println("updateReservationStatus结果: " + result);
                return result;
            }
        } else {
            System.out.println("这是一个房间预约");
        }
        
        // 如果是房间预约，调用专门的approveReservation方法
        if (reservation.getRoomId() != null) {
            System.out.println("这是一个房间预约");
            if (status.equals("已批准")) {
                System.out.println("调用approveReservation方法");
                boolean result = reservationDao.approveReservation(id);
                System.out.println("approveReservation结果: " + result);
                return result;
            } else if (status.equals("已拒绝") || status.equals("已取消")) {
                System.out.println("调用updateReservationStatus方法");
                boolean result = reservationDao.updateReservationStatus(id, status);
                System.out.println("updateReservationStatus结果: " + result);
                return result;
            }
        }
        
        // 默认使用原有逻辑
        boolean result = reservationDao.updateReservationStatus(id, status);
        System.out.println("更新结果: " + result);
        return result;
    }
    
    @Override
    public int[] getReservationStatistics() {
        return reservationDao.getReservationStatistics();
    }
}
