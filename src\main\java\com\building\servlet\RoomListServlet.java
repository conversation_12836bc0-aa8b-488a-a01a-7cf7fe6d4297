package com.building.servlet;

import java.io.IOException;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.model.Room;
import com.building.service.RoomService;
import com.building.service.impl.RoomServiceImpl;

/**
 * 房间列表控制器
 * 处理房间列表的显示和统计
 */
@WebServlet("/room/list")
public class RoomListServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    private RoomService roomService;
    
    @Override
    public void init() throws ServletException {
        roomService = new RoomServiceImpl();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取房间列表
        List<Room> rooms = roomService.getAllRooms();
        request.setAttribute("rooms", rooms);
        
        // 获取房间统计信息
        int[] stats = roomService.getRoomStatistics();
        request.setAttribute("totalRooms", stats[0]);
        request.setAttribute("usedRooms", stats[1]);
        request.setAttribute("freeRooms", stats[2]);
        
        // 转发到房间列表页面
        request.getRequestDispatcher("/WEB-INF/views/room/list.jsp").forward(request, response);
    }
    
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doGet(request, response);
    }
} 