package com.building.service.impl;

import java.util.List;
import com.building.dao.DeviceDao;
import com.building.model.Device;
import com.building.service.DeviceService;

/**
 * 设备服务实现类
 */
public class DeviceServiceImpl implements DeviceService {
    private DeviceDao deviceDao;
    
    public DeviceServiceImpl() {
        this.deviceDao = new DeviceDao();
    }
    
    @Override
    public List<Device> getAllDevices() {
        return deviceDao.getAllDevices();
    }
    
    @Override
    public Device getDeviceById(int id) {
        if (id <= 0) {
            return null;
        }
        return deviceDao.getDeviceById(id);
    }
    
    @Override
    public int[] getDeviceStatistics() {
        return deviceDao.getDeviceStatistics();
    }
    
    @Override
    public boolean updateDeviceStatus(int id, String status) {
        // 验证ID
        if (id <= 0) {
            System.out.println("设备ID无效: " + id);
            return false;
        }
        
        // 验证状态值
        if (status == null || status.trim().isEmpty()) {
            System.out.println("状态值为空");
            return false;
        }
        
        // 检查设备是否存在
        Device device = getDeviceById(id);
        if (device == null) {
            System.out.println("设备不存在，ID: " + id);
            return false;
        }
        
        // 检查状态是否有效
        String[] validStatuses = {"空闲", "使用中", "维修中", "已报废"};
        boolean isValidStatus = false;
        for (String validStatus : validStatuses) {
            if (validStatus.equals(status.trim())) {
                isValidStatus = true;
                break;
            }
        }
        
        if (!isValidStatus) {
            System.out.println("无效的状态值: " + status);
            return false;
        }
        
        return deviceDao.updateDeviceStatus(id, status.trim());
    }
} 