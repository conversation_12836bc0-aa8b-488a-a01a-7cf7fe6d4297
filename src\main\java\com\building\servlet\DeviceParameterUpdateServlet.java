package com.building.servlet;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.building.dao.DeviceAlertDao;
import com.building.dao.DeviceDao;
import com.building.dao.DeviceParameterDao;
import com.building.model.Device;
import com.building.model.DeviceAlert;
import com.building.model.DeviceParameter;
import com.google.gson.Gson;

/**
 * 设备参数更新Servlet
 * 用于处理设备参数更新请求，通常由设备通过API调用
 */
@WebServlet("/api/device/update")
public class DeviceParameterUpdateServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private DeviceDao deviceDao;
    private DeviceParameterDao parameterDao;
    private DeviceAlertDao alertDao;
    private Gson gson;
    
    // 告警阈值
    private static final double TEMPERATURE_HIGH_THRESHOLD = 40.0;
    private static final double HUMIDITY_HIGH_THRESHOLD = 80.0;
    private static final double POWER_CONSUMPTION_HIGH_THRESHOLD = 500.0;
    
    @Override
    public void init() throws ServletException {
        deviceDao = new DeviceDao();
        parameterDao = new DeviceParameterDao();
        alertDao = new DeviceAlertDao();
        gson = new Gson();
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 设置响应类型
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        // 获取请求参数
        String deviceIdParam = request.getParameter("deviceId");
        String apiKey = request.getParameter("apiKey");
        String temperatureParam = request.getParameter("temperature");
        String humidityParam = request.getParameter("humidity");
        String powerConsumptionParam = request.getParameter("powerConsumption");
        String runtimeParam = request.getParameter("runtime");
        String powerStatusParam = request.getParameter("powerStatus");
        
        Map<String, Object> result = new HashMap<>();
        
        // 验证必要参数
        if (deviceIdParam == null || apiKey == null) {
            result.put("success", false);
            result.put("message", "参数不完整");
            response.getWriter().write(gson.toJson(result));
            return;
        }
        
        // TODO: 验证API密钥
        // 这里简化处理，实际应用中应该有更安全的验证机制
        if (!"api_key_123456".equals(apiKey)) {
            result.put("success", false);
            result.put("message", "API密钥无效");
            response.getWriter().write(gson.toJson(result));
            return;
        }
        
        try {
            int deviceId = Integer.parseInt(deviceIdParam);
            
            // 获取设备信息
            Device device = deviceDao.getDeviceById(deviceId);
            if (device == null) {
                result.put("success", false);
                result.put("message", "设备不存在");
                response.getWriter().write(gson.toJson(result));
                return;
            }
            
            // 更新设备连接状态
            deviceDao.updateDeviceConnectionStatus(deviceId, 1);
            
            // 更新设备电源状态
            if (powerStatusParam != null) {
                int powerStatus = Integer.parseInt(powerStatusParam);
                deviceDao.updateDevicePowerStatus(deviceId, powerStatus);
                
                // 记录参数历史
                parameterDao.addParameter(new DeviceParameter(deviceId, "power_status", String.valueOf(powerStatus)));
            }
            
            // 更新设备参数
            if (temperatureParam != null && humidityParam != null && 
                powerConsumptionParam != null && runtimeParam != null) {
                double temperature = Double.parseDouble(temperatureParam);
                double humidity = Double.parseDouble(humidityParam);
                double powerConsumption = Double.parseDouble(powerConsumptionParam);
                int runtime = Integer.parseInt(runtimeParam);
                
                // 更新设备参数
                deviceDao.updateDeviceParameters(deviceId, temperature, humidity, powerConsumption, runtime);
                
                // 记录参数历史
                parameterDao.addParameter(new DeviceParameter(deviceId, "temperature", String.valueOf(temperature)));
                parameterDao.addParameter(new DeviceParameter(deviceId, "humidity", String.valueOf(humidity)));
                parameterDao.addParameter(new DeviceParameter(deviceId, "power_consumption", String.valueOf(powerConsumption)));
                parameterDao.addParameter(new DeviceParameter(deviceId, "runtime", String.valueOf(runtime)));
                
                // 检查参数是否超过阈值，生成告警
                checkAndCreateAlerts(deviceId, temperature, humidity, powerConsumption);
            }
            
            result.put("success", true);
            result.put("message", "参数更新成功");
            
        } catch (NumberFormatException e) {
            result.put("success", false);
            result.put("message", "参数格式错误");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作异常: " + e.getMessage());
        }
        
        response.getWriter().write(gson.toJson(result));
    }
    
    /**
     * 检查参数是否超过阈值，生成告警
     * @param deviceId 设备ID
     * @param temperature 温度
     * @param humidity 湿度
     * @param powerConsumption 功耗
     */
    private void checkAndCreateAlerts(int deviceId, double temperature, double humidity, double powerConsumption) {
        // 检查温度
        if (temperature > TEMPERATURE_HIGH_THRESHOLD) {
            DeviceAlert alert = new DeviceAlert(
                deviceId, 
                "温度过高", 
                temperature > TEMPERATURE_HIGH_THRESHOLD + 10 ? 3 : 2, 
                "设备温度达到 " + temperature + "°C，超过阈值 " + TEMPERATURE_HIGH_THRESHOLD + "°C"
            );
            alertDao.addAlert(alert);
        }
        
        // 检查湿度
        if (humidity > HUMIDITY_HIGH_THRESHOLD) {
            DeviceAlert alert = new DeviceAlert(
                deviceId, 
                "湿度过高", 
                humidity > HUMIDITY_HIGH_THRESHOLD + 10 ? 3 : 2, 
                "设备湿度达到 " + humidity + "%，超过阈值 " + HUMIDITY_HIGH_THRESHOLD + "%"
            );
            alertDao.addAlert(alert);
        }
        
        // 检查功耗
        if (powerConsumption > POWER_CONSUMPTION_HIGH_THRESHOLD) {
            DeviceAlert alert = new DeviceAlert(
                deviceId, 
                "功耗过高", 
                powerConsumption > POWER_CONSUMPTION_HIGH_THRESHOLD + 100 ? 3 : 2, 
                "设备功耗达到 " + powerConsumption + "W，超过阈值 " + POWER_CONSUMPTION_HIGH_THRESHOLD + "W"
            );
            alertDao.addAlert(alert);
        }
    }
}
