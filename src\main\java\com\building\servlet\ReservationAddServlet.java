package com.building.servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.model.ApiResponse;
import com.building.model.Reservation;
import com.building.model.Room;
import com.building.model.Device;
import com.building.model.User;
import com.building.service.ReservationService;
import com.building.service.RoomService;
import com.building.dao.DeviceDao;
import com.building.service.impl.ReservationServiceImpl;
import com.building.service.impl.RoomServiceImpl;
import com.building.util.JsonUtil;

/**
 * 预约添加控制器
 * 处理预约的添加
 */
@WebServlet("/reservation/add")
public class ReservationAddServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    private ReservationService reservationService;
    private RoomService roomService;
    private DeviceDao deviceDao;
    
    @Override
    public void init() throws ServletException {
        try {
            reservationService = new ReservationServiceImpl();
            roomService = new RoomServiceImpl();
            deviceDao = new DeviceDao();
            System.out.println("ReservationAddServlet 初始化成功");
        } catch (Exception e) {
            System.err.println("ReservationAddServlet 初始化失败: " + e.getMessage());
            e.printStackTrace();
            throw new ServletException("初始化服务失败", e);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        System.out.println("开始处理预约添加请求");
        
        // 设置请求编码
        request.setCharacterEncoding("UTF-8");
        // 设置响应类型和编码
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = response.getWriter();
        
        try {
            // 检查用户是否登录
            HttpSession session = request.getSession();
            User user = (User) session.getAttribute("user");
            if (user == null) {
                System.out.println("用户未登录");
                ApiResponse apiResponse = new ApiResponse(false, "用户未登录");
                out.print(JsonUtil.toJson(apiResponse));
                return;
            }
            System.out.println("当前用户ID: " + user.getId());
            
            // 获取表单数据
            String roomIdStr = request.getParameter("roomId");
            String deviceIdStr = request.getParameter("deviceId");
            String startTime = request.getParameter("startTime");
            String endTime = request.getParameter("endTime");
            String purpose = request.getParameter("purpose");
            String description = request.getParameter("description");
            
            System.out.println("接收到的参数：");
            System.out.println("roomId: " + roomIdStr);
            System.out.println("deviceId: " + deviceIdStr);
            System.out.println("startTime: " + startTime);
            System.out.println("endTime: " + endTime);
            System.out.println("purpose: " + purpose);
            System.out.println("description: " + description);
            
            // 验证数据
            if (startTime == null || startTime.trim().isEmpty() || 
                endTime == null || endTime.trim().isEmpty() ||
                purpose == null || purpose.trim().isEmpty()) {
                System.out.println("基本表单数据不完整");
                ApiResponse apiResponse = new ApiResponse(false, "请填写所有必填字段");
                out.print(JsonUtil.toJson(apiResponse));
                return;
            }

            // 验证预约类型
            boolean isRoomReservation = roomIdStr != null && !roomIdStr.trim().isEmpty();
            boolean isDeviceReservation = deviceIdStr != null && !deviceIdStr.trim().isEmpty();
            
            if (!isRoomReservation && !isDeviceReservation) {
                System.out.println("未选择预约房间或设备");
                ApiResponse apiResponse = new ApiResponse(false, "请选择预约房间或设备");
                out.print(JsonUtil.toJson(apiResponse));
                return;
            }
            
            if (isRoomReservation && isDeviceReservation) {
                System.out.println("不能同时预约房间和设备");
                ApiResponse apiResponse = new ApiResponse(false, "请只选择预约房间或设备其中之一");
                out.print(JsonUtil.toJson(apiResponse));
                return;
            }
            
            // 转换日期时间格式
            startTime = startTime.replace("T", " ");
            endTime = endTime.replace("T", " ");
            
            System.out.println("转换后的时间格式：");
            System.out.println("startTime: " + startTime);
            System.out.println("endTime: " + endTime);
            
            // 创建预约对象
            Reservation reservation = new Reservation();
            reservation.setUserId(user.getId());
            
            // 根据预约类型设置ID
            if (isRoomReservation) {
                reservation.setRoomId(Integer.parseInt(roomIdStr));
                reservation.setDeviceId(null);
            } else {
                reservation.setDeviceId(Integer.parseInt(deviceIdStr));
                reservation.setRoomId(null);
            }
            
            reservation.setStartTime(startTime);
            reservation.setEndTime(endTime);
            reservation.setPurpose(purpose);
            reservation.setStatus("待审核");
            
            // 添加预约
            System.out.println("开始添加预约");
            boolean success = reservationService.addReservation(reservation);
            
            if (success) {
                System.out.println("预约添加成功");
                ApiResponse apiResponse = new ApiResponse(true, "预约添加成功");
                out.print(JsonUtil.toJson(apiResponse));
            } else {
                String errorMessage = isRoomReservation ? 
                    "预约添加失败，该房间在所选时间段已被预约" : 
                    "预约添加失败，该设备在所选时间段已被预约";
                System.out.println(errorMessage);
                ApiResponse apiResponse = new ApiResponse(false, errorMessage);
                out.print(JsonUtil.toJson(apiResponse));
            }
        } catch (NumberFormatException e) {
            System.err.println("房间ID或设备ID格式不正确: " + e.getMessage());
            ApiResponse apiResponse = new ApiResponse(false, "房间ID或设备ID格式不正确");
            out.print(JsonUtil.toJson(apiResponse));
        } catch (Exception e) {
            System.err.println("处理预约添加请求时发生错误: " + e.getMessage());
            e.printStackTrace();
            ApiResponse apiResponse = new ApiResponse(false, "系统错误：" + e.getMessage());
            out.print(JsonUtil.toJson(apiResponse));
        }
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        try {
            System.out.println("开始处理预约添加页面请求");
            
            // 检查用户是否登录
            HttpSession session = request.getSession();
            User user = (User) session.getAttribute("user");
            if (user == null) {
                System.out.println("用户未登录，重定向到登录页面");
                response.sendRedirect(request.getContextPath() + "/login.jsp");
                return;
            }
            
            // 获取所有可用房间
            System.out.println("获取可用房间列表");
            List<Room> rooms = roomService.getAllRooms();
            request.setAttribute("rooms", rooms);
            
            // 获取所有可用设备
            System.out.println("获取可用设备列表");
            List<Device> devices = deviceDao.getAllDevices();
            request.setAttribute("devices", devices);
            
            // 转发到预约添加页面
            System.out.println("转发到预约添加页面");
            request.getRequestDispatcher("/WEB-INF/views/reservation/add.jsp").forward(request, response);
            
        } catch (Exception e) {
            System.err.println("处理预约添加页面请求时发生错误: " + e.getMessage());
            e.printStackTrace();
            throw new ServletException("处理预约添加页面请求失败", e);
        }
    }
} 