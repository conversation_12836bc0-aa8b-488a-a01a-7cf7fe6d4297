<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="设备详情" />
    <jsp:param name="content" value="/WEB-INF/views/device/detail-content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 设备详情卡片样式 */
        .device-info-card {
            border-radius: 12px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }
        .device-info-card:hover {
            box-shadow: 0 0 20px rgba(0,0,0,0.15);
            transform: translateY(-3px);
        }
        /* 参数徽章样式 */
        .parameter-badge {
            font-size: 0.9rem;
            padding: 6px 12px;
            margin-right: 10px;
            border-radius: 6px;
            display: inline-block;
        }
        /* 图表容器样式 */
        .parameter-chart {
            width: 100%;
            height: 100%;
            margin: 0;
            min-height: 350px;
        }
        /* 增加卡片内边距 */
        .card-body {
            padding: 1.5rem !important;
        }
        /* 增加行间距 */
        .row {
            margin-bottom: 1rem;
        }
        /* 标签页内容间距 */
        .tab-pane {
            padding: 1rem 0;
        }
        /* 确保标签不重叠 */
        .nav-tabs .nav-link {
            padding: 0.5rem 1rem;
            margin-right: 0.25rem;
        }
        /* 响应式调整 */
        @media (max-width: 768px) {
            .card-body {
                padding: 1rem !important;
            }
            .nav-tabs .nav-link {
                padding: 0.5rem 0.75rem;
                font-size: 0.9rem;
            }
        }

        /* 进度条样式 */
        .progress-bar-custom {
            font-size: 14px;
            font-weight: 600;
        }
        .progress-container {
            height: 25px;
            margin-top: 10px;
            margin-bottom: 15px;
        }
        .status-indicator {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-indicator.online {
            background-color: #28a745;
        }
        .status-indicator.offline {
            background-color: #dc3545;
        }
        .status-indicator.warning {
            background-color: #ffc107;
        }
        .alert-card {
            border-left: 5px solid;
            margin-bottom: 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .alert-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .alert-card.high {
            border-left-color: #dc3545;
        }
        .alert-card.medium {
            border-left-color: #ffc107;
        }
        .alert-card.low {
            border-left-color: #28a745;
        }
        .alert-time {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 8px;
        }
        .maintenance-card {
            border-left: 5px solid #17a2b8;
            margin-bottom: 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .maintenance-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .maintenance-time {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 8px;
        }
        /* 实时参数卡片样式 */
        .param-card {
            border-radius: 10px;
            transition: all 0.3s ease;
            margin-bottom: 20px;
            height: 100%;
        }
        .param-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .param-card .card-body {
            padding: 20px;
        }
        .param-card h3 {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 10px 0;
        }
        .param-card i {
            font-size: 2.2rem;
            opacity: 0.8;
        }
    " />
    <jsp:param name="scripts" value="
        <script src='https://cdn.jsdelivr.net/npm/chart.js'></script>
        <script>
            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                // 初始化图表
                initCharts();

                // 设置定时刷新
                setInterval(function() {
                    refreshDeviceStatus();
                }, 30000); // 每30秒刷新一次

                // 控制按钮点击事件
                document.querySelectorAll('.control-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const deviceId = this.getAttribute('data-device-id');
                        const action = this.getAttribute('data-action');
                        controlDevice(deviceId, action);
                    });
                });

                // 解决告警按钮点击事件
                document.querySelectorAll('.resolve-alert-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const alertId = this.getAttribute('data-alert-id');
                        resolveAlert(alertId);
                    });
                });
            });

            // 初始化图表
            function initCharts() {
                // 温度图表
                const temperatureCtx = document.getElementById('temperatureChart');
                if (temperatureCtx) {
                    new Chart(temperatureCtx, {
                        type: 'line',
                        data: {
                            labels: ['1小时前', '50分钟前', '40分钟前', '30分钟前', '20分钟前', '10分钟前', '现在'],
                            datasets: [{
                                label: '温度 (°C)',
                                data: [25, 26, 25.5, 26.2, 26.5, 26.3, ${device.temperature}],
                                borderColor: 'rgba(255, 99, 132, 1)',
                                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    suggestedMin: 20,
                                    suggestedMax: 40
                                }
                            }
                        }
                    });
                }

                // 湿度图表
                const humidityCtx = document.getElementById('humidityChart');
                if (humidityCtx) {
                    new Chart(humidityCtx, {
                        type: 'line',
                        data: {
                            labels: ['1小时前', '50分钟前', '40分钟前', '30分钟前', '20分钟前', '10分钟前', '现在'],
                            datasets: [{
                                label: '湿度 (%)',
                                data: [45, 46, 47, 46.5, 46, 45.5, ${device.humidity}],
                                borderColor: 'rgba(54, 162, 235, 1)',
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    suggestedMin: 30,
                                    suggestedMax: 80
                                }
                            }
                        }
                    });
                }

                // 功耗图表
                const powerCtx = document.getElementById('powerChart');
                if (powerCtx) {
                    new Chart(powerCtx, {
                        type: 'line',
                        data: {
                            labels: ['1小时前', '50分钟前', '40分钟前', '30分钟前', '20分钟前', '10分钟前', '现在'],
                            datasets: [{
                                label: '功耗 (W)',
                                data: [120, 125, 130, 128, 132, 135, ${device.powerConsumption}],
                                borderColor: 'rgba(255, 159, 64, 1)',
                                backgroundColor: 'rgba(255, 159, 64, 0.2)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    suggestedMin: 0,
                                    suggestedMax: 500
                                }
                            }
                        }
                    });
                }

                // 运行时间图表
                const runtimeCtx = document.getElementById('runtimeChart');
                if (runtimeCtx) {
                    new Chart(runtimeCtx, {
                        type: 'line',
                        data: {
                            labels: ['1小时前', '50分钟前', '40分钟前', '30分钟前', '20分钟前', '10分钟前', '现在'],
                            datasets: [{
                                label: '运行时间 (分钟)',
                                data: [${device.runtime - 60}, ${device.runtime - 50}, ${device.runtime - 40}, ${device.runtime - 30}, ${device.runtime - 20}, ${device.runtime - 10}, ${device.runtime}],
                                borderColor: 'rgba(75, 192, 192, 1)',
                                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: false
                                }
                            }
                        }
                    });
                }
            }

            // 刷新设备状态
            function refreshDeviceStatus() {
                const deviceId = document.querySelector('.device-info-card').getAttribute('data-device-id');

                fetch('${pageContext.request.contextPath}/device/monitor', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=getDeviceStatus&deviceId=' + deviceId
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const device = data.device;

                        // 更新设备状态
                        const statusIndicator = document.querySelector('.status-indicator');
                        if (device.connectionStatus === 1) {
                            statusIndicator.classList.remove('offline');
                            statusIndicator.classList.add('online');
                            document.querySelector('.connection-status').textContent = '在线';
                        } else {
                            statusIndicator.classList.remove('online');
                            statusIndicator.classList.add('offline');
                            document.querySelector('.connection-status').textContent = '离线';
                        }

                        // 更新设备参数
                        document.querySelector('.temperature-value').textContent = device.temperature.toFixed(1) + '°C';
                        document.querySelector('.humidity-value').textContent = device.humidity.toFixed(1) + '%';
                        document.querySelector('.power-value').textContent = device.powerConsumption.toFixed(1) + 'W';
                        document.querySelector('.runtime-value').textContent = device.runtime + '分钟';

                        // 更新电源状态
                        const powerStatus = document.querySelector('.power-status');
                        if (device.powerStatus === 1) {
                            powerStatus.textContent = '开启';
                            powerStatus.classList.remove('badge-danger');
                            powerStatus.classList.add('badge-success');
                        } else {
                            powerStatus.textContent = '关闭';
                            powerStatus.classList.remove('badge-success');
                            powerStatus.classList.add('badge-danger');
                        }
                    }
                })
                .catch(error => console.error('Error:', error));
            }

            // 控制设备
            function controlDevice(deviceId, action) {
                fetch('${pageContext.request.contextPath}/device/control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'deviceId=' + deviceId + '&action=' + action
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('操作成功');
                        // 刷新页面
                        window.location.reload();
                    } else {
                        alert('操作失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请稍后重试');
                });
            }

            // 解决告警
            function resolveAlert(alertId) {
                fetch('${pageContext.request.contextPath}/device/monitor', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=resolveAlert&alertId=' + alertId
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新告警状态
                        const alertCard = document.querySelector('.alert-card[data-alert-id=\"' + alertId + '\"]');
                        alertCard.querySelector('.alert-status').textContent = '已解决';
                        alertCard.querySelector('.resolve-alert-btn').disabled = true;
                    } else {
                        alert('解决告警失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请稍后重试');
                });
            }
        </script>
    " />
</jsp:include>
