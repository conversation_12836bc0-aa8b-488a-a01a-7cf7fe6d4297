// 产线管理JavaScript功能

// 页面加载完成后初始化
$(document).ready(function() {
    // 初始化数据表格
    initDataTable();
    
    // 绑定事件处理器
    bindEventHandlers();
});

// 初始化数据表格
function initDataTable() {
    if ($.fn.DataTable) {
        $('#productionLineTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Chinese.json"
            },
            "pageLength": 10,
            "responsive": true,
            "order": [[0, "desc"]]
        });
    }
}

// 绑定事件处理器
function bindEventHandlers() {
    // 表单提交事件
    $('#addProductionLineForm').on('submit', function(e) {
        e.preventDefault();
        saveProductionLine();
    });
    
    $('#editProductionLineForm').on('submit', function(e) {
        e.preventDefault();
        updateProductionLine();
    });
}

// 刷新数据
function refreshData() {
    showLoading();
    setTimeout(function() {
        location.reload();
    }, 500);
}

// 显示加载状态
function showLoading() {
    const loadingHtml = `
        <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <span class="ms-2">正在刷新数据...</span>
        </div>
    `;
    
    // 可以在这里添加加载状态显示逻辑
}

// 保存新产线
function saveProductionLine() {
    const formData = {
        name: $('#name').val(),
        status: $('#status').val()
    };
    
    // 验证表单
    if (!formData.name.trim()) {
        showAlert('请输入产线名称', 'warning');
        return;
    }
    
    // 发送AJAX请求
    $.ajax({
        url: '/production/add',
        type: 'POST',
        data: formData,
        success: function(response) {
            if (response.success) {
                showAlert('产线添加成功', 'success');
                $('#addProductionLineModal').modal('hide');
                $('#addProductionLineForm')[0].reset();
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                showAlert(response.message || '添加失败', 'error');
            }
        },
        error: function() {
            showAlert('网络错误，请稍后重试', 'error');
        }
    });
}

// 编辑产线
function editProductionLine(id) {
    // 获取产线信息
    $.ajax({
        url: '/production/get/' + id,
        type: 'GET',
        success: function(response) {
            if (response.success) {
                const line = response.data;
                $('#editId').val(line.id);
                $('#editName').val(line.name);
                $('#editStatus').val(line.status);
                $('#editProductionLineModal').modal('show');
            } else {
                showAlert(response.message || '获取产线信息失败', 'error');
            }
        },
        error: function() {
            showAlert('网络错误，请稍后重试', 'error');
        }
    });
}

// 更新产线
function updateProductionLine() {
    const formData = {
        id: $('#editId').val(),
        name: $('#editName').val(),
        status: $('#editStatus').val()
    };
    
    // 验证表单
    if (!formData.name.trim()) {
        showAlert('请输入产线名称', 'warning');
        return;
    }
    
    // 发送AJAX请求
    $.ajax({
        url: '/production/update',
        type: 'POST',
        data: formData,
        success: function(response) {
            if (response.success) {
                showAlert('产线更新成功', 'success');
                $('#editProductionLineModal').modal('hide');
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                showAlert(response.message || '更新失败', 'error');
            }
        },
        error: function() {
            showAlert('网络错误，请稍后重试', 'error');
        }
    });
}

// 删除产线
function deleteProductionLine(id) {
    if (confirm('确定要删除这条产线记录吗？此操作不可恢复。')) {
        $.ajax({
            url: '/production/delete/' + id,
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    showAlert('产线删除成功', 'success');
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showAlert(response.message || '删除失败', 'error');
                }
            },
            error: function() {
                showAlert('网络错误，请稍后重试', 'error');
            }
        });
    }
}

// 显示提示信息
function showAlert(message, type) {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
            <i class="bi ${type === 'success' ? 'bi-check-circle' : 
                          type === 'error' ? 'bi-exclamation-triangle' : 
                          type === 'warning' ? 'bi-exclamation-triangle' : 
                          'bi-info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('body').append(alertHtml);
    
    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 3000);
}

// 导出数据
function exportData() {
    window.location.href = '/production/export';
}

// 筛选功能
function filterData() {
    // 这里可以添加筛选逻辑
    console.log('筛选功能待实现');
} 