-- 创建摄像头表
CREATE TABLE IF NOT EXISTS camera (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '摄像头名称',
    ip_address VARCHAR(50) NOT NULL COMMENT 'IP地址',
    port INT NOT NULL DEFAULT 554 COMMENT '端口号',
    username VARCHAR(50) COMMENT '用户名',
    password VARCHAR(50) COMMENT '密码',
    rtsp_url VARCHAR(255) COMMENT 'RTSP URL',
    location VARCHAR(100) COMMENT '位置',
    brand VARCHAR(50) COMMENT '品牌',
    model VARCHAR(50) COMMENT '型号',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-离线 1-在线',
    last_online_time DATETIME COMMENT '最后在线时间',
    room_id INT COMMENT '所属房间ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (room_id) REFERENCES room(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='摄像头信息表';

-- 创建人员进出记录表
CREATE TABLE IF NOT EXISTS person_record (
    id INT PRIMARY KEY AUTO_INCREMENT,
    camera_id INT NOT NULL COMMENT '摄像头ID',
    room_id INT NOT NULL COMMENT '房间ID',
    person_count INT NOT NULL DEFAULT 0 COMMENT '人数',
    record_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    image_url VARCHAR(255) COMMENT '图像URL',
    FOREIGN KEY (camera_id) REFERENCES camera(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES room(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人员进出记录表';

-- 插入测试数据
INSERT INTO camera (
    name, ip_address, port, username, password, rtsp_url, 
    location, brand, model, status, room_id
) VALUES
('教室A101摄像头', '*************', 554, 'admin', 'admin123', 
 'rtsp://*************:554/live/ch1', '教室A101前方', '海康威视', 'DS-2CD2T85G1-I5', 0, 1),
('教室A102摄像头', '*************', 554, 'admin', 'admin123', 
 'rtsp://*************:554/live/ch1', '教室A102后方', '大华', 'DH-IPC-HDW1235C', 0, 2),
('走廊监控摄像头', '*************', 554, 'admin', 'admin123', 
 'rtsp://*************:554/live/ch1', '一楼走廊', '宇视', 'HIC3421E-VD', 0, NULL);
