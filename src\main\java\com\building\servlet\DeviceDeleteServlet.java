package com.building.servlet;

import com.building.dao.DeviceDao;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@WebServlet("/device/delete")
public class DeviceDeleteServlet extends HttpServlet {
    private DeviceDao deviceDao;
    private ObjectMapper objectMapper;

    @Override
    public void init() throws ServletException {
        deviceDao = new DeviceDao();
        objectMapper = new ObjectMapper();
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");
        Map<String, Object> result = new HashMap<>();
        
        try {
            int id = Integer.parseInt(request.getParameter("id"));
            boolean success = deviceDao.deleteDevice(id);
            
            result.put("success", success);
            if (success) {
                result.put("message", "设备删除成功");
            } else {
                result.put("message", "设备删除失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "系统错误：" + e.getMessage());
        }
        
        objectMapper.writeValue(response.getWriter(), result);
    }
} 