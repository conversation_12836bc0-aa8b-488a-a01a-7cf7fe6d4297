package com.building.service;

import java.util.List;
import com.building.model.Room;

/**
 * 房间服务接口
 */
public interface RoomService {
    
    /**
     * 获取所有房间列表
     * @return 房间列表
     */
    List<Room> getAllRooms();
    
    /**
     * 根据ID获取房间信息
     * @param id 房间ID
     * @return 房间对象，如果不存在返回null
     */
    Room getRoomById(int id);
    
    /**
     * 获取房间统计信息
     * @return 包含总数、使用中和空闲数量的数组
     */
    int[] getRoomStatistics();
    
    /**
     * 更新房间状态
     * @param id 房间ID
     * @param status 新状态
     * @return 更新是否成功
     */
    boolean updateRoomStatus(int id, String status);
} 