package com.building.servlet;

import java.io.IOException;
import java.io.PrintWriter;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.building.service.RoomService;
import com.building.service.impl.RoomServiceImpl;

@WebServlet("/room/stats")
public class RoomStatsServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    private RoomService roomService;
    
    @Override
    public void init() throws ServletException {
        roomService = new RoomServiceImpl();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        response.setContentType("application/json;charset=UTF-8");
        
        try {
            int[] stats = roomService.getRoomStatistics();
            
            String json = String.format(
                "{\"total\":%d,\"used\":%d,\"free\":%d}",
                stats[0], stats[1], stats[2]
            );
            
            PrintWriter out = response.getWriter();
            out.write(json);
            
        } catch (Exception e) {
            String json = String.format(
                "{\"error\":true,\"message\":\"获取统计数据失败：%s\"}",
                e.getMessage()
            );
            
            PrintWriter out = response.getWriter();
            out.write(json);
        }
    }
} 