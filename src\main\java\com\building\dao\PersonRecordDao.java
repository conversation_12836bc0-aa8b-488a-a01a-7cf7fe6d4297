package com.building.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.building.model.PersonRecord;
import com.building.util.DBUtil;

/**
 * 人员记录数据访问对象
 * 用于处理人员进出记录相关的数据库操作
 */
public class PersonRecordDao {
    
    /**
     * 添加人员记录
     * @param record 人员记录对象
     * @return 是否添加成功
     */
    public boolean addRecord(PersonRecord record) {
        String sql = "INSERT INTO person_record (camera_id, room_id, person_count, image_url) " +
                    "VALUES (?, ?, ?, ?)";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, record.getCameraId());
            pstmt.setInt(2, record.getRoomId());
            pstmt.setInt(3, record.getPersonCount());
            pstmt.setString(4, record.getImageUrl());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 获取指定房间的最新人员记录
     * @param roomId 房间ID
     * @return 人员记录对象，如果不存在则返回null
     */
    public PersonRecord getLatestRecordByRoomId(int roomId) {
        String sql = "SELECT * FROM person_record WHERE room_id = ? ORDER BY record_time DESC LIMIT 1";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, roomId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToPersonRecord(rs);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 获取指定房间的人员记录历史
     * @param roomId 房间ID
     * @param limit 限制记录数量
     * @return 人员记录列表
     */
    public List<PersonRecord> getRecordHistoryByRoomId(int roomId, int limit) {
        List<PersonRecord> records = new ArrayList<>();
        String sql = "SELECT * FROM person_record WHERE room_id = ? ORDER BY record_time DESC LIMIT ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, roomId);
            pstmt.setInt(2, limit);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    records.add(mapResultSetToPersonRecord(rs));
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return records;
    }
    
    /**
     * 获取指定时间范围内的人员记录
     * @param roomId 房间ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 人员记录列表
     */
    public List<PersonRecord> getRecordsByTimeRange(int roomId, String startTime, String endTime) {
        List<PersonRecord> records = new ArrayList<>();
        String sql = "SELECT * FROM person_record WHERE room_id = ? AND record_time BETWEEN ? AND ? ORDER BY record_time";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, roomId);
            pstmt.setString(2, startTime);
            pstmt.setString(3, endTime);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    records.add(mapResultSetToPersonRecord(rs));
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return records;
    }
    
    /**
     * 将ResultSet映射到PersonRecord对象
     * @param rs ResultSet
     * @return PersonRecord对象
     * @throws SQLException 如果发生SQL异常
     */
    private PersonRecord mapResultSetToPersonRecord(ResultSet rs) throws SQLException {
        PersonRecord record = new PersonRecord();
        record.setId(rs.getInt("id"));
        record.setCameraId(rs.getInt("camera_id"));
        record.setRoomId(rs.getInt("room_id"));
        record.setPersonCount(rs.getInt("person_count"));
        record.setRecordTime(rs.getString("record_time"));
        record.setImageUrl(rs.getString("image_url"));
        return record;
    }
}
