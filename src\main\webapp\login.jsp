<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>产教融合大楼数字化管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .login-container {
            max-width: 400px;
            margin: 100px auto;
            padding: 20px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .system-title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .error-message {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 20px;
            display: none;
        }
        .error-message.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <h2 class="system-title">产教融合大楼数字化管理系统</h2>
            <!-- 错误信息显示区域 -->
            <div class="error-message <%= request.getAttribute("error") != null ? "show" : "" %>">
                <%= request.getAttribute("error") != null ? request.getAttribute("error") : "" %>
            </div>
            <form action="login" method="post" id="loginForm">
                <div class="mb-3">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="username" name="username" required
                           value="<%= request.getParameter("username") != null ? request.getParameter("username") : "" %>">
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
                <div class="mb-3">
                    <label for="captcha" class="form-label">验证码</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="captcha" name="captcha" required>
                        <img src="captcha" class="img-thumbnail" style="height: 38px; cursor: pointer;" onclick="this.src='captcha?'+Math.random()" title="点击刷新验证码">
                    </div>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember">记住密码</label>
                </div>
                <button type="submit" class="btn btn-primary w-100">登录</button>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 如果有错误信息，5秒后自动隐藏
        $(document).ready(function() {
            var errorMessage = $('.error-message');
            if (errorMessage.hasClass('show')) {
                setTimeout(function() {
                    errorMessage.fadeOut('slow');
                }, 5000);
            }

            // 当开始输入时，隐藏错误信息
            $('input').on('input', function() {
                errorMessage.fadeOut('slow');
            });
        });
    </script>
</body>
</html>