<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="用户管理" />
    <jsp:param name="content" value="/WEB-INF/views/user/content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 卡片样式 */
        .card {
            border-radius: 15px;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        .card:hover {
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        /* 按钮样式 */
        .btn-outline-primary, .btn-outline-success, .btn-outline-danger {
            border-width: 2px;
            font-weight: 500;
        }
        .btn-outline-primary:hover, .btn-outline-success:hover, .btn-outline-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        /* 徽章样式 */
        .badge {
            font-weight: 500;
            padding: 0.5em 0.8em;
        }

        /* 表单样式 */
        .form-control, .form-select {
            border-radius: 10px;
            padding: 0.6rem 1rem;
            border: 1px solid #dee2e6;
            transition: all 0.2s;
        }
        .form-control:focus, .form-select:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.15);
        }

        /* 用户头像样式 */
        .avatar-circle {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            transition: transform 0.2s;
        }
        .avatar-circle:hover {
            transform: scale(1.1);
        }

        /* 表格行样式 */
        .table tbody tr {
            transition: background-color 0.2s;
        }
        .table tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
            animation: fadeIn 0.5s ease forwards;
        }
    " />
</jsp:include>