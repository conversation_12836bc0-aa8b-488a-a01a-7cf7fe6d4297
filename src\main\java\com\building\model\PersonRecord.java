package com.building.model;

/**
 * 人员记录实体类
 * 对应数据库中的person_record表
 */
public class PersonRecord {
    private int id;             // 记录ID
    private int cameraId;       // 摄像头ID
    private int roomId;         // 房间ID
    private int personCount;    // 人数
    private String recordTime;  // 记录时间
    private String imageUrl;    // 图像URL
    
    // 非持久化字段
    private Camera camera;      // 摄像头
    private Room room;          // 房间
    
    /**
     * 默认构造函数
     */
    public PersonRecord() {
    }
    
    /**
     * 带参数的构造函数
     * @param cameraId 摄像头ID
     * @param roomId 房间ID
     * @param personCount 人数
     * @param imageUrl 图像URL
     */
    public PersonRecord(int cameraId, int roomId, int personCount, String imageUrl) {
        this.cameraId = cameraId;
        this.roomId = roomId;
        this.personCount = personCount;
        this.imageUrl = imageUrl;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public int getCameraId() {
        return cameraId;
    }
    
    public void setCameraId(int cameraId) {
        this.cameraId = cameraId;
    }
    
    public int getRoomId() {
        return roomId;
    }
    
    public void setRoomId(int roomId) {
        this.roomId = roomId;
    }
    
    public int getPersonCount() {
        return personCount;
    }
    
    public void setPersonCount(int personCount) {
        this.personCount = personCount;
    }
    
    public String getRecordTime() {
        return recordTime;
    }
    
    public void setRecordTime(String recordTime) {
        this.recordTime = recordTime;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public Camera getCamera() {
        return camera;
    }
    
    public void setCamera(Camera camera) {
        this.camera = camera;
    }
    
    public Room getRoom() {
        return room;
    }
    
    public void setRoom(Room room) {
        this.room = room;
    }
    
    @Override
    public String toString() {
        return "PersonRecord{" +
                "id=" + id +
                ", cameraId=" + cameraId +
                ", roomId=" + roomId +
                ", personCount=" + personCount +
                ", recordTime='" + recordTime + '\'' +
                '}';
    }
}
