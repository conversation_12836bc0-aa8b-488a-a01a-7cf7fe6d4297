package com.building.service.impl;

import com.building.dao.UserDao;
import com.building.model.User;
import com.building.service.UserService;

/**
 * 用户服务实现类
 */
public class UserServiceImpl implements UserService {
    
    private UserDao userDao;
    
    public UserServiceImpl() {
        userDao = new UserDao();
    }
    
    @Override
    public User login(String username, String password) {
        if (username == null || password == null) {
            return null;
        }
        return userDao.findByUsernameAndPassword(username, password);
    }
    
    @Override
    public User findByUsername(String username) {
        if (username == null) {
            return null;
        }
        return userDao.findByUsername(username);
    }
    
    @Override
    public boolean checkUsernameExists(String username) {
        if (username == null) {
            return false;
        }
        return userDao.findByUsername(username) != null;
    }
} 