package com.building.servlet;

import java.io.IOException;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.dao.DeviceAlertDao;
import com.building.dao.DeviceDao;
import com.building.dao.DeviceMaintenanceDao;
import com.building.dao.DeviceParameterDao;
import com.building.model.Device;
import com.building.model.DeviceAlert;
import com.building.model.DeviceMaintenance;
import com.building.model.DeviceParameter;

/**
 * 设备详情Servlet
 * 用于处理设备详情页面的请求
 */
@WebServlet("/device/detail")
public class DeviceDetailServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private DeviceDao deviceDao;
    private DeviceParameterDao parameterDao;
    private DeviceAlertDao alertDao;
    private DeviceMaintenanceDao maintenanceDao;
    
    @Override
    public void init() throws ServletException {
        deviceDao = new DeviceDao();
        parameterDao = new DeviceParameterDao();
        alertDao = new DeviceAlertDao();
        maintenanceDao = new DeviceMaintenanceDao();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取设备ID
        String idParam = request.getParameter("id");
        if (idParam == null || idParam.isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/device/list");
            return;
        }
        
        try {
            int deviceId = Integer.parseInt(idParam);
            
            // 获取设备信息
            Device device = deviceDao.getDeviceById(deviceId);
            if (device == null) {
                response.sendRedirect(request.getContextPath() + "/device/list");
                return;
            }
            
            // 获取设备参数历史
            List<DeviceParameter> parameters = parameterDao.getLatestParametersByDeviceId(deviceId);
            device.setParameters(parameters);
            
            // 获取设备告警
            List<DeviceAlert> alerts = alertDao.getAlertsByDeviceId(deviceId);
            device.setAlerts(alerts);
            
            // 获取设备维护记录
            List<DeviceMaintenance> maintenances = maintenanceDao.getMaintenanceByDeviceId(deviceId);
            
            // 设置请求属性
            request.setAttribute("device", device);
            request.setAttribute("maintenances", maintenances);
            
            // 转发到设备详情页面
            request.getRequestDispatcher("/WEB-INF/views/device/detail.jsp").forward(request, response);
            
        } catch (NumberFormatException e) {
            response.sendRedirect(request.getContextPath() + "/device/list");
        }
    }
}
