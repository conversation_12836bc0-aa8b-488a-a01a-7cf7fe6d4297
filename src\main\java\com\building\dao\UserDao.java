package com.building.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import com.building.model.User;
import com.building.util.DBUtil;

/**
 * 用户数据访问对象
 * 负责处理与用户相关的所有数据库操作
 * 包括：用户登录、更新登录时间等
 * 
 * <AUTHOR>
 * @date 2024-03-04
 */
public class UserDao {
    
    /**
     * 获取用户列表（分页）
     * @param page 页码
     * @param pageSize 每页大小
     * @param search 搜索关键词
     * @return 用户列表
     */
    public List<User> getUsers(int page, int pageSize, String search) {
        List<User> users = new ArrayList<>();
        String sql = "SELECT * FROM user WHERE username LIKE ? OR real_name LIKE ? ORDER BY id LIMIT ? OFFSET ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            String searchPattern = "%" + (search != null ? search : "") + "%";
            pstmt.setString(1, searchPattern);
            pstmt.setString(2, searchPattern);
            pstmt.setInt(3, pageSize);
            pstmt.setInt(4, (page - 1) * pageSize);
            
            System.out.println("执行SQL查询: " + sql);
            System.out.println("参数: searchPattern=" + searchPattern + ", pageSize=" + pageSize + ", offset=" + (page - 1) * pageSize);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    try {
                        User user = new User();
                        user.setId(rs.getInt("id"));
                        user.setUsername(rs.getString("username"));
                        user.setRealName(rs.getString("real_name"));
                        user.setRole(rs.getString("role"));
                        
                        // 处理时间字段
                        Timestamp createTime = rs.getTimestamp("create_time");
                        Timestamp lastLoginTime = rs.getTimestamp("last_login_time");
                        
                        if (createTime != null) {
                            user.setCreateTime(createTime.toString());
                        } else {
                            System.out.println("警告: create_time 为 null");
                            user.setCreateTime(null);
                        }
                        
                        if (lastLoginTime != null) {
                            user.setLastLoginTime(lastLoginTime.toString());
                        } else {
                            System.out.println("警告: last_login_time 为 null");
                            user.setLastLoginTime(null);
                        }
                        
                        users.add(user);
                        System.out.println("成功添加用户: " + user.getUsername());
                    } catch (Exception e) {
                        System.err.println("处理用户数据时出错: " + e.getMessage());
                        e.printStackTrace();
                        throw new RuntimeException("处理用户数据失败: " + e.getMessage());
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("数据库查询错误: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("获取用户列表失败: " + e.getMessage());
        }
        return users;
    }
    
    /**
     * 获取用户总数
     * @param search 搜索关键词
     * @return 用户总数
     */
    public int getTotalUsers(String search) {
        String sql = "SELECT COUNT(*) as total FROM user WHERE username LIKE ? OR real_name LIKE ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            String searchPattern = "%" + (search != null ? search : "") + "%";
            pstmt.setString(1, searchPattern);
            pstmt.setString(2, searchPattern);
            
            System.out.println("执行SQL查询: " + sql);
            System.out.println("参数: searchPattern=" + searchPattern);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("total");
                }
            }
        } catch (SQLException e) {
            System.err.println("数据库查询错误: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("获取用户总数失败: " + e.getMessage());
        }
        return 0;
    }
    
    /**
     * 根据ID获取用户信息
     * @param id 用户ID
     * @return 用户对象，如果不存在返回null
     */
    public User getUserById(int id) {
        String sql = "SELECT * FROM user WHERE id = ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    User user = new User();
                    user.setId(rs.getInt("id"));
                    user.setUsername(rs.getString("username"));
                    user.setRealName(rs.getString("real_name"));
                    user.setRole(rs.getString("role"));
                    user.setCreateTime(rs.getString("create_time"));
                    user.setLastLoginTime(rs.getString("last_login_time"));
                    return user;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * 添加新用户
     * @param user 用户对象
     * @return 是否添加成功
     */
    public boolean addUser(User user) {
        String sql = "INSERT INTO user (username, password, real_name, role) VALUES (?, ?, ?, ?)";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, user.getUsername());
            pstmt.setString(2, user.getPassword());
            pstmt.setString(3, user.getRealName());
            pstmt.setString(4, user.getRole());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新用户信息
     * @param user 用户对象
     * @return 是否更新成功
     */
    public boolean updateUser(User user) {
        StringBuilder sql = new StringBuilder("UPDATE user SET username=?, real_name=?, role=?");
        List<Object> params = new ArrayList<>();
        params.add(user.getUsername());
        params.add(user.getRealName());
        params.add(user.getRole());
        
        // 如果密码不为空，则更新密码
        if (user.getPassword() != null && !user.getPassword().trim().isEmpty()) {
            sql.append(", password=?");
            params.add(user.getPassword());
        }
        
        sql.append(" WHERE id=?");
        params.add(user.getId());
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql.toString())) {
            
            for (int i = 0; i < params.size(); i++) {
                pstmt.setObject(i + 1, params.get(i));
            }
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 删除用户
     * @param id 用户ID
     * @return 是否删除成功
     */
    public boolean deleteUser(int id) {
        String sql = "DELETE FROM user WHERE id = ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 更新用户最后登录时间
     * @param id 用户ID
     * @return 是否更新成功
     */
    public boolean updateLastLoginTime(int id) {
        String sql = "UPDATE user SET last_login_time = CURRENT_TIMESTAMP WHERE id = ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 用户登录验证
     * 根据用户名和密码验证用户身份
     * 登录成功后自动更新最后登录时间
     * 
     * @param username 用户名
     * @param password 密码（加密后的）
     * @return 登录成功返回用户对象，失败返回null
     */
    public User login(String username, String password) {
        String sql = "SELECT * FROM user WHERE username = ? AND password = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, username);
            pstmt.setString(2, password);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                User user = new User();
                user.setId(rs.getInt("id"));
                user.setUsername(rs.getString("username"));
                user.setRealName(rs.getString("real_name"));
                user.setRole(rs.getString("role"));
                user.setCreateTime(rs.getString("create_time"));
                user.setLastLoginTime(rs.getString("last_login_time"));
                
                // 更新最后登录时间
                updateLastLoginTime(user.getId());
                
                return user;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                if (rs != null) rs.close();
                if (pstmt != null) pstmt.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
            DBUtil.closeConnection(conn);
        }
        return null;
    }
    
    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户对象，如果不存在返回null
     */
    public User findByUsername(String username) {
        String sql = "SELECT * FROM user WHERE username = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, username);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                User user = new User();
                user.setId(rs.getInt("id"));
                user.setUsername(rs.getString("username"));
                user.setRealName(rs.getString("real_name"));
                user.setRole(rs.getString("role"));
                user.setCreateTime(rs.getString("create_time"));
                user.setLastLoginTime(rs.getString("last_login_time"));
                return user;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                if (rs != null) rs.close();
                if (pstmt != null) pstmt.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
            DBUtil.closeConnection(conn);
        }
        return null;
    }
    
    /**
     * 根据用户名和密码查找用户
     * 
     * @param username 用户名
     * @param password 密码
     * @return 用户对象，如果不存在返回null
     */
    public User findByUsernameAndPassword(String username, String password) {
        return login(username, password); // 复用现有的login方法
    }
} 