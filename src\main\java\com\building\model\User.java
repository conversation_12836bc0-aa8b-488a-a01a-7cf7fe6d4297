package com.building.model;

/**
 * 用户实体类
 * 对应数据库中的user表
 * 包含用户的基本信息、认证信息和时间信息
 * 
 * <AUTHOR>
 * @date 2024-03-04
 */
public class User {
    /**
     * 用户ID，主键
     */
    private Integer id;
    
    /**
     * 用户名，用于登录
     */
    private String username;
    
    /**
     * 密码，存储加密后的密码
     */
    private String password;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 用户角色
     * 可选值：admin（管理员）、user（普通用户）
     */
    private String role;
    
    /**
     * 账号创建时间
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    private String createTime;
    
    /**
     * 最后登录时间
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    private String lastLoginTime;
    
    // Getters and Setters
    /**
     * 获取用户ID
     * 
     * @return 用户ID
     */
    public Integer getId() {
        return id;
    }
    
    /**
     * 设置用户ID
     * 
     * @param id 用户ID
     */
    public void setId(Integer id) {
        this.id = id;
    }
    
    /**
     * 获取用户名
     * 
     * @return 用户名
     */
    public String getUsername() {
        return username;
    }
    
    /**
     * 设置用户名
     * 
     * @param username 用户名
     */
    public void setUsername(String username) {
        this.username = username;
    }
    
    /**
     * 获取密码
     * 
     * @return 加密后的密码
     */
    public String getPassword() {
        return password;
    }
    
    /**
     * 设置密码
     * 
     * @param password 加密后的密码
     */
    public void setPassword(String password) {
        this.password = password;
    }
    
    /**
     * 获取真实姓名
     * 
     * @return 真实姓名
     */
    public String getRealName() {
        return realName;
    }
    
    /**
     * 设置真实姓名
     * 
     * @param realName 真实姓名
     */
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    /**
     * 获取用户角色
     * 
     * @return 用户角色（admin/user）
     */
    public String getRole() {
        return role;
    }
    
    /**
     * 设置用户角色
     * 
     * @param role 用户角色（admin/user）
     */
    public void setRole(String role) {
        this.role = role;
    }
    
    /**
     * 获取账号创建时间
     * 
     * @return 创建时间
     */
    public String getCreateTime() {
        return createTime;
    }
    
    /**
     * 设置账号创建时间
     * 
     * @param createTime 创建时间
     */
    public void setCreateTime(String createTime) {
        this.createTime = createTime != null ? createTime.trim() : null;
    }
    
    /**
     * 获取最后登录时间
     * 
     * @return 最后登录时间
     */
    public String getLastLoginTime() {
        return lastLoginTime;
    }
    
    /**
     * 设置最后登录时间
     * 
     * @param lastLoginTime 最后登录时间
     */
    public void setLastLoginTime(String lastLoginTime) {
        this.lastLoginTime = lastLoginTime != null ? lastLoginTime.trim() : null;
    }
} 