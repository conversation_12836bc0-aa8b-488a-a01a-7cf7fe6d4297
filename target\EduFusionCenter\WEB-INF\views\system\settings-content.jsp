<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<h1 class="page-title">
    <i class="bi bi-gear me-2"></i>系统设置
</h1>

<div id="alertContainer"></div>

<!-- 安全设置 -->
<div id="security-section" class="settings-card visible-section">
    <span class="badge bg-primary badge-info">1/4</span>
    <div class="setting-header">
        <div class="setting-icon">
            <i class="bi bi-shield-lock"></i>
        </div>
        <div>
            <h5 class="setting-title">安全设置</h5>
            <p class="setting-description">管理密码策略和登录安全相关设置</p>
        </div>
    </div>
    
    <form id="securityForm">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label required-field">密码最小长度</label>
                    <input type="number" class="form-control" name="minPasswordLength" value="8" min="6" max="30">
                    <div class="form-text">推荐：8-12个字符</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label required-field">登录失败限制</label>
                    <input type="number" class="form-control" name="maxLoginAttempts" value="5" min="1" max="10">
                    <div class="form-text">超过限制后账户将被临时锁定</div>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <label class="form-label">密码复杂度要求</label>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="requireUppercase" checked>
                        <label class="form-check-label">必须包含大写字母</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="requireNumbers" checked>
                        <label class="form-check-label">必须包含数字</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="requireSpecialChars">
                        <label class="form-check-label">必须包含特殊字符</label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="actions-container mt-4">
            <button type="button" class="btn btn-primary" id="saveSecurityBtn">
                <i class="bi bi-check-circle"></i> 保存安全设置
            </button>
        </div>
    </form>
    
    <div class="pagination-container">
        <div></div>
        <button class="btn btn-outline-primary btn-sm" onclick="showSection('email-section')">
            下一项 <i class="bi bi-arrow-right"></i>
        </button>
    </div>
</div>

<!-- 邮件设置 -->
<div id="email-section" class="settings-card hidden-section">
    <span class="badge bg-primary badge-info">2/4</span>
    <div class="setting-header">
        <div class="setting-icon">
            <i class="bi bi-envelope"></i>
        </div>
        <div>
            <h5 class="setting-title">邮件设置</h5>
            <p class="setting-description">配置邮件服务器和通知邮件设置</p>
        </div>
    </div>
    
    <form id="emailForm">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label required-field">SMTP服务器</label>
                    <input type="text" class="form-control" name="smtpServer" placeholder="如：smtp.example.com">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label required-field">SMTP端口</label>
                    <input type="number" class="form-control" name="smtpPort" value="587" min="1" max="65535">
                    <div class="form-text">常用端口：25, 465, 587</div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label required-field">发件人邮箱</label>
                    <input type="email" class="form-control" name="senderEmail" placeholder="<EMAIL>">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label required-field">邮箱密码</label>
                    <div class="password-field-container">
                        <input type="password" class="form-control" name="emailPassword">
                        <span class="password-toggle" onclick="togglePasswordVisibility('emailPassword')">
                            <i class="bi bi-eye"></i>
                        </span>
                    </div>
                    <div class="form-text">Gmail用户可能需要使用应用专用密码</div>
                </div>
            </div>
        </div>
        
        <div class="actions-container mt-4">
            <button type="button" class="btn btn-outline-primary me-2" id="testEmailBtn">
                <i class="bi bi-envelope-check"></i> 测试连接
            </button>
            <button type="button" class="btn btn-primary" id="saveEmailBtn">
                <i class="bi bi-check-circle"></i> 保存邮件设置
            </button>
        </div>
    </form>
    
    <div class="pagination-container">
        <button class="btn btn-outline-primary btn-sm" onclick="showSection('security-section')">
            <i class="bi bi-arrow-left"></i> 上一项
        </button>
        <button class="btn btn-outline-primary btn-sm" onclick="showSection('backup-section')">
            下一项 <i class="bi bi-arrow-right"></i>
        </button>
    </div>
</div>

<!-- 备份设置 -->
<div id="backup-section" class="settings-card hidden-section">
    <span class="badge bg-primary badge-info">3/4</span>
    <div class="setting-header">
        <div class="setting-icon">
            <i class="bi bi-clock-history"></i>
        </div>
        <div>
            <h5 class="setting-title">备份设置</h5>
            <p class="setting-description">配置数据备份策略和自动备份选项</p>
        </div>
    </div>
    
    <form id="backupForm">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">自动备份</label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" name="autoBackup">
                        <label class="form-check-label">启用自动备份</label>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">备份频率</label>
                    <select class="form-select" name="backupFrequency">
                        <option value="daily">每天</option>
                        <option value="weekly">每周</option>
                        <option value="monthly">每月</option>
                    </select>
                    <div class="form-text">自动备份将按指定频率在凌晨2点执行</div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label required-field">备份保留时间(天)</label>
                    <input type="number" class="form-control" name="backupRetention" value="30" min="1" max="365">
                    <div class="form-text">超过保留时间的备份将被自动删除</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label required-field">备份路径</label>
                    <div class="input-group">
                        <input type="text" class="form-control" name="backupPath" placeholder="D:\backup">
                        <button class="btn btn-outline-secondary" type="button" id="browseBackupPathBtn">
                            <i class="bi bi-folder"></i> 浏览...
                        </button>
                    </div>
                    <div class="form-text">请确保指定路径存在且有写入权限</div>
                </div>
            </div>
        </div>
        
        <div class="actions-container mt-4">
            <button type="button" class="btn btn-outline-primary me-2" id="executeBackupBtn">
                <i class="bi bi-download"></i> 立即备份
            </button>
            <button type="button" class="btn btn-primary" id="saveBackupBtn">
                <i class="bi bi-check-circle"></i> 保存备份设置
            </button>
        </div>
    </form>
    
    <div class="pagination-container">
        <button class="btn btn-outline-primary btn-sm" onclick="showSection('email-section')">
            <i class="bi bi-arrow-left"></i> 上一项
        </button>
        <button class="btn btn-outline-primary btn-sm" onclick="showSection('notification-section')">
            下一项 <i class="bi bi-arrow-right"></i>
        </button>
    </div>
</div>

<!-- 通知设置 -->
<div id="notification-section" class="settings-card hidden-section">
    <span class="badge bg-primary badge-info">4/4</span>
    <div class="setting-header">
        <div class="setting-icon">
            <i class="bi bi-bell"></i>
        </div>
        <div>
            <h5 class="setting-title">通知设置</h5>
            <p class="setting-description">配置系统通知和提醒选项</p>
        </div>
    </div>
    
    <form id="notificationForm">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">系统通知</label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" name="systemNotifications" checked>
                        <label class="form-check-label">启用系统通知</label>
                    </div>
                    <div class="form-text">系统消息将显示在用户界面上</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">邮件通知</label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" name="emailNotifications">
                        <label class="form-check-label">启用邮件通知</label>
                    </div>
                    <div class="form-text">重要事件将通过邮件发送给相关用户</div>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <label class="form-label">通知类型</label>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="notifyReservation" checked>
                        <label class="form-check-label">预约相关通知</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="notifyMaintenance" checked>
                        <label class="form-check-label">设备维护通知</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="notifySystem" checked>
                        <label class="form-check-label">系统更新通知</label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="actions-container mt-4">
            <button type="button" class="btn btn-primary" id="saveNotificationBtn">
                <i class="bi bi-check-circle"></i> 保存通知设置
            </button>
        </div>
    </form>
    
    <div class="pagination-container">
        <button class="btn btn-outline-primary btn-sm" onclick="showSection('backup-section')">
            <i class="bi bi-arrow-left"></i> 上一项
        </button>
        <div></div>
    </div>
</div> 