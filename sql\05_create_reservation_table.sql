/*
 * 预约表
*/
CREATE TABLE reservation (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_id INT,
    device_id INT,
    user_id INT NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    purpose VARCHAR(500) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT '待审核',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES room(id),
    FOREIGN KEY (device_id) REFERENCES device(id),
    FOREIG<PERSON> KEY (user_id) REFERENCES user(id),
    CHECK (
        (room_id IS NOT NULL AND device_id IS NULL) OR 
        (room_id IS NULL AND device_id IS NOT NULL)
    )
);
