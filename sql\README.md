# 数据库脚本说明

本目录包含创建数据库和初始化数据所需的SQL脚本。

## 脚本说明

1. `01_create_database.sql`
   - 创建数据库 `building_db`
   - 设置字符集为 utf8mb4

2. `02_create_tables.sql`
   - 创建用户表 `user`
   - 创建房间表 `room`
   - 创建使用记录表 `usage_record`

3. `04_create_reservation_table.sql`
   - 创建预约表 `reservation`

4. `03_insert_initial_data.sql`
   - 插入默认管理员账号
   - 插入测试房间数据

## 使用说明

1. 使用MySQL Workbench打开这些脚本
2. 按顺序执行脚本：
   - 先执行 `01_create_database.sql`
   - 然后执行 `02_create_tables.sql`
   - 最后执行 `03_insert_initial_data.sql`

## 数据库连接信息

- 服务器地址：127.0.0.1
- 端口：31306
- 用户名：root
- 密码：Yf.31306
- 数据库名：building_db

## 默认账号

- 用户名：admin
- 密码：admin123
