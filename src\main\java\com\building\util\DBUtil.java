package com.building.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Driver;
import java.util.Enumeration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据库工具类
 * 提供数据库连接和关闭的基本功能
 * 使用单例模式管理数据库连接
 * 
 * <AUTHOR>
 * @date 2024-03-04
 */
public class DBUtil {
    private static final Logger logger = LoggerFactory.getLogger(DBUtil.class);
    
    /**
     * 数据库连接URL
     * 包含数据库地址、端口、数据库名和连接参数
     */
    private static final String URL = "******************************************************************************************************************************************************";
    
    /**
     * 数据库用户名
     */
    private static final String USERNAME = "root";
    
    /**
     * 数据库密码
     */
    private static final String PASSWORD = "Yf.31306";
    
    /**
     * 静态初始化块
     * 加载MySQL驱动类
     */
    static {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            throw new RuntimeException("加载MySQL驱动失败", e);
        }
    }
    
    /**
     * 获取数据库连接
     * 使用配置的URL、用户名和密码创建新的数据库连接
     * 
     * @return 数据库连接对象
     * @throws SQLException 如果连接失败则抛出SQL异常
     */
    public static Connection getConnection() throws SQLException {
        try {
            return DriverManager.getConnection(URL, USERNAME, PASSWORD);
        } catch (SQLException e) {
            System.err.println("数据库连接失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    /**
     * 关闭数据库连接
     * 安全地关闭数据库连接，如果连接为null则忽略
     * 
     * @param conn 要关闭的数据库连接
     */
    public static void closeConnection(Connection conn) {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 清理所有数据库资源
     * 取消注册所有JDBC驱动程序，终止清理线程
     * 此方法应在应用程序关闭时调用
     */
    public static void cleanupResources() {
        // 取消注册所有的JDBC驱动
        Enumeration<Driver> drivers = DriverManager.getDrivers();
        while (drivers.hasMoreElements()) {
            Driver driver = drivers.nextElement();
            try {
                DriverManager.deregisterDriver(driver);
                logger.info("取消注册JDBC驱动程序: {}", driver);
            } catch (SQLException e) {
                logger.error("取消注册JDBC驱动程序失败: {}", driver, e);
            }
        }
        
        // 关闭MySQL的AbandonedConnectionCleanupThread
        try {
            Class<?> cleanupClass = Class.forName("com.mysql.cj.jdbc.AbandonedConnectionCleanupThread");
            try {
                java.lang.reflect.Method shutdownMethod = cleanupClass.getMethod("checkedShutdown");
                shutdownMethod.invoke(null);
                logger.info("成功关闭MySQL abandoned connection cleanup线程");
            } catch (Exception e) {
                // 尝试使用旧版本的方法
                try {
                    java.lang.reflect.Method shutdownMethod = cleanupClass.getMethod("shutdown");
                    shutdownMethod.invoke(null);
                    logger.info("成功关闭MySQL abandoned connection cleanup线程");
                } catch (Exception ex) {
                    logger.error("关闭MySQL abandoned connection cleanup线程失败", ex);
                }
            }
        } catch (ClassNotFoundException e) {
            logger.error("找不到MySQL AbandonedConnectionCleanupThread类", e);
        }
    }
} 