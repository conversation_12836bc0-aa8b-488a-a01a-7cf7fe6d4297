package com.building.service;

import com.building.model.SystemSettings;

/**
 * 系统设置服务接口
 */
public interface SystemService {
    
    /**
     * 获取当前系统设置
     * @return 系统设置对象
     */
    SystemSettings getCurrentSettings();
    
    /**
     * 更新安全设置
     * @param minPasswordLength 密码最小长度
     * @param requireUppercase 是否要求大写字母
     * @param requireNumbers 是否要求数字
     * @param requireSpecialChars 是否要求特殊字符
     * @param maxLoginAttempts 最大登录尝试次数
     * @return 是否更新成功
     */
    boolean updateSecuritySettings(int minPasswordLength, boolean requireUppercase, boolean requireNumbers, 
                                boolean requireSpecialChars, int maxLoginAttempts);
    
    /**
     * 更新邮件设置
     * @param smtpServer SMTP服务器地址
     * @param smtpPort SMTP端口
     * @param senderEmail 发件人邮箱
     * @param emailPassword 邮箱密码
     * @return 是否更新成功
     */
    boolean updateEmailSettings(String smtpServer, int smtpPort, String senderEmail, String emailPassword);
    
    /**
     * 更新备份设置
     * @param autoBackup 是否自动备份
     * @param backupFrequency 备份频率
     * @param backupRetention 备份保留天数
     * @param backupPath 备份路径
     * @return 是否更新成功
     */
    boolean updateBackupSettings(boolean autoBackup, String backupFrequency, int backupRetention, String backupPath);
    
    /**
     * 更新通知设置
     * @param systemNotifications 是否启用系统通知
     * @param emailNotifications 是否启用邮件通知
     * @param notifyReservation 是否通知预约相关事件
     * @param notifyMaintenance 是否通知设备维护事件
     * @param notifySystem 是否通知系统更新事件
     * @return 是否更新成功
     */
    boolean updateNotificationSettings(boolean systemNotifications, boolean emailNotifications, 
                                     boolean notifyReservation, boolean notifyMaintenance, boolean notifySystem);
    
    /**
     * 测试邮件连接
     * @param smtpServer SMTP服务器地址
     * @param smtpPort SMTP端口
     * @param senderEmail 发件人邮箱
     * @param emailPassword 邮箱密码
     * @return 是否连接成功
     */
    boolean testEmailConnection(String smtpServer, int smtpPort, String senderEmail, String emailPassword);
    
    /**
     * 执行立即备份
     * @return 是否备份成功
     */
    boolean executeBackup();
} 