<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid py-4">
    <!-- 返回按钮 -->
    <div class="mb-4">
        <a href="${pageContext.request.contextPath}/camera/detail?id=${camera.id}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left"></i> 返回摄像头详情
        </a>
    </div>
    
    <!-- 视频流容器 -->
    <div class="stream-container">
        <!-- 视频流信息 -->
        <div class="stream-info">
            <h5 class="mb-1">
                <span class="camera-status ${camera.status == 1 ? 'camera-online' : 'camera-offline'}"></span>
                ${camera.name}
            </h5>
            <p class="mb-0 text-white-50">
                <i class="bi bi-geo-alt me-1"></i> ${camera.location}
                <c:if test="${camera.room != null}">
                    <span class="mx-2">|</span>
                    <i class="bi bi-building me-1"></i> ${camera.room.roomNumber}
                </c:if>
            </p>
        </div>
        
        <!-- 视频流内容 -->
        <c:choose>
            <c:when test="${camera.status == 1}">
                <img id="streamImage" src="${pageContext.request.contextPath}/static/images/camera-stream.jpg" alt="摄像头视频流">
            </c:when>
            <c:otherwise>
                <div class="stream-overlay">
                    <i class="bi bi-camera-video-off mb-3 fs-1"></i>
                    <h4>摄像头离线</h4>
                    <p>请连接摄像头后查看视频流</p>
                    <button id="connectBtn" class="btn btn-primary mt-3" data-action="connect">
                        <i class="bi bi-wifi me-1"></i> 连接摄像头
                    </button>
                </div>
            </c:otherwise>
        </c:choose>
        
        <!-- 视频流控制 -->
        <c:if test="${camera.status == 1}">
            <div class="stream-controls">
                <button class="btn control-btn" data-action="pan_left">
                    <i class="bi bi-arrow-left"></i>
                </button>
                <button class="btn control-btn" data-action="tilt_up">
                    <i class="bi bi-arrow-up"></i>
                </button>
                <button class="btn control-btn" data-action="home">
                    <i class="bi bi-house"></i>
                </button>
                <button class="btn control-btn" data-action="tilt_down">
                    <i class="bi bi-arrow-down"></i>
                </button>
                <button class="btn control-btn" data-action="pan_right">
                    <i class="bi bi-arrow-right"></i>
                </button>
                <button class="btn control-btn" data-action="zoom_out">
                    <i class="bi bi-dash-lg"></i>
                </button>
                <button class="btn control-btn" data-action="zoom_in">
                    <i class="bi bi-plus-lg"></i>
                </button>
                <button class="btn btn-primary" id="fullscreenBtn">
                    <i class="bi bi-fullscreen"></i>
                </button>
                <button id="connectBtn" class="btn btn-danger" data-action="disconnect">
                    <i class="bi bi-wifi-off"></i>
                </button>
            </div>
        </c:if>
    </div>
</div>
