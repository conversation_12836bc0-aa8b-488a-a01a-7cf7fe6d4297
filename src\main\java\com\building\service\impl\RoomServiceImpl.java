package com.building.service.impl;

import java.util.List;
import com.building.dao.RoomDao;
import com.building.model.Room;
import com.building.service.RoomService;

/**
 * 房间服务实现类
 */
public class RoomServiceImpl implements RoomService {
    
    private RoomDao roomDao;
    
    public RoomServiceImpl() {
        roomDao = new RoomDao();
    }
    
    @Override
    public List<Room> getAllRooms() {
        return roomDao.getAllRooms();
    }
    
    @Override
    public Room getRoomById(int id) {
        if (id <= 0) {
            return null;
        }
        return roomDao.getRoomById(id);
    }
    
    @Override
    public int[] getRoomStatistics() {
        return roomDao.getRoomStats();
    }
    
    @Override
    public boolean updateRoomStatus(int id, String status) {
        if (id <= 0 || status == null || status.trim().isEmpty()) {
            return false;
        }
        Room room = getRoomById(id);
        if (room == null) {
            return false;
        }
        room.setStatus(status);
        return roomDao.updateRoom(room);
    }
} 