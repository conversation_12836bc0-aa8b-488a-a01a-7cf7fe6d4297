package com.building.model;

import java.util.List;

/**
 * 教室布局实体类
 * 用于存储教室内部布局信息
 */
public class RoomLayout {
    private Integer id;              // 布局ID
    private Integer roomId;          // 关联的教室ID
    private String layoutData;       // 布局数据（JSON格式）
    private String imageUrl;         // 布局图片URL
    private String createTime;       // 创建时间
    private String updateTime;       // 更新时间
    
    // 非持久化字段，用于前端展示
    private Room room;               // 关联的教室对象
    private List<DevicePosition> devicePositions;  // 设备位置列表
    
    /**
     * 设备位置内部类
     * 用于表示设备在教室中的位置
     */
    public static class DevicePosition {
        private Integer deviceId;    // 设备ID
        private String deviceName;   // 设备名称
        private String deviceType;   // 设备类型
        private Double posX;         // X坐标
        private Double posY;         // Y坐标
        private String status;       // 设备状态
        
        // Getters and Setters
        public Integer getDeviceId() {
            return deviceId;
        }
        
        public void setDeviceId(Integer deviceId) {
            this.deviceId = deviceId;
        }
        
        public String getDeviceName() {
            return deviceName;
        }
        
        public void setDeviceName(String deviceName) {
            this.deviceName = deviceName;
        }
        
        public String getDeviceType() {
            return deviceType;
        }
        
        public void setDeviceType(String deviceType) {
            this.deviceType = deviceType;
        }
        
        public Double getPosX() {
            return posX;
        }
        
        public void setPosX(Double posX) {
            this.posX = posX;
        }
        
        public Double getPosY() {
            return posY;
        }
        
        public void setPosY(Double posY) {
            this.posY = posY;
        }
        
        public String getStatus() {
            return status;
        }
        
        public void setStatus(String status) {
            this.status = status;
        }
    }
    
    // Getters and Setters
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public Integer getRoomId() {
        return roomId;
    }
    
    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }
    
    public String getLayoutData() {
        return layoutData;
    }
    
    public void setLayoutData(String layoutData) {
        this.layoutData = layoutData;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    public String getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
    
    public Room getRoom() {
        return room;
    }
    
    public void setRoom(Room room) {
        this.room = room;
    }
    
    public List<DevicePosition> getDevicePositions() {
        return devicePositions;
    }
    
    public void setDevicePositions(List<DevicePosition> devicePositions) {
        this.devicePositions = devicePositions;
    }
}
