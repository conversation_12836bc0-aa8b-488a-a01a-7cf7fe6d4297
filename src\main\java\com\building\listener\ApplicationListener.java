package com.building.listener;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.building.util.DBUtil;

/**
 * 应用生命周期监听器
 * 负责在应用启动和关闭时执行必要的初始化和清理工作
 */
@WebListener
public class ApplicationListener implements ServletContextListener {
    private static final Logger logger = LoggerFactory.getLogger(ApplicationListener.class);

    /**
     * 应用初始化时调用
     */
    @Override
    public void contextInitialized(ServletContextEvent sce) {
        logger.info("应用程序正在启动...");
    }

    /**
     * 应用关闭时调用
     * 清理所有数据库资源和其他需要关闭的资源
     */
    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        logger.info("应用程序正在关闭，开始清理资源...");
        
        // 清理数据库资源
        try {
            DBUtil.cleanupResources();
            logger.info("数据库资源清理完成");
        } catch (Exception e) {
            logger.error("清理数据库资源时发生错误", e);
        }
        
        logger.info("应用程序关闭，所有资源已清理");
    }
} 