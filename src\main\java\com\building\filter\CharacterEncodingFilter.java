package com.building.filter;

import java.io.IOException;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;

/**
 * 字符编码过滤器
 * 统一处理请求和响应的字符编码
 * 支持通过web.xml配置编码方式和是否强制设置
 * 
 * <AUTHOR>
 * @date 2024-03-04
 */
@WebFilter("/*")
public class CharacterEncodingFilter implements Filter {
    /**
     * 字符编码方式
     * 默认值：UTF-8
     */
    private String encoding;
    
    /**
     * 是否强制设置编码
     * 如果为true，则覆盖已设置的编码
     * 如果为false，则只在未设置编码时设置
     */
    private boolean forceEncoding;
    
    /**
     * 过滤器初始化
     * 从web.xml中读取配置参数
     * 
     * @param filterConfig 过滤器配置对象
     * @throws ServletException 如果初始化失败
     */
    public void init(FilterConfig filterConfig) throws ServletException {
        encoding = filterConfig.getInitParameter("encoding");
        forceEncoding = Boolean.parseBoolean(filterConfig.getInitParameter("forceEncoding"));
    }
    
    /**
     * 执行过滤操作
     * 设置请求和响应的字符编码
     * 
     * @param request 请求对象
     * @param response 响应对象
     * @param chain 过滤器链
     * @throws IOException 如果发生I/O错误
     * @throws ServletException 如果处理请求时发生错误
     */
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        if (encoding != null && (forceEncoding || request.getCharacterEncoding() == null)) {
            request.setCharacterEncoding(encoding);
            response.setCharacterEncoding(encoding);
        }
        chain.doFilter(request, response);
    }
    
    /**
     * 过滤器销毁
     * 清理过滤器占用的资源
     */
    public void destroy() {
        // 清理资源
    }
} 