<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4">
    <h1 class="h2"><i class="bi bi-speedometer2 me-2 text-primary"></i>控制面板</h1>
    <div class="btn-toolbar">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshDashboard()">
                <i class="bi bi-arrow-clockwise"></i> 刷新数据
            </button>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="bi bi-calendar3"></i> 时间范围
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="#">今天</a></li>
                <li><a class="dropdown-item" href="#">本周</a></li>
                <li><a class="dropdown-item" href="#">本月</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#">全部</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #4e73df, #224abe);">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1 text-white-50">房间总数</h6>
                        <h2 class="mt-3 mb-0 text-white fw-bold" data-stat="total">0</h2>
                        <p class="text-white-50 mt-2 mb-0"><i class="bi bi-arrow-up-right"></i> 较上月增长 5%</p>
                    </div>
                    <div class="stats-icon-container bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="bi bi-building stats-icon text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #1cc88a, #13855c);">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1 text-white-50">已使用房间</h6>
                        <h2 class="mt-3 mb-0 text-white fw-bold" data-stat="used">0</h2>
                        <p class="text-white-50 mt-2 mb-0"><i class="bi bi-arrow-up-right"></i> 使用率 65%</p>
                    </div>
                    <div class="stats-icon-container bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="bi bi-check-circle stats-icon text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #f6c23e, #dda20a);">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1 text-white-50">空闲房间</h6>
                        <h2 class="mt-3 mb-0 text-white fw-bold" data-stat="free">0</h2>
                        <p class="text-white-50 mt-2 mb-0"><i class="bi bi-arrow-down-right"></i> 空闲率 35%</p>
                    </div>
                    <div class="stats-icon-container bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="bi bi-circle stats-icon text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #e74a3b, #c0392b);">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1 text-white-50">楼层布局图</h6>
                        <h2 class="mt-3 mb-0 text-white fw-bold">1F</h2>
                        <p class="text-white-50 mt-2 mb-0"><i class="bi bi-eye"></i> 查看详细布局</p>
                    </div>
                    <div class="stats-icon-container bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="bi bi-map stats-icon text-white"></i>
                    </div>
                </div>
                <a href="${pageContext.request.contextPath}/layout/floor1" class="stretched-link"></a>
            </div>
        </div>
    </div>
</div>

<!-- 详细统计 -->
<div class="row mb-4">
    <!-- 楼层分布 -->
    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-0 pt-4 pb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 fw-bold"><i class="bi bi-bar-chart-fill text-primary me-2"></i>楼层分布</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-download me-2"></i>导出数据</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-arrow-repeat me-2"></i>刷新</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body px-4 pb-4">
                <canvas id="floorChart" height="250"></canvas>
            </div>
        </div>
    </div>
    <!-- 房间类型分布 -->
    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-0 pt-4 pb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 fw-bold"><i class="bi bi-pie-chart-fill text-success me-2"></i>房间类型分布</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-download me-2"></i>导出数据</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-arrow-repeat me-2"></i>刷新</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body px-4 pb-4">
                <canvas id="typeChart" height="250"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 pt-4 pb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 fw-bold"><i class="bi bi-activity text-danger me-2"></i>最近活动</h5>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-filter"></i> 筛选
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-download"></i> 导出
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th class="border-0 ps-4">时间</th>
                                <th class="border-0">房间号</th>
                                <th class="border-0">操作类型</th>
                                <th class="border-0">操作人</th>
                                <th class="border-0 text-end pe-4">详情</th>
                            </tr>
                        </thead>
                        <tbody id="activityList">
                            <!-- 示例数据，实际应该由JavaScript动态加载 -->
                            <tr>
                                <td class="ps-4">2023-06-11 10:30</td>
                                <td><span class="badge bg-light text-dark">301</span></td>
                                <td><span class="badge bg-success">预约</span></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                            <span>张</span>
                                        </div>
                                        <span>张三</span>
                                    </div>
                                </td>
                                <td class="text-end pe-4">
                                    <button class="btn btn-sm btn-light rounded-pill">
                                        <i class="bi bi-eye"></i> 查看
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="ps-4">2023-06-11 09:15</td>
                                <td><span class="badge bg-light text-dark">205</span></td>
                                <td><span class="badge bg-warning">修改</span></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-success text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                            <span>李</span>
                                        </div>
                                        <span>李四</span>
                                    </div>
                                </td>
                                <td class="text-end pe-4">
                                    <button class="btn btn-sm btn-light rounded-pill">
                                        <i class="bi bi-eye"></i> 查看
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="ps-4">2023-06-10 16:45</td>
                                <td><span class="badge bg-light text-dark">102</span></td>
                                <td><span class="badge bg-danger">取消</span></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-warning text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                            <span>王</span>
                                        </div>
                                        <span>王五</span>
                                    </div>
                                </td>
                                <td class="text-end pe-4">
                                    <button class="btn btn-sm btn-light rounded-pill">
                                        <i class="bi bi-eye"></i> 查看
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-between align-items-center p-3 border-top">
                    <span class="text-muted">显示 1-3 条，共 24 条</span>
                    <nav>
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled"><a class="page-link" href="#">上一页</a></li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item"><a class="page-link" href="#">下一页</a></li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshDashboard() {
    // 刷新统计数据
    fetch('${pageContext.request.contextPath}/room/stats')
        .then(response => response.json())
        .then(data => {
            document.querySelector('[data-stat=total]').textContent = data.total;
            document.querySelector('[data-stat=used]').textContent = data.used;
            document.querySelector('[data-stat=free]').textContent = data.free;

            // 显示刷新成功提示
            const toast = new bootstrap.Toast(document.getElementById('refreshToast'));
            toast.show();
        })
        .catch(error => console.error('Error:', error));
}
</script>

<!-- 刷新提示 Toast -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="refreshToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="bi bi-check-circle me-2"></i> 数据已成功刷新！
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>