package com.building.servlet;

import java.io.IOException;
import java.io.PrintWriter;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.model.ApiResponse;
import com.building.model.User;
import com.building.service.ReservationService;
import com.building.service.impl.ReservationServiceImpl;
import com.building.util.JsonUtil;

/**
 * 预约更新控制器
 * 处理预约状态的更新
 */
@WebServlet("/reservation/update")
public class ReservationUpdateServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    private ReservationService reservationService;
    
    @Override
    public void init() throws ServletException {
        reservationService = new ReservationServiceImpl();
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 设置响应类型
        response.setContentType("application/json;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        // 检查用户是否登录
        HttpSession session = request.getSession();
        User user = (User) session.getAttribute("user");
        if (user == null) {
            ApiResponse apiResponse = new ApiResponse(false, "用户未登录");
            out.print(JsonUtil.toJson(apiResponse));
            return;
        }
        
        // 检查用户权限
        if (!"admin".equals(user.getRole())) {
            ApiResponse apiResponse = new ApiResponse(false, "权限不足，只有管理员可以更新预约状态");
            out.print(JsonUtil.toJson(apiResponse));
            return;
        }
        
        // 获取表单数据
        String idStr = request.getParameter("id");
        String status = request.getParameter("status");
        
        // 验证数据
        if (idStr == null || idStr.trim().isEmpty() || 
            status == null || status.trim().isEmpty()) {
            ApiResponse apiResponse = new ApiResponse(false, "请填写所有必填字段");
            out.print(JsonUtil.toJson(apiResponse));
            return;
        }
        
        try {
            int id = Integer.parseInt(idStr);
            
            // 更新预约状态
            boolean success = reservationService.updateReservationStatus(id, status);
            
            if (success) {
                ApiResponse apiResponse = new ApiResponse(true, "预约状态更新成功");
                out.print(JsonUtil.toJson(apiResponse));
            } else {
                ApiResponse apiResponse = new ApiResponse(false, "预约状态更新失败");
                out.print(JsonUtil.toJson(apiResponse));
            }
        } catch (NumberFormatException e) {
            ApiResponse apiResponse = new ApiResponse(false, "预约ID格式不正确");
            out.print(JsonUtil.toJson(apiResponse));
        } catch (Exception e) {
            e.printStackTrace();
            ApiResponse apiResponse = new ApiResponse(false, "系统错误：" + e.getMessage());
            out.print(JsonUtil.toJson(apiResponse));
        }
    }
} 