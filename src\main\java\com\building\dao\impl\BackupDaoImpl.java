package com.building.dao.impl;

import com.building.dao.BackupDao;
import com.building.util.DBUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库备份DAO实现类
 */
@Repository
public class BackupDaoImpl implements BackupDao {
    private static final Logger logger = LoggerFactory.getLogger(BackupDaoImpl.class);
    
    @Override
    public boolean logBackup(java.util.Date backupTime, String backupFile, String backupPath, 
                          long backupSize, String backupType, String status, 
                          String errorMessage, int duration) {
        Connection conn = null;
        PreparedStatement stmt = null;
        
        try {
            conn = DBUtil.getConnection();
            String sql = "INSERT INTO backup_logs (backup_time, backup_file, backup_path, backup_size, " +
                    "backup_type, status, error_message, duration) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            stmt = conn.prepareStatement(sql);
            stmt.setTimestamp(1, new Timestamp(backupTime.getTime()));
            stmt.setString(2, backupFile);
            stmt.setString(3, backupPath);
            stmt.setLong(4, backupSize);
            stmt.setString(5, backupType);
            stmt.setString(6, status);
            stmt.setString(7, errorMessage);
            stmt.setInt(8, duration);
            
            int result = stmt.executeUpdate();
            return result > 0;
        } catch (SQLException e) {
            logger.error("记录备份日志失败", e);
            return false;
        } finally {
            closeResources(conn, stmt, null);
        }
    }
    
    @Override
    public List<Map<String, Object>> getBackupLogs(int limit) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<Map<String, Object>> logs = new ArrayList<>();
        
        try {
            conn = DBUtil.getConnection();
            String sql = "SELECT * FROM backup_logs ORDER BY backup_time DESC LIMIT ?";
            
            stmt = conn.prepareStatement(sql);
            stmt.setInt(1, limit);
            
            rs = stmt.executeQuery();
            
            while (rs.next()) {
                Map<String, Object> log = new HashMap<>();
                log.put("id", rs.getInt("id"));
                log.put("backupTime", rs.getTimestamp("backup_time"));
                log.put("backupFile", rs.getString("backup_file"));
                log.put("backupPath", rs.getString("backup_path"));
                log.put("backupSize", rs.getLong("backup_size"));
                log.put("backupType", rs.getString("backup_type"));
                log.put("status", rs.getString("status"));
                log.put("errorMessage", rs.getString("error_message"));
                log.put("duration", rs.getInt("duration"));
                log.put("createTime", rs.getTimestamp("create_time"));
                
                logs.add(log);
            }
            
            return logs;
        } catch (SQLException e) {
            logger.error("获取备份日志失败", e);
            return Collections.emptyList();
        } finally {
            closeResources(conn, stmt, rs);
        }
    }
    
    @Override
    public int cleanupBackupLogs(int retentionDays) {
        Connection conn = null;
        PreparedStatement stmt = null;
        
        try {
            conn = DBUtil.getConnection();
            String sql = "DELETE FROM backup_logs WHERE backup_time < DATE_SUB(NOW(), INTERVAL ? DAY)";
            
            stmt = conn.prepareStatement(sql);
            stmt.setInt(1, retentionDays);
            
            return stmt.executeUpdate();
        } catch (SQLException e) {
            logger.error("清理备份日志失败", e);
            return 0;
        } finally {
            closeResources(conn, stmt, null);
        }
    }
    
    @Override
    public List<Map<String, Object>> getBackupSchedules() {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<Map<String, Object>> schedules = new ArrayList<>();
        
        try {
            conn = DBUtil.getConnection();
            String sql = "SELECT * FROM backup_schedule ORDER BY id";
            
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            
            while (rs.next()) {
                Map<String, Object> schedule = new HashMap<>();
                schedule.put("id", rs.getInt("id"));
                schedule.put("scheduleName", rs.getString("schedule_name"));
                schedule.put("active", rs.getBoolean("active"));
                schedule.put("scheduleType", rs.getString("schedule_type"));
                schedule.put("dayOfWeek", rs.getObject("day_of_week"));
                schedule.put("dayOfMonth", rs.getObject("day_of_month"));
                schedule.put("hour", rs.getInt("hour"));
                schedule.put("minute", rs.getInt("minute"));
                schedule.put("retentionDays", rs.getInt("retention_days"));
                schedule.put("backupPath", rs.getString("backup_path"));
                schedule.put("lastRunTime", rs.getTimestamp("last_run_time"));
                schedule.put("nextRunTime", rs.getTimestamp("next_run_time"));
                schedule.put("createTime", rs.getTimestamp("create_time"));
                schedule.put("updateTime", rs.getTimestamp("update_time"));
                
                schedules.add(schedule);
            }
            
            return schedules;
        } catch (SQLException e) {
            logger.error("获取备份调度任务列表失败", e);
            return Collections.emptyList();
        } finally {
            closeResources(conn, stmt, rs);
        }
    }
    
    @Override
    public Map<String, Object> getBackupScheduleById(int scheduleId) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            String sql = "SELECT * FROM backup_schedule WHERE id = ?";
            
            stmt = conn.prepareStatement(sql);
            stmt.setInt(1, scheduleId);
            
            rs = stmt.executeQuery();
            
            if (rs.next()) {
                Map<String, Object> schedule = new HashMap<>();
                schedule.put("id", rs.getInt("id"));
                schedule.put("scheduleName", rs.getString("schedule_name"));
                schedule.put("active", rs.getBoolean("active"));
                schedule.put("scheduleType", rs.getString("schedule_type"));
                schedule.put("dayOfWeek", rs.getObject("day_of_week"));
                schedule.put("dayOfMonth", rs.getObject("day_of_month"));
                schedule.put("hour", rs.getInt("hour"));
                schedule.put("minute", rs.getInt("minute"));
                schedule.put("retentionDays", rs.getInt("retention_days"));
                schedule.put("backupPath", rs.getString("backup_path"));
                schedule.put("lastRunTime", rs.getTimestamp("last_run_time"));
                schedule.put("nextRunTime", rs.getTimestamp("next_run_time"));
                schedule.put("createTime", rs.getTimestamp("create_time"));
                schedule.put("updateTime", rs.getTimestamp("update_time"));
                
                return schedule;
            }
            
            return null;
        } catch (SQLException e) {
            logger.error("获取备份调度任务详情失败", e);
            return null;
        } finally {
            closeResources(conn, stmt, rs);
        }
    }
    
    @Override
    public boolean updateSchedule(int scheduleId, boolean active, String scheduleType,
                               Integer dayOfWeek, Integer dayOfMonth, int hour,
                               int minute, int retentionDays, String backupPath) {
        Connection conn = null;
        PreparedStatement stmt = null;
        
        try {
            conn = DBUtil.getConnection();
            String sql = "UPDATE backup_schedule SET active = ?, schedule_type = ?, " +
                    "day_of_week = ?, day_of_month = ?, hour = ?, minute = ?, " +
                    "retention_days = ?, backup_path = ? WHERE id = ?";
            
            stmt = conn.prepareStatement(sql);
            stmt.setBoolean(1, active);
            stmt.setString(2, scheduleType);
            
            if (dayOfWeek != null) {
                stmt.setInt(3, dayOfWeek);
            } else {
                stmt.setNull(3, Types.INTEGER);
            }
            
            if (dayOfMonth != null) {
                stmt.setInt(4, dayOfMonth);
            } else {
                stmt.setNull(4, Types.INTEGER);
            }
            
            stmt.setInt(5, hour);
            stmt.setInt(6, minute);
            stmt.setInt(7, retentionDays);
            stmt.setString(8, backupPath);
            stmt.setInt(9, scheduleId);
            
            int result = stmt.executeUpdate();
            return result > 0;
        } catch (SQLException e) {
            logger.error("更新备份调度任务失败", e);
            return false;
        } finally {
            closeResources(conn, stmt, null);
        }
    }
    
    @Override
    public int addSchedule(String scheduleName, boolean active, String scheduleType,
                        Integer dayOfWeek, Integer dayOfMonth, int hour,
                        int minute, int retentionDays, String backupPath) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            String sql = "INSERT INTO backup_schedule (schedule_name, active, schedule_type, " +
                    "day_of_week, day_of_month, hour, minute, retention_days, backup_path) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            stmt.setString(1, scheduleName);
            stmt.setBoolean(2, active);
            stmt.setString(3, scheduleType);
            
            if (dayOfWeek != null) {
                stmt.setInt(4, dayOfWeek);
            } else {
                stmt.setNull(4, Types.INTEGER);
            }
            
            if (dayOfMonth != null) {
                stmt.setInt(5, dayOfMonth);
            } else {
                stmt.setNull(5, Types.INTEGER);
            }
            
            stmt.setInt(6, hour);
            stmt.setInt(7, minute);
            stmt.setInt(8, retentionDays);
            stmt.setString(9, backupPath);
            
            int result = stmt.executeUpdate();
            
            if (result > 0) {
                rs = stmt.getGeneratedKeys();
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
            
            return -1;
        } catch (SQLException e) {
            logger.error("添加备份调度任务失败", e);
            return -1;
        } finally {
            closeResources(conn, stmt, rs);
        }
    }
    
    @Override
    public boolean deleteSchedule(int scheduleId) {
        Connection conn = null;
        PreparedStatement stmt = null;
        
        try {
            conn = DBUtil.getConnection();
            String sql = "DELETE FROM backup_schedule WHERE id = ?";
            
            stmt = conn.prepareStatement(sql);
            stmt.setInt(1, scheduleId);
            
            int result = stmt.executeUpdate();
            return result > 0;
        } catch (SQLException e) {
            logger.error("删除备份调度任务失败", e);
            return false;
        } finally {
            closeResources(conn, stmt, null);
        }
    }
    
    @Override
    public boolean updateScheduleRunTime(int scheduleId, java.util.Date lastRunTime, java.util.Date nextRunTime) {
        Connection conn = null;
        PreparedStatement stmt = null;
        
        try {
            conn = DBUtil.getConnection();
            String sql = "UPDATE backup_schedule SET last_run_time = ?, next_run_time = ? WHERE id = ?";
            
            stmt = conn.prepareStatement(sql);
            
            if (lastRunTime != null) {
                stmt.setTimestamp(1, new Timestamp(lastRunTime.getTime()));
            } else {
                stmt.setNull(1, Types.TIMESTAMP);
            }
            
            if (nextRunTime != null) {
                stmt.setTimestamp(2, new Timestamp(nextRunTime.getTime()));
            } else {
                stmt.setNull(2, Types.TIMESTAMP);
            }
            
            stmt.setInt(3, scheduleId);
            
            int result = stmt.executeUpdate();
            return result > 0;
        } catch (SQLException e) {
            logger.error("更新调度任务运行时间失败", e);
            return false;
        } finally {
            closeResources(conn, stmt, null);
        }
    }
    
    @Override
    public List<Map<String, Object>> getDueSchedules(java.util.Date currentTime) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<Map<String, Object>> schedules = new ArrayList<>();
        
        try {
            conn = DBUtil.getConnection();
            String sql = "SELECT * FROM backup_schedule WHERE active = 1 AND next_run_time <= ?";
            
            stmt = conn.prepareStatement(sql);
            stmt.setTimestamp(1, new Timestamp(currentTime.getTime()));
            
            rs = stmt.executeQuery();
            
            while (rs.next()) {
                Map<String, Object> schedule = new HashMap<>();
                schedule.put("id", rs.getInt("id"));
                schedule.put("scheduleName", rs.getString("schedule_name"));
                schedule.put("scheduleType", rs.getString("schedule_type"));
                schedule.put("dayOfWeek", rs.getObject("day_of_week"));
                schedule.put("dayOfMonth", rs.getObject("day_of_month"));
                schedule.put("hour", rs.getInt("hour"));
                schedule.put("minute", rs.getInt("minute"));
                schedule.put("retentionDays", rs.getInt("retention_days"));
                schedule.put("backupPath", rs.getString("backup_path"));
                
                schedules.add(schedule);
            }
            
            return schedules;
        } catch (SQLException e) {
            logger.error("获取到期备份任务失败", e);
            return Collections.emptyList();
        } finally {
            closeResources(conn, stmt, rs);
        }
    }
    
    /**
     * 关闭数据库资源
     */
    private void closeResources(Connection conn, Statement stmt, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                logger.error("关闭ResultSet失败", e);
            }
        }
        
        if (stmt != null) {
            try {
                stmt.close();
            } catch (SQLException e) {
                logger.error("关闭Statement失败", e);
            }
        }
        
        if (conn != null) {
            DBUtil.closeConnection(conn);
        }
    }
} 