<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-primary fw-bold">
                        <i class="bi bi-building me-2"></i>楼层布局
                    </h2>
                    <p class="text-muted">查看各楼层教室布局和状态，管理房间资源</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 楼层选择器 -->
    <div class="floor-selector">
        <div class="floor-selector-inner">
            <c:forEach items="${floors}" var="floor">
                <button type="button" class="floor-btn ${floor == currentFloor ? 'active' : ''}" data-floor="${floor}">
                    ${floor}楼
                </button>
            </c:forEach>
        </div>
    </div>

    <!-- 楼层统计卡片 -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-3">
            <div class="floor-stats-card">
                <div class="floor-stats-header">
                    <h5 class="mb-0">
                        <i class="bi bi-bar-chart-line me-2 text-primary"></i>${currentFloor}楼使用情况
                    </h5>
                </div>
                <div class="floor-stats-body">
                    <div class="row">
                        <div class="col-md-7">
                            <div class="stats-row">
                                <div class="stats-item">
                                    <div class="stats-value" id="totalRooms">
                                        <c:set var="totalRooms" value="${rooms.size()}" />
                                        ${totalRooms}
                                    </div>
                                    <div class="stats-label">总房间数</div>
                                </div>
                                <div class="stats-item available">
                                    <div class="stats-value" id="availableRooms">
                                        <c:set var="availableRooms" value="0" />
                                        <c:forEach items="${rooms}" var="room">
                                            <c:if test="${room.status != '使用中' && room.status != '维护中'}">
                                                <c:set var="availableRooms" value="${availableRooms + 1}" />
                                            </c:if>
                                        </c:forEach>
                                        ${availableRooms}
                                    </div>
                                    <div class="stats-label">空闲房间</div>
                                </div>
                                <div class="stats-item occupied">
                                    <div class="stats-value" id="occupiedRooms">
                                        <c:set var="occupiedRooms" value="0" />
                                        <c:forEach items="${rooms}" var="room">
                                            <c:if test="${room.status == '使用中'}">
                                                <c:set var="occupiedRooms" value="${occupiedRooms + 1}" />
                                            </c:if>
                                        </c:forEach>
                                        ${occupiedRooms}
                                    </div>
                                    <div class="stats-label">使用中</div>
                                </div>
                                <div class="stats-item maintenance">
                                    <div class="stats-value" id="maintenanceRooms">
                                        <c:set var="maintenanceRooms" value="0" />
                                        <c:forEach items="${rooms}" var="room">
                                            <c:if test="${room.status == '维护中'}">
                                                <c:set var="maintenanceRooms" value="${maintenanceRooms + 1}" />
                                            </c:if>
                                        </c:forEach>
                                        ${maintenanceRooms}
                                    </div>
                                    <div class="stats-label">维护中</div>
                                </div>
                            </div>
                            <div class="stats-row mt-4">
                                <div class="stats-item">
                                    <div class="stats-value">
                                        <c:set var="totalCapacity" value="0" />
                                        <c:forEach items="${rooms}" var="room">
                                            <c:set var="totalCapacity" value="${totalCapacity + room.capacity}" />
                                        </c:forEach>
                                        ${totalCapacity}
                                    </div>
                                    <div class="stats-label">总容量</div>
                                </div>
                                <div class="stats-item">
                                    <div class="stats-value">
                                        <c:set var="totalArea" value="0" />
                                        <c:forEach items="${rooms}" var="room">
                                            <c:set var="totalArea" value="${totalArea + room.area}" />
                                        </c:forEach>
                                        ${totalArea}
                                    </div>
                                    <div class="stats-label">总面积(m²)</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div style="height: 200px;">
                                <canvas id="floorStatsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-3">
            <div class="room-list-card h-100">
                <div class="room-list-header">
                    <h5 class="mb-0">
                        <i class="bi bi-filter-circle me-2 text-primary"></i>筛选
                    </h5>
                </div>
                <div class="room-list-body">
                    <div class="mb-3">
                        <label for="roomSearchInput" class="form-label">搜索房间</label>
                        <div class="input-group">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="bi bi-search text-muted"></i>
                            </span>
                            <input type="text" class="form-control border-start-0 ps-0" id="roomSearchInput" placeholder="输入房间号或类型...">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">房间状态</label>
                        <div class="d-flex flex-wrap">
                            <button class="btn btn-sm btn-outline-secondary me-2 mb-2 room-filter-btn active" data-filter="all">全部</button>
                            <button class="btn btn-sm btn-outline-success me-2 mb-2 room-filter-btn" data-filter="">空闲</button>
                            <button class="btn btn-sm btn-outline-danger me-2 mb-2 room-filter-btn" data-filter="occupied">使用中</button>
                            <button class="btn btn-sm btn-outline-warning me-2 mb-2 room-filter-btn" data-filter="maintenance">维护中</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 楼层布局图 -->
    <div class="floor-layout-container mb-4">
        <div class="floor-layout" id="floorLayout">
            <!-- 楼层布局标题 -->
            <div class="floor-layout-header">
                <h5 class="mb-0">${currentFloor}楼平面图</h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-arrows-fullscreen"></i> 全屏
                    </button>
                </div>
            </div>

            <!-- 图例 -->
            <div class="floor-layout-legend">
                <div class="legend-item">
                    <div class="legend-color legend-available"></div>
                    <span>空闲</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-occupied"></div>
                    <span>使用中</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-maintenance"></div>
                    <span>维护中</span>
                </div>
            </div>

            <!-- 房间 -->
            <c:forEach items="${rooms}" var="room">
                <div class="room-item ${room.status == '使用中' ? 'occupied' : room.status == '维护中' ? 'maintenance' : ''}"
                    data-id="${room.id}"
                    style="left: ${room.position != null ? room.position.split(',')[0] : '10'}px;
                           top: ${room.position != null ? room.position.split(',')[1] : '10'}px;
                           width: ${room.width != null ? room.width / 10 : 100}px;
                           height: ${room.length != null ? room.length / 10 : 80}px;">
                    <div class="room-number">
                        ${room.roomNumber}
                        <span class="status-badge ${room.status == '使用中' ? 'occupied' : room.status == '维护中' ? 'maintenance' : 'available'}">
                            ${room.status == '使用中' ? '使用中' : room.status == '维护中' ? '维护中' : '空闲'}
                        </span>
                    </div>
                    <div class="room-type">${room.roomType}</div>

                    <!-- 设备图标 -->
                    <div class="room-devices">
                        <c:set var="deviceCount" value="${room.deviceCount != null ? room.deviceCount : 0}" />
                        <c:set var="onlineDeviceCount" value="${room.onlineDeviceCount != null ? room.onlineDeviceCount : 0}" />

                        <c:if test="${deviceCount > 0}">
                            <div class="device-icon ${onlineDeviceCount > 0 ? 'online' : 'offline'}" title="设备">
                                <i class="bi bi-pc-display"></i>
                            </div>
                        </c:if>

                        <c:if test="${room.hasCameraDevice}">
                            <div class="device-icon ${room.cameraOnline ? 'online' : 'offline'}" title="摄像头">
                                <i class="bi bi-camera-video"></i>
                            </div>
                        </c:if>
                    </div>

                    <div class="room-status">
                        <small>${room.area}m² | ${room.capacity}人</small>
                        <small>
                            <i class="bi bi-people"></i>
                            <c:choose>
                                <c:when test="${room.currentPersonCount != null}">
                                    ${room.currentPersonCount}人
                                </c:when>
                                <c:otherwise>
                                    未知
                                </c:otherwise>
                            </c:choose>
                        </small>
                    </div>
                </div>
            </c:forEach>

            <div class="floor-layout-footer">
                产教融合大楼 ${currentFloor}楼 | 共${rooms.size()}间教室 | 总面积: ${totalArea}m²
            </div>
        </div>
    </div>

    <!-- 房间列表 -->
    <div class="room-list-card mb-4">
        <div class="room-list-header">
            <h5 class="mb-0">
                <i class="bi bi-list-ul me-2 text-primary"></i>${currentFloor}楼房间列表
            </h5>
            <div class="d-flex">
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control" placeholder="搜索房间..." id="tableSearchInput">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="room-list-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>房间号</th>
                            <th>类型</th>
                            <th>面积(m²)</th>
                            <th>容量</th>
                            <th>设备数</th>
                            <th>当前人数</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach items="${rooms}" var="room">
                            <tr class="room-list-item">
                                <td class="room-list-number">${room.roomNumber}</td>
                                <td class="room-list-type">${room.roomType}</td>
                                <td>${room.area}</td>
                                <td>${room.capacity}</td>
                                <td>
                                    <c:set var="deviceCount" value="${room.deviceCount != null ? room.deviceCount : 0}" />
                                    <c:set var="onlineDeviceCount" value="${room.onlineDeviceCount != null ? room.onlineDeviceCount : 0}" />
                                    <span class="badge bg-${onlineDeviceCount == deviceCount ? 'success' : onlineDeviceCount > 0 ? 'warning' : 'secondary'} rounded-pill">
                                        ${onlineDeviceCount}/${deviceCount}
                                    </span>
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${room.currentPersonCount != null}">
                                            <span class="badge bg-info rounded-pill">${room.currentPersonCount}人</span>
                                        </c:when>
                                        <c:otherwise>
                                            <span class="badge bg-secondary rounded-pill">未知</span>
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td class="room-list-status">
                                    <span class="status-badge ${room.status == '使用中' ? 'occupied' : room.status == '维护中' ? 'maintenance' : 'available'}">
                                        ${room.status}
                                    </span>
                                </td>
                                <td>
                                    <a href="${pageContext.request.contextPath}/room/detail?id=${room.id}" class="btn btn-sm btn-outline-primary rounded-pill">
                                        <i class="bi bi-eye"></i> 查看
                                    </a>
                                </td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
