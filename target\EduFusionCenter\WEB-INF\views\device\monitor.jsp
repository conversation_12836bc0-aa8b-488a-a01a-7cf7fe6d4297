<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="设备监控" />
    <jsp:param name="content" value="/WEB-INF/views/device/monitor-content.jsp" />
    <jsp:param name="additionalStyles" value="
        .device-card {
            transition: transform 0.2s;
            margin-bottom: 20px;
        }
        .device-card:hover {
            transform: translateY(-5px);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-online {
            background-color: #28a745;
        }
        .status-offline {
            background-color: #dc3545;
        }
        .parameter-chart {
            height: 250px;
        }
        .alert-card {
            border-left: 4px solid #dc3545;
        }
        /* 设备参数显示样式优化 */
        .device-parameter {
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
            padding: 5px 0;
        }
        .device-parameter i {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }
        .temperature-value, .humidity-value, .power-value {
            font-size: 1rem;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
            text-align: center;
            margin-bottom: 3px;
        }
        .device-parameter small {
            font-size: 0.75rem;
            color: #6c757d;
        }
    " />
    <jsp:param name="scripts" value="
        <script src='https://cdn.jsdelivr.net/npm/chart.js'></script>
        <script>
            // 全局变量，存储图表实例
            let temperatureChart = null;
            let humidityChart = null;
            let powerChart = null;
            let selectedDeviceId = null;
            let chartUpdateInterval = null;

            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                // 初始化图表容器
                initChartContainers();

                // 设置设备状态定时刷新
                setInterval(refreshDeviceStatus, 10000);

                // 绑定设备选择器事件
                document.getElementById('deviceSelector').addEventListener('change', function() {
                    const deviceId = this.value;
                    if (deviceId) {
                        loadDeviceCharts(deviceId);
                    } else {
                        hideCharts();
                    }
                });

                // 绑定设备详情按钮点击事件
                document.querySelectorAll('.device-detail-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const deviceId = this.getAttribute('data-device-id');
                        loadDeviceDetail(deviceId);
                    });
                });

                // 绑定解决告警按钮点击事件
                document.querySelectorAll('.resolve-alert-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const alertId = this.getAttribute('data-alert-id');
                        resolveAlert(alertId);
                    });
                });

                // 绑定设备控制按钮点击事件
                document.querySelectorAll('.device-control-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const deviceId = this.getAttribute('data-device-id');
                        const action = this.getAttribute('data-action');
                        controlDevice(deviceId, action);
                    });
                });
            });

            // 初始化图表容器
            function initChartContainers() {
                // 初始化图表容器，但不创建图表实例
                console.log('图表容器初始化完成');
            }

            // 加载设备图表
            function loadDeviceCharts(deviceId) {
                selectedDeviceId = deviceId;

                // 显示加载中提示
                document.getElementById('noDeviceSelectedMessage').classList.add('d-none');
                document.getElementById('chartLoadingMessage').classList.remove('d-none');
                document.getElementById('chartContainer').classList.add('d-none');

                // 清除之前的定时更新
                if (chartUpdateInterval) {
                    clearInterval(chartUpdateInterval);
                }

                // 获取设备参数历史数据
                fetchDeviceParameters(deviceId, function(success, data) {
                    // 隐藏加载中提示
                    document.getElementById('chartLoadingMessage').classList.add('d-none');

                    if (success) {
                        // 显示图表容器
                        document.getElementById('chartContainer').classList.remove('d-none');

                        // 处理参数数据
                        const paramData = processParameterData(data.parameters);

                        // 创建或更新图表
                        createOrUpdateCharts(paramData);

                        // 设置定时更新图表
                        chartUpdateInterval = setInterval(function() {
                            updateCharts(deviceId);
                        }, 30000); // 每30秒更新一次
                    } else {
                        // 显示错误信息
                        document.getElementById('noDeviceSelectedMessage').classList.remove('d-none');
                        document.getElementById('noDeviceSelectedMessage').innerHTML =
                            '<i class=\"bi bi-exclamation-triangle text-danger me-2\"></i>' +
                            '无法加载设备数据，请稍后重试';
                    }
                });
            }

            // 隐藏图表
            function hideCharts() {
                document.getElementById('chartContainer').classList.add('d-none');
                document.getElementById('noDeviceSelectedMessage').classList.remove('d-none');
                document.getElementById('chartLoadingMessage').classList.add('d-none');

                // 清除定时更新
                if (chartUpdateInterval) {
                    clearInterval(chartUpdateInterval);
                }
            }

            // 获取设备参数历史数据
            function fetchDeviceParameters(deviceId, callback) {
                fetch('${pageContext.request.contextPath}/device/monitor', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=getDeviceParameters&deviceId=' + deviceId
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        callback(true, data);
                    } else {
                        callback(false, null);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    callback(false, null);
                });
            }

            // 处理参数数据
            function processParameterData(parameters) {
                // 初始化结果对象
                const result = {
                    temperature: {
                        labels: [],
                        data: []
                    },
                    humidity: {
                        labels: [],
                        data: []
                    },
                    powerConsumption: {
                        labels: [],
                        data: []
                    }
                };

                // 按参数名称分组
                const temperatureParams = parameters.filter(p => p.parameterName === 'temperature').slice(0, 7);
                const humidityParams = parameters.filter(p => p.parameterName === 'humidity').slice(0, 7);
                const powerParams = parameters.filter(p => p.parameterName === 'power_consumption').slice(0, 7);

                // 如果没有足够的数据，使用模拟数据填充
                if (temperatureParams.length === 0) {
                    result.temperature.labels = ['1分钟前', '50秒前', '40秒前', '30秒前', '20秒前', '10秒前', '现在'];
                    result.temperature.data = [22, 22.5, 23, 22.8, 22.6, 23.1, 23.4];
                } else {
                    // 处理温度数据
                    temperatureParams.reverse().forEach((param, index) => {
                        const time = formatTime(param.recordTime);
                        result.temperature.labels.push(time);
                        result.temperature.data.push(parseFloat(param.parameterValue));
                    });
                }

                if (humidityParams.length === 0) {
                    result.humidity.labels = ['1分钟前', '50秒前', '40秒前', '30秒前', '20秒前', '10秒前', '现在'];
                    result.humidity.data = [45, 46, 47, 46.5, 46, 45.5, 45];
                } else {
                    // 处理湿度数据
                    humidityParams.reverse().forEach((param, index) => {
                        const time = formatTime(param.recordTime);
                        result.humidity.labels.push(time);
                        result.humidity.data.push(parseFloat(param.parameterValue));
                    });
                }

                if (powerParams.length === 0) {
                    result.powerConsumption.labels = ['1分钟前', '50秒前', '40秒前', '30秒前', '20秒前', '10秒前', '现在'];
                    result.powerConsumption.data = [320, 325, 330, 328, 335, 340, 338];
                } else {
                    // 处理功耗数据
                    powerParams.reverse().forEach((param, index) => {
                        const time = formatTime(param.recordTime);
                        result.powerConsumption.labels.push(time);
                        result.powerConsumption.data.push(parseFloat(param.parameterValue));
                    });
                }

                return result;
            }

            // 格式化时间
            function formatTime(timeString) {
                if (!timeString) return '现在';

                const date = new Date(timeString);
                const now = new Date();
                const diffMs = now - date;
                const diffSec = Math.floor(diffMs / 1000);

                if (diffSec < 60) {
                    return diffSec + '秒前';
                } else if (diffSec < 3600) {
                    return Math.floor(diffSec / 60) + '分钟前';
                } else if (diffSec < 86400) {
                    return Math.floor(diffSec / 3600) + '小时前';
                } else {
                    return date.getMonth() + 1 + '/' + date.getDate() + ' ' +
                           date.getHours() + ':' + date.getMinutes();
                }
            }

            // 创建或更新图表
            function createOrUpdateCharts(paramData) {
                // 温度图表
                const temperatureCtx = document.getElementById('temperatureChart');
                if (temperatureCtx) {
                    // 如果图表已存在，销毁它
                    if (temperatureChart) {
                        temperatureChart.destroy();
                    }

                    // 创建新图表
                    temperatureChart = new Chart(temperatureCtx, {
                        type: 'line',
                        data: {
                            labels: paramData.temperature.labels,
                            datasets: [{
                                label: '温度 (°C)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                borderWidth: 2,
                                fill: true,
                                data: paramData.temperature.data
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: true,
                                    position: 'top'
                                },
                                title: {
                                    display: true,
                                    text: '设备温度历史数据'
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    suggestedMin: Math.min(...paramData.temperature.data) - 5,
                                    suggestedMax: Math.max(...paramData.temperature.data) + 5
                                }
                            }
                        }
                    });
                }

                // 湿度图表
                const humidityCtx = document.getElementById('humidityChart');
                if (humidityCtx) {
                    // 如果图表已存在，销毁它
                    if (humidityChart) {
                        humidityChart.destroy();
                    }

                    // 创建新图表
                    humidityChart = new Chart(humidityCtx, {
                        type: 'line',
                        data: {
                            labels: paramData.humidity.labels,
                            datasets: [{
                                label: '湿度 (%)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                borderWidth: 2,
                                fill: true,
                                data: paramData.humidity.data
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: true,
                                    position: 'top'
                                },
                                title: {
                                    display: true,
                                    text: '设备湿度历史数据'
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    suggestedMin: Math.min(...paramData.humidity.data) - 5,
                                    suggestedMax: Math.max(...paramData.humidity.data) + 5
                                }
                            }
                        }
                    });
                }

                // 功耗图表
                const powerCtx = document.getElementById('powerChart');
                if (powerCtx) {
                    // 如果图表已存在，销毁它
                    if (powerChart) {
                        powerChart.destroy();
                    }

                    // 创建新图表
                    powerChart = new Chart(powerCtx, {
                        type: 'line',
                        data: {
                            labels: paramData.powerConsumption.labels,
                            datasets: [{
                                label: '功耗 (W)',
                                borderColor: 'rgba(255, 159, 64, 1)',
                                backgroundColor: 'rgba(255, 159, 64, 0.2)',
                                borderWidth: 2,
                                fill: true,
                                data: paramData.powerConsumption.data
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: true,
                                    position: 'top'
                                },
                                title: {
                                    display: true,
                                    text: '设备功耗历史数据'
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    suggestedMin: Math.min(...paramData.powerConsumption.data) - 50,
                                    suggestedMax: Math.max(...paramData.powerConsumption.data) + 50
                                }
                            }
                        }
                    });
                }
            }

            // 更新图表
            function updateCharts(deviceId) {
                fetchDeviceParameters(deviceId, function(success, data) {
                    if (success) {
                        const paramData = processParameterData(data.parameters);

                        // 更新温度图表
                        if (temperatureChart) {
                            temperatureChart.data.labels = paramData.temperature.labels;
                            temperatureChart.data.datasets[0].data = paramData.temperature.data;
                            temperatureChart.update();
                        }

                        // 更新湿度图表
                        if (humidityChart) {
                            humidityChart.data.labels = paramData.humidity.labels;
                            humidityChart.data.datasets[0].data = paramData.humidity.data;
                            humidityChart.update();
                        }

                        // 更新功耗图表
                        if (powerChart) {
                            powerChart.data.labels = paramData.powerConsumption.labels;
                            powerChart.data.datasets[0].data = paramData.powerConsumption.data;
                            powerChart.update();
                        }
                    }
                });
            }

            // 刷新设备状态
            function refreshDeviceStatus() {
                document.querySelectorAll('.device-card').forEach(card => {
                    const deviceId = card.getAttribute('data-device-id');

                    fetch('${pageContext.request.contextPath}/device/monitor', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'action=getDeviceStatus&deviceId=' + deviceId
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const device = data.device;

                            // 更新设备状态
                            const statusIndicator = card.querySelector('.status-indicator');
                            if (device.connectionStatus === 1) {
                                statusIndicator.classList.add('status-online');
                                statusIndicator.classList.remove('status-offline');
                            } else {
                                statusIndicator.classList.add('status-offline');
                                statusIndicator.classList.remove('status-online');
                            }

                            // 更新设备参数
                            const powerStatus = card.querySelector('.power-status');
                            if (device.powerStatus === 1) {
                                powerStatus.textContent = '开启';
                                powerStatus.classList.add('badge-success');
                                powerStatus.classList.remove('badge-danger');
                            } else {
                                powerStatus.textContent = '关闭';
                                powerStatus.classList.remove('badge-success');
                                powerStatus.classList.add('badge-danger');
                            }
                        }
                    })
                    .catch(error => console.error('Error:', error));
                });
            }

            // 加载设备详情
            function loadDeviceDetail(deviceId) {
                // 跳转到设备详情页面
                window.location.href = '${pageContext.request.contextPath}/device/detail?id=' + deviceId;
            }

            // 解决告警
            function resolveAlert(alertId) {
                console.log('解决告警ID:', alertId);

                fetch('${pageContext.request.contextPath}/device/monitor', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=resolveAlert&alertId=' + alertId
                })
                .then(response => response.json())
                .then(data => {
                    console.log('解决告警响应:', data);

                    if (data.success) {
                        // 移除告警卡片
                        const alertCard = document.querySelector('.alert-card[data-alert-id=\"' + alertId + '\"]');
                        if (alertCard) {
                            alertCard.remove();
                            console.log('已移除告警卡片');

                            // 如果没有告警了，显示无告警信息
                            if (document.querySelectorAll('.alert-card').length === 0) {
                                document.querySelector('#alertsContainer').innerHTML =
                                    '<div class=\"alert alert-success text-center py-3 mb-0 d-flex align-items-center justify-content-center\">' +
                                    '<i class=\"bi bi-check-circle-fill me-3\" style=\"font-size: 2.5rem;\"></i>' +
                                    '<span style=\"font-size: 1.5rem; font-weight: 500;\">当前没有未解决的告警</span>' +
                                    '</div>';
                                console.log('已更新为无告警状态');
                            }
                        } else {
                            console.error('未找到告警卡片元素');
                        }
                    } else {
                        alert('解决告警失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请稍后重试');
                });
            }

            // 控制设备
            function controlDevice(deviceId, action) {
                console.log('控制设备ID:', deviceId, '操作:', action);

                // 跳转到设备控制页面
                window.location.href = '${pageContext.request.contextPath}/device/detail?id=' + deviceId;
            }
        </script>
    " />
</jsp:include>
