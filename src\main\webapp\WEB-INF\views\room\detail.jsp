<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="教室详情" />
    <jsp:param name="content" value="/WEB-INF/views/room/detail-content.jsp" />
    <jsp:param name="additionalStyles" value="
        .room-layout {
            position: relative;
            width: 100%;
            height: 500px;
            border: 1px solid #ddd;
            background-color: #f8f9fa;
            margin-bottom: 20px;
        }
        .device-item {
            position: absolute;
            border: 2px solid #007bff;
            background-color: rgba(0, 123, 255, 0.1);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .device-item:hover {
            background-color: rgba(0, 123, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .device-item.fault {
            border-color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
        }
        .device-item.fault:hover {
            background-color: rgba(220, 53, 69, 0.2);
        }
        .device-item.maintenance {
            border-color: #ffc107;
            background-color: rgba(255, 193, 7, 0.1);
        }
        .device-item.maintenance:hover {
            background-color: rgba(255, 193, 7, 0.2);
        }
        .device-icon {
            font-size: 1.5rem;
        }
        .device-tooltip {
            position: absolute;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            display: none;
        }
        .room-info-card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .room-status-badge {
            font-size: 0.9rem;
            padding: 5px 10px;
        }
    " />
    <jsp:param name="scripts" value="
        <script>
            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                // 设备悬停事件
                document.querySelectorAll('.device-item').forEach(device => {
                    device.addEventListener('mouseenter', function(e) {
                        const tooltip = this.querySelector('.device-tooltip');
                        tooltip.style.display = 'block';
                    });
                    
                    device.addEventListener('mouseleave', function(e) {
                        const tooltip = this.querySelector('.device-tooltip');
                        tooltip.style.display = 'none';
                    });
                });
                
                // 设备控制按钮点击事件
                document.querySelectorAll('.device-control-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const deviceId = this.getAttribute('data-device-id');
                        const action = this.getAttribute('data-action');
                        
                        // 发送AJAX请求控制设备
                        fetch('${pageContext.request.contextPath}/device/control', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'deviceId=' + deviceId + '&action=' + action
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert('操作成功');
                                // 刷新页面
                                window.location.reload();
                            } else {
                                alert('操作失败: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('操作失败，请稍后重试');
                        });
                    });
                });
            });
        </script>
    " />
</jsp:include>
