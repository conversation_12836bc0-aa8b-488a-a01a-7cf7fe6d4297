package com.building.controller;

import com.building.model.SystemSettings;
import com.building.service.SystemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/system")
public class SystemController {
    private static final Logger logger = LoggerFactory.getLogger(SystemController.class);

    @Autowired
    private SystemService systemService;

    /**
     * 显示系统设置页面
     */
    @GetMapping("/settings")
    public String settings(Model model) {
        try {
            // 获取当前系统设置并添加到模型中
            SystemSettings settings = systemService.getCurrentSettings();
            model.addAttribute("settings", settings);
            logger.info("加载系统设置页面成功");
        } catch (Exception e) {
            logger.error("加载系统设置页面失败", e);
        }
        
        return "system/settings";
    }
    
    /**
     * 获取当前系统安全设置的API
     */
    @GetMapping("/getSecuritySettings")
    @ResponseBody
    public Map<String, Object> getSecuritySettings() {
        Map<String, Object> result = new HashMap<>();
        try {
            SystemSettings settings = systemService.getCurrentSettings();
            result.put("minPasswordLength", settings.getMinPasswordLength());
            result.put("requireUppercase", settings.isRequireUppercase());
            result.put("requireNumbers", settings.isRequireNumbers());
            result.put("requireSpecialChars", settings.isRequireSpecialChars());
            result.put("maxLoginAttempts", settings.getMaxLoginAttempts());
            
            result.put("success", true);
        } catch (Exception e) {
            logger.error("获取安全设置失败", e);
            result.put("success", false);
            result.put("message", "获取安全设置失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 保存安全设置
     */
    @PostMapping("/saveSecuritySettings")
    @ResponseBody
    public Map<String, Object> saveSecuritySettings(
            @RequestParam(defaultValue = "8") int minPasswordLength, 
            @RequestParam(defaultValue = "true") boolean requireUppercase,
            @RequestParam(defaultValue = "true") boolean requireNumbers,
            @RequestParam(defaultValue = "false") boolean requireSpecialChars,
            @RequestParam(defaultValue = "5") int maxLoginAttempts,
            HttpServletRequest request) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            // 记录操作用户信息
            String username = request.getSession().getAttribute("username") != null ? 
                    request.getSession().getAttribute("username").toString() : "unknown";
            
            logger.info("用户[{}]正在保存安全设置", username);
            
            // 更新系统安全设置
            systemService.updateSecuritySettings(
                    minPasswordLength, 
                    requireUppercase, 
                    requireNumbers, 
                    requireSpecialChars, 
                    maxLoginAttempts);
            
            result.put("success", true);
            result.put("message", "安全设置已保存");
            logger.info("安全设置保存成功");
        } catch (Exception e) {
            logger.error("保存安全设置失败", e);
            result.put("success", false);
            result.put("message", "保存失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取当前邮件设置的API
     */
    @GetMapping("/getEmailSettings")
    @ResponseBody
    public Map<String, Object> getEmailSettings() {
        Map<String, Object> result = new HashMap<>();
        try {
            SystemSettings settings = systemService.getCurrentSettings();
            result.put("smtpServer", settings.getSmtpServer());
            result.put("smtpPort", settings.getSmtpPort());
            result.put("senderEmail", settings.getSenderEmail());
            
            result.put("success", true);
        } catch (Exception e) {
            logger.error("获取邮件设置失败", e);
            result.put("success", false);
            result.put("message", "获取邮件设置失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 保存邮件设置
     */
    @PostMapping("/saveEmailSettings")
    @ResponseBody
    public Map<String, Object> saveEmailSettings(
            @RequestParam(required = false) String smtpServer,
            @RequestParam(defaultValue = "587") int smtpPort,
            @RequestParam(required = false) String senderEmail,
            @RequestParam(required = false) String emailPassword) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            // 更新邮件设置
            systemService.updateEmailSettings(smtpServer, smtpPort, senderEmail, emailPassword);
            
            result.put("success", true);
            result.put("message", "邮件设置已保存");
            logger.info("邮件设置保存成功");
        } catch (Exception e) {
            logger.error("保存邮件设置失败", e);
            result.put("success", false);
            result.put("message", "保存失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取当前备份设置的API
     */
    @GetMapping("/getBackupSettings")
    @ResponseBody
    public Map<String, Object> getBackupSettings() {
        Map<String, Object> result = new HashMap<>();
        try {
            SystemSettings settings = systemService.getCurrentSettings();
            result.put("autoBackup", settings.isAutoBackup());
            result.put("backupFrequency", settings.getBackupFrequency());
            result.put("backupRetention", settings.getBackupRetention());
            result.put("backupPath", settings.getBackupPath());
            
            result.put("success", true);
        } catch (Exception e) {
            logger.error("获取备份设置失败", e);
            result.put("success", false);
            result.put("message", "获取备份设置失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 保存备份设置
     */
    @PostMapping("/saveBackupSettings")
    @ResponseBody
    public Map<String, Object> saveBackupSettings(
            @RequestParam(defaultValue = "false") boolean autoBackup,
            @RequestParam(defaultValue = "daily") String backupFrequency,
            @RequestParam(defaultValue = "30") int backupRetention,
            @RequestParam(required = false) String backupPath) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            // 验证备份路径是否有效
            if (autoBackup && (backupPath == null || backupPath.trim().isEmpty())) {
                result.put("success", false);
                result.put("message", "启用自动备份时必须指定备份路径");
                return result;
            }
            
            // 更新备份设置
            systemService.updateBackupSettings(autoBackup, backupFrequency, backupRetention, backupPath);
            
            result.put("success", true);
            result.put("message", "备份设置已保存");
            logger.info("备份设置保存成功");
        } catch (Exception e) {
            logger.error("保存备份设置失败", e);
            result.put("success", false);
            result.put("message", "保存失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取当前通知设置的API
     */
    @GetMapping("/getNotificationSettings")
    @ResponseBody
    public Map<String, Object> getNotificationSettings() {
        Map<String, Object> result = new HashMap<>();
        try {
            SystemSettings settings = systemService.getCurrentSettings();
            result.put("systemNotifications", settings.isSystemNotifications());
            result.put("emailNotifications", settings.isEmailNotifications());
            result.put("notifyReservation", settings.isNotifyReservation());
            result.put("notifyMaintenance", settings.isNotifyMaintenance());
            result.put("notifySystem", settings.isNotifySystem());
            
            result.put("success", true);
        } catch (Exception e) {
            logger.error("获取通知设置失败", e);
            result.put("success", false);
            result.put("message", "获取通知设置失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 保存通知设置
     */
    @PostMapping("/saveNotificationSettings")
    @ResponseBody
    public Map<String, Object> saveNotificationSettings(
            @RequestParam(defaultValue = "true") boolean systemNotifications,
            @RequestParam(defaultValue = "false") boolean emailNotifications,
            @RequestParam(defaultValue = "true") boolean notifyReservation,
            @RequestParam(defaultValue = "true") boolean notifyMaintenance,
            @RequestParam(defaultValue = "true") boolean notifySystem) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            // 更新通知设置
            systemService.updateNotificationSettings(
                    systemNotifications, 
                    emailNotifications, 
                    notifyReservation, 
                    notifyMaintenance, 
                    notifySystem);
            
            result.put("success", true);
            result.put("message", "通知设置已保存");
            logger.info("通知设置保存成功");
        } catch (Exception e) {
            logger.error("保存通知设置失败", e);
            result.put("success", false);
            result.put("message", "保存失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 测试邮件配置
     */
    @PostMapping("/testEmailConnection")
    @ResponseBody
    public Map<String, Object> testEmailConnection(
            @RequestParam String smtpServer,
            @RequestParam int smtpPort,
            @RequestParam String senderEmail,
            @RequestParam String emailPassword) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            // 测试邮件连接
            boolean testResult = systemService.testEmailConnection(
                    smtpServer, smtpPort, senderEmail, emailPassword);
            
            if (testResult) {
                result.put("success", true);
                result.put("message", "邮件服务器连接成功");
            } else {
                result.put("success", false);
                result.put("message", "邮件服务器连接失败");
            }
        } catch (Exception e) {
            logger.error("测试邮件连接失败", e);
            result.put("success", false);
            result.put("message", "测试失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 立即执行系统备份
     */
    @PostMapping("/executeBackup")
    @ResponseBody
    public Map<String, Object> executeBackup() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 执行立即备份
            boolean backupResult = systemService.executeBackup();
            
            if (backupResult) {
                result.put("success", true);
                result.put("message", "备份执行成功");
            } else {
                result.put("success", false);
                result.put("message", "备份执行失败");
            }
        } catch (Exception e) {
            logger.error("执行备份失败", e);
            result.put("success", false);
            result.put("message", "备份失败：" + e.getMessage());
        }
        return result;
    }
} 