<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <h2><i class="bi bi-calendar-event me-2"></i>房间活动记录</h2>
        </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <form id="searchForm" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">房间号</label>
                            <input type="text" class="form-control" name="roomNumber" placeholder="输入房间号">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">活动类型</label>
                            <select class="form-select" name="activityType">
                                <option value="">全部</option>
                                <option value="meeting">会议</option>
                                <option value="training">培训</option>
                                <option value="interview">面试</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">日期范围</label>
                            <div class="input-group">
                                <input type="date" class="form-control" name="startDate">
                                <span class="input-group-text">至</span>
                                <input type="date" class="form-control" name="endDate">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-search me-1"></i>搜索
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 活动记录表格 -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>房间号</th>
                                    <th>活动类型</th>
                                    <th>使用人</th>
                                    <th>开始时间</th>
                                    <th>结束时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach items="${activities}" var="activity">
                                    <tr>
                                        <td>${activity.roomNumber}</td>
                                        <td>${activity.type}</td>
                                        <td>${activity.userName}</td>
                                        <td>${activity.startTime}</td>
                                        <td>${activity.endTime}</td>
                                        <td>
                                            <span class="badge bg-${activity.status == '进行中' ? 'success' : 'secondary'}">
                                                ${activity.status}
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-info" onclick="viewDetails(${activity.id})">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">上一页</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#">下一页</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详情模态框 -->
<div class="modal fade" id="activityDetailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">活动详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="fw-bold">房间号：</label>
                    <span id="detailRoomNumber"></span>
                </div>
                <div class="mb-3">
                    <label class="fw-bold">活动类型：</label>
                    <span id="detailType"></span>
                </div>
                <div class="mb-3">
                    <label class="fw-bold">使用人：</label>
                    <span id="detailUser"></span>
                </div>
                <div class="mb-3">
                    <label class="fw-bold">开始时间：</label>
                    <span id="detailStartTime"></span>
                </div>
                <div class="mb-3">
                    <label class="fw-bold">结束时间：</label>
                    <span id="detailEndTime"></span>
                </div>
                <div class="mb-3">
                    <label class="fw-bold">状态：</label>
                    <span id="detailStatus"></span>
                </div>
                <div class="mb-3">
                    <label class="fw-bold">备注：</label>
                    <p id="detailNotes" class="text-muted"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 表单提交处理
    $('#searchForm').submit(function(e) {
        e.preventDefault();
        var formData = $(this).serialize();
        
        // TODO: 发送AJAX请求获取搜索结果
        $.ajax({
            url: '${pageContext.request.contextPath}/room/searchActivities',
            type: 'GET',
            data: formData,
            success: function(response) {
                // TODO: 更新表格内容
                console.log('搜索结果:', response);
            },
            error: function() {
                alert('搜索失败，请稍后重试');
            }
        });
    });
});

// 查看详情
function viewDetails(activityId) {
    // TODO: 发送AJAX请求获取活动详情
    $.ajax({
        url: '${pageContext.request.contextPath}/room/activity/' + activityId,
        type: 'GET',
        success: function(response) {
            // 更新模态框内容
            $('#detailRoomNumber').text(response.roomNumber);
            $('#detailType').text(response.type);
            $('#detailUser').text(response.userName);
            $('#detailStartTime').text(response.startTime);
            $('#detailEndTime').text(response.endTime);
            $('#detailStatus').text(response.status);
            $('#detailNotes').text(response.notes);
            
            // 显示模态框
            $('#activityDetailModal').modal('show');
        },
        error: function() {
            alert('获取详情失败，请稍后重试');
        }
    });
}
</script> 