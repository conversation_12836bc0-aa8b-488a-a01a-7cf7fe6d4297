package com.building.service;

import java.util.List;
import com.building.model.Reservation;

/**
 * 预约服务接口
 */
public interface ReservationService {
    
    /**
     * 获取所有预约列表
     * @return 预约列表
     */
    List<Reservation> getAllReservations();
    
    /**
     * 根据ID获取预约信息
     * @param id 预约ID
     * @return 预约对象，如果不存在返回null
     */
    Reservation getReservationById(int id);
    
    /**
     * 获取用户的所有预约
     * @param userId 用户ID
     * @return 预约列表
     */
    List<Reservation> getReservationsByUserId(int userId);
    
    /**
     * 获取房间的所有预约
     * @param roomId 房间ID
     * @return 预约列表
     */
    List<Reservation> getReservationsByRoomId(int roomId);
    
    /**
     * 检查房间在指定时间段内是否已被预约
     * @param roomId 房间ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 如果已被预约返回true，否则返回false
     */
    boolean isRoomReserved(int roomId, String startTime, String endTime);
    
    /**
     * 获取用户的预约列表
     */
    List<Reservation> getUserReservations(int userId);
    
    /**
     * 添加预约
     */
    boolean addReservation(Reservation reservation);
    
    /**
     * 更新预约
     * @param reservation 预约对象
     * @return 更新是否成功
     */
    boolean updateReservation(Reservation reservation);
    
    /**
     * 删除预约
     */
    boolean deleteReservation(int id);
    
    /**
     * 更新预约状态
     */
    boolean updateReservationStatus(int id, String status);
    
    /**
     * 获取预约统计信息
     */
    int[] getReservationStatistics();
    
    /**
     * 检查设备在指定时间段内是否已被预约
     * @param deviceId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 如果已被预约返回true，否则返回false
     */
    boolean isDeviceReserved(int deviceId, String startTime, String endTime);
} 