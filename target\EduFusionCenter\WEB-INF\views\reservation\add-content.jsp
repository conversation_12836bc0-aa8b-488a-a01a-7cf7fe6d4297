<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">添加预约</h1>
        <a href="${pageContext.request.contextPath}/reservation/list" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> 返回预约列表
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form id="addReservationForm" action="${pageContext.request.contextPath}/reservation/add" method="post">
                <div class="row mb-4">
                    <div class="col-12">
                        <label class="form-label">预约类型</label>
                        <div class="d-flex gap-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="reservationType" id="roomType" value="room" checked>
                                <label class="form-check-label" for="roomType">
                                    预约房间
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="reservationType" id="deviceType" value="device">
                                <label class="form-check-label" for="deviceType">
                                    预约设备
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3" id="roomSelection">
                        <label class="form-label">房间</label>
                        <select class="form-select" name="roomId">
                            <option value="">请选择房间</option>
                            <c:forEach items="${rooms}" var="room">
                                <option value="${room.id}">${room.roomNumber} - ${room.roomType}</option>
                            </c:forEach>
                        </select>
                    </div>
                    <div class="col-md-12 mb-3" id="deviceSelection" style="display: none;">
                        <label class="form-label">设备</label>
                        <select class="form-select" name="deviceId">
                            <option value="">请选择设备</option>
                            <c:forEach items="${devices}" var="device">
                                <option value="${device.id}">${device.name} - ${device.type} (${device.location})</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">使用目的</label>
                        <input type="text" class="form-control" name="purpose" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">备注</label>
                        <input type="text" class="form-control" name="description">
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">开始时间</label>
                        <input type="datetime-local" class="form-control" name="startTime" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">结束时间</label>
                        <input type="datetime-local" class="form-control" name="endTime" required>
                    </div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="button" class="btn btn-secondary me-md-2" onclick="window.location.href='${pageContext.request.contextPath}/reservation/list'">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('addReservationForm');
    const roomSelection = document.getElementById('roomSelection');
    const deviceSelection = document.getElementById('deviceSelection');
    const roomSelect = roomSelection.querySelector('select');
    const deviceSelect = deviceSelection.querySelector('select');
    
    // 监听预约类型切换
    document.querySelectorAll('input[name="reservationType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'room') {
                roomSelection.style.display = 'block';
                deviceSelection.style.display = 'none';
                roomSelect.setAttribute('required', '');
                deviceSelect.removeAttribute('required');
                deviceSelect.value = '';
            } else {
                roomSelection.style.display = 'none';
                deviceSelection.style.display = 'block';
                deviceSelect.setAttribute('required', '');
                roomSelect.removeAttribute('required');
                roomSelect.value = '';
            }
        });
    });
    
    form.addEventListener('submit', function(event) {
        event.preventDefault();
        
        // 收集表单数据
        const formData = new URLSearchParams();
        const formElements = form.elements;
        
        // 根据预约类型设置ID
        const reservationType = form.querySelector('input[name="reservationType"]:checked').value;
        if (reservationType === 'room') {
            formData.append('roomId', roomSelect.value);
            formData.append('deviceId', '');
        } else {
            formData.append('deviceId', deviceSelect.value);
            formData.append('roomId', '');
        }
        
        // 添加其他表单数据
        for (let i = 0; i < formElements.length; i++) {
            const element = formElements[i];
            if (element.name && element.value && 
                element.name !== 'roomId' && 
                element.name !== 'deviceId' &&
                element.name !== 'reservationType') {
                formData.append(element.name, element.value);
            }
        }
        
        fetch('${pageContext.request.contextPath}/reservation/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: formData.toString()
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('预约添加成功');
                window.location.href = '${pageContext.request.contextPath}/reservation/list';
            } else {
                alert('预约添加失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('系统错误，请稍后重试');
        });
    });
});
</script> 