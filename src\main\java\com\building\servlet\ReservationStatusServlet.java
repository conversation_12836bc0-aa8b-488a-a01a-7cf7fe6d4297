package com.building.servlet;

import java.io.IOException;
import java.io.PrintWriter;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.model.ApiResponse;
import com.building.model.User;
import com.building.model.Room;
import com.building.model.Reservation;
import com.building.service.ReservationService;
import com.building.service.RoomService;
import com.building.service.impl.ReservationServiceImpl;
import com.building.service.impl.RoomServiceImpl;
import com.building.util.JsonUtil;

/**
 * 预约状态更新控制器
 */
@WebServlet("/reservation/status")
public class ReservationStatusServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    private ReservationService reservationService;
    
    @Override
    public void init() throws ServletException {
        try {
            reservationService = new ReservationServiceImpl();
            System.out.println("ReservationStatusServlet 初始化成功");
        } catch (Exception e) {
            System.err.println("ReservationStatusServlet 初始化失败: " + e.getMessage());
            e.printStackTrace();
            throw new ServletException("初始化服务失败", e);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        System.out.println("开始处理预约状态更新请求");
        
        // 设置请求编码
        request.setCharacterEncoding("UTF-8");
        // 设置响应类型和编码
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = response.getWriter();
        
        try {
            // 检查用户是否登录
            HttpSession session = request.getSession();
            User user = (User) session.getAttribute("user");
            if (user == null) {
                System.out.println("用户未登录");
                ApiResponse apiResponse = new ApiResponse(false, "用户未登录");
                out.print(JsonUtil.toJson(apiResponse));
                return;
            }
            
            // 检查用户权限
            if (!"admin".equals(user.getRole())) {
                System.out.println("用户无权限");
                ApiResponse apiResponse = new ApiResponse(false, "无权限执行此操作");
                out.print(JsonUtil.toJson(apiResponse));
                return;
            }
            
            // 获取参数
            String idStr = request.getParameter("id");
            String status = request.getParameter("status");
            
            System.out.println("接收到的参数：");
            System.out.println("id: " + idStr);
            System.out.println("status: " + status);
            
            // 验证参数
            if (idStr == null || idStr.trim().isEmpty() || 
                status == null || status.trim().isEmpty()) {
                System.out.println("参数不完整");
                ApiResponse apiResponse = new ApiResponse(false, "请提供预约ID和状态");
                out.print(JsonUtil.toJson(apiResponse));
                return;
            }
            
            int id = Integer.parseInt(idStr);
            
            // 更新状态
            System.out.println("开始更新预约状态");
            try {
                boolean success = reservationService.updateReservationStatus(id, status);
                
                if (success) {
                    System.out.println("状态更新成功");
                    // 获取更新后的房间状态
                    Reservation reservation = reservationService.getReservationById(id);
                    ApiResponse apiResponse = new ApiResponse(true, "状态更新成功");
                    
                    if (reservation != null && reservation.getRoomId() != null && 
                        (status.equals("已批准") || status.equals("已取消") || status.equals("已拒绝"))) {
                        RoomService roomService = new RoomServiceImpl();
                        Room room = roomService.getRoomById(reservation.getRoomId());
                        apiResponse.setData(room);
                    }
                    
                    out.print(JsonUtil.toJson(apiResponse));
                } else {
                    System.out.println("状态更新失败");
                    ApiResponse apiResponse = new ApiResponse(false, "状态更新失败，请检查预约是否存在或状态是否有效");
                    out.print(JsonUtil.toJson(apiResponse));
                }
            } catch (IllegalArgumentException e) {
                System.err.println("状态更新参数错误: " + e.getMessage());
                ApiResponse apiResponse = new ApiResponse(false, "状态更新失败：" + e.getMessage());
                out.print(JsonUtil.toJson(apiResponse));
            } catch (Exception e) {
                System.err.println("状态更新时发生未知错误: " + e.getMessage());
                e.printStackTrace();
                ApiResponse apiResponse = new ApiResponse(false, "系统错误：" + e.getMessage());
                out.print(JsonUtil.toJson(apiResponse));
            }
        } catch (NumberFormatException e) {
            System.err.println("预约ID格式不正确: " + e.getMessage());
            ApiResponse apiResponse = new ApiResponse(false, "预约ID格式不正确");
            out.print(JsonUtil.toJson(apiResponse));
        } catch (Exception e) {
            System.err.println("处理预约状态更新请求时发生错误: " + e.getMessage());
            e.printStackTrace();
            ApiResponse apiResponse = new ApiResponse(false, "系统错误：" + e.getMessage());
            out.print(JsonUtil.toJson(apiResponse));
        }
    }
}
