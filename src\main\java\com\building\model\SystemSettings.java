package com.building.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统设置实体类
 */
public class SystemSettings implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Integer id;
    
    // 安全设置
    private Integer minPasswordLength = 8; // 密码最小长度
    private boolean requireUppercase = true; // 是否要求大写字母
    private boolean requireNumbers = true; // 是否要求数字
    private boolean requireSpecialChars = false; // 是否要求特殊字符
    private Integer maxLoginAttempts = 5; // 最大登录尝试次数
    
    // 邮件设置
    private String smtpServer; // SMTP服务器地址
    private Integer smtpPort = 587; // SMTP端口
    private String senderEmail; // 发件人邮箱
    private String emailPassword; // 邮箱密码(加密存储)
    
    // 备份设置
    private boolean autoBackup = false; // 是否自动备份
    private String backupFrequency = "daily"; // 备份频率：daily, weekly, monthly
    private Integer backupRetention = 30; // 备份保留天数
    private String backupPath; // 备份路径
    private Date lastBackupTime; // 上次备份时间
    
    // 通知设置
    private boolean systemNotifications = true; // 是否启用系统通知
    private boolean emailNotifications = false; // 是否启用邮件通知
    private boolean notifyReservation = true; // 是否通知预约相关事件
    private boolean notifyMaintenance = true; // 是否通知设备维护事件
    private boolean notifySystem = true; // 是否通知系统更新事件
    
    // 创建和更新时间
    private Date createTime;
    private Date updateTime;
    
    // 新添加的属性
    private String siteName; // 网站名称
    private String siteUrl; // 网站URL
    private String adminEmail; // 管理员邮箱
    private String siteDescription; // 网站描述
    private boolean maintenanceMode; // 维护模式
    private int pageSize; // 每页显示条数
    
    public SystemSettings() {
    }
    
    // Getters and Setters
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public Integer getMinPasswordLength() {
        return minPasswordLength;
    }
    
    public void setMinPasswordLength(Integer minPasswordLength) {
        this.minPasswordLength = minPasswordLength;
    }
    
    public boolean isRequireUppercase() {
        return requireUppercase;
    }
    
    public void setRequireUppercase(boolean requireUppercase) {
        this.requireUppercase = requireUppercase;
    }
    
    public boolean isRequireNumbers() {
        return requireNumbers;
    }
    
    public void setRequireNumbers(boolean requireNumbers) {
        this.requireNumbers = requireNumbers;
    }
    
    public boolean isRequireSpecialChars() {
        return requireSpecialChars;
    }
    
    public void setRequireSpecialChars(boolean requireSpecialChars) {
        this.requireSpecialChars = requireSpecialChars;
    }
    
    public Integer getMaxLoginAttempts() {
        return maxLoginAttempts;
    }
    
    public void setMaxLoginAttempts(Integer maxLoginAttempts) {
        this.maxLoginAttempts = maxLoginAttempts;
    }
    
    public String getSmtpServer() {
        return smtpServer;
    }
    
    public void setSmtpServer(String smtpServer) {
        this.smtpServer = smtpServer;
    }
    
    public Integer getSmtpPort() {
        return smtpPort;
    }
    
    public void setSmtpPort(Integer smtpPort) {
        this.smtpPort = smtpPort;
    }
    
    public String getSenderEmail() {
        return senderEmail;
    }
    
    public void setSenderEmail(String senderEmail) {
        this.senderEmail = senderEmail;
    }
    
    public String getEmailPassword() {
        return emailPassword;
    }
    
    public void setEmailPassword(String emailPassword) {
        this.emailPassword = emailPassword;
    }
    
    public boolean isAutoBackup() {
        return autoBackup;
    }
    
    public void setAutoBackup(boolean autoBackup) {
        this.autoBackup = autoBackup;
    }
    
    public String getBackupFrequency() {
        return backupFrequency;
    }
    
    public void setBackupFrequency(String backupFrequency) {
        this.backupFrequency = backupFrequency;
    }
    
    public Integer getBackupRetention() {
        return backupRetention;
    }
    
    public void setBackupRetention(Integer backupRetention) {
        this.backupRetention = backupRetention;
    }
    
    public String getBackupPath() {
        return backupPath;
    }
    
    public void setBackupPath(String backupPath) {
        this.backupPath = backupPath;
    }
    
    public Date getLastBackupTime() {
        return lastBackupTime;
    }
    
    public void setLastBackupTime(Date lastBackupTime) {
        this.lastBackupTime = lastBackupTime;
    }
    
    public boolean isSystemNotifications() {
        return systemNotifications;
    }
    
    public void setSystemNotifications(boolean systemNotifications) {
        this.systemNotifications = systemNotifications;
    }
    
    public boolean isEmailNotifications() {
        return emailNotifications;
    }
    
    public void setEmailNotifications(boolean emailNotifications) {
        this.emailNotifications = emailNotifications;
    }
    
    public boolean isNotifyReservation() {
        return notifyReservation;
    }
    
    public void setNotifyReservation(boolean notifyReservation) {
        this.notifyReservation = notifyReservation;
    }
    
    public boolean isNotifyMaintenance() {
        return notifyMaintenance;
    }
    
    public void setNotifyMaintenance(boolean notifyMaintenance) {
        this.notifyMaintenance = notifyMaintenance;
    }
    
    public boolean isNotifySystem() {
        return notifySystem;
    }
    
    public void setNotifySystem(boolean notifySystem) {
        this.notifySystem = notifySystem;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getSiteName() {
        return siteName;
    }
    
    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }
    
    public String getSiteUrl() {
        return siteUrl;
    }
    
    public void setSiteUrl(String siteUrl) {
        this.siteUrl = siteUrl;
    }
    
    public String getAdminEmail() {
        return adminEmail;
    }
    
    public void setAdminEmail(String adminEmail) {
        this.adminEmail = adminEmail;
    }
    
    public String getSiteDescription() {
        return siteDescription;
    }
    
    public void setSiteDescription(String siteDescription) {
        this.siteDescription = siteDescription;
    }
    
    public boolean isMaintenanceMode() {
        return maintenanceMode;
    }
    
    public void setMaintenanceMode(boolean maintenanceMode) {
        this.maintenanceMode = maintenanceMode;
    }
    
    public int getPageSize() {
        return pageSize;
    }
    
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
    
    @Override
    public String toString() {
        return "SystemSettings{" +
                "id=" + id +
                ", minPasswordLength=" + minPasswordLength +
                ", requireUppercase=" + requireUppercase +
                ", requireNumbers=" + requireNumbers +
                ", requireSpecialChars=" + requireSpecialChars +
                ", maxLoginAttempts=" + maxLoginAttempts +
                ", smtpServer='" + smtpServer + '\'' +
                ", smtpPort=" + smtpPort +
                ", senderEmail='" + senderEmail + '\'' +
                ", autoBackup=" + autoBackup +
                ", backupFrequency='" + backupFrequency + '\'' +
                ", backupRetention=" + backupRetention +
                ", backupPath='" + backupPath + '\'' +
                ", systemNotifications=" + systemNotifications +
                ", emailNotifications=" + emailNotifications +
                ", notifyReservation=" + notifyReservation +
                ", notifyMaintenance=" + notifyMaintenance +
                ", notifySystem=" + notifySystem +
                ", siteName='" + siteName + '\'' +
                ", siteUrl='" + siteUrl + '\'' +
                ", adminEmail='" + adminEmail + '\'' +
                ", siteDescription='" + siteDescription + '\'' +
                ", maintenanceMode=" + maintenanceMode +
                ", pageSize=" + pageSize +
                '}';
    }
} 