package com.building.servlet;

import java.io.IOException;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.dao.UserDao;
import com.building.model.User;

/**
 * 用户列表控制器
 * 处理用户列表的显示和统计
 */
@WebServlet("/user/list")
public class UserListServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    private UserDao userDao;
    
    @Override
    public void init() throws ServletException {
        userDao = new UserDao();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        try {
            // 获取用户列表
            List<User> users = userDao.getUsers(1, 10, null);
            int totalUsers = userDao.getTotalUsers(null);
            
            // 计算管理员和普通用户数量
            int adminCount = 0;
            int userCount = 0;
            for (User user : users) {
                if ("admin".equals(user.getRole())) {
                    adminCount++;
                } else {
                    userCount++;
                }
            }
            
            // 设置属性
            request.setAttribute("users", users);
            request.setAttribute("totalUsers", totalUsers);
            request.setAttribute("adminCount", adminCount);
            request.setAttribute("userCount", userCount);
            
            // 转发到用户列表页面
            request.getRequestDispatcher("/WEB-INF/views/user/list.jsp").forward(request, response);
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "获取用户列表失败：" + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/error/500.jsp").forward(request, response);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doGet(request, response);
    }
} 