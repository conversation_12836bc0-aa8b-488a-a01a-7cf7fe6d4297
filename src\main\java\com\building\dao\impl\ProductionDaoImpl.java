package com.building.dao.impl;

import com.building.dao.ProductionDao;
import com.building.model.ProductionLine;
import com.building.util.DBUtil;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@Repository
public class ProductionDaoImpl implements ProductionDao {

    @Override
    public List<ProductionLine> getAllProductionLines() {
        List<ProductionLine> productionLines = new ArrayList<>();
        String sql = "SELECT id, name, status, last_updated FROM production_line";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                ProductionLine line = new ProductionLine();
                line.setId(rs.getInt("id"));
                line.setName(rs.getString("name"));
                line.setStatus(rs.getString("status"));
                line.setLastUpdated(rs.getString("last_updated"));
                productionLines.add(line);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return productionLines;
    }

    @Override
    public void addProductionLine(ProductionLine line) {
        String sql = "INSERT INTO production_line (name, status) VALUES (?, ?)";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, line.getName());
            stmt.setString(2, line.getStatus());
            stmt.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
            throw new RuntimeException("添加产线失败", e);
        }
    }

    @Override
    public ProductionLine getProductionLine(int id) {
        String sql = "SELECT id, name, status, last_updated FROM production_line WHERE id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    ProductionLine line = new ProductionLine();
                    line.setId(rs.getInt("id"));
                    line.setName(rs.getString("name"));
                    line.setStatus(rs.getString("status"));
                    line.setLastUpdated(rs.getString("last_updated"));
                    return line;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            throw new RuntimeException("获取产线信息失败", e);
        }
        return null;
    }

    @Override
    public void updateProductionLine(ProductionLine line) {
        String sql = "UPDATE production_line SET name = ?, status = ? WHERE id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, line.getName());
            stmt.setString(2, line.getStatus());
            stmt.setInt(3, line.getId());
            stmt.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
            throw new RuntimeException("更新产线失败", e);
        }
    }

    @Override
    public void deleteProductionLine(int id) {
        String sql = "DELETE FROM production_line WHERE id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, id);
            stmt.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
            throw new RuntimeException("删除产线失败", e);
        }
    }
} 