<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="控制面板" />
    <jsp:param name="content" value="/WEB-INF/views/dashboard/index.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 统计卡片样式 */
        .stats-card {
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .stats-icon {
            font-size: 2.2rem;
            opacity: 0.9;
        }
        .stats-icon-container {
            transition: all 0.3s ease;
        }
        .stats-card:hover .stats-icon-container {
            transform: scale(1.1);
        }

        /* 活动列表样式 */
        .activity-time {
            font-size: 0.875rem;
            color: #6c757d;
        }
        .activity-badge {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .activity-badge.add { background-color: #28a745; }
        .activity-badge.update { background-color: #ffc107; }
        .activity-badge.delete { background-color: #dc3545; }

        /* 图表卡片样式 */
        .chart-card {
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .chart-card:hover {
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        /* 表格样式 */
        .table th {
            font-weight: 600;
            color: #495057;
        }
        .table td {
            vertical-align: middle;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
    " />
    <jsp:param name="scripts" value="
        <script src='https://cdn.jsdelivr.net/npm/chart.js'></script>
        <script>
            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                // 获取统计数据
                fetch('${pageContext.request.contextPath}/room/stats')
                    .then(response => response.json())
                    .then(data => {
                        document.querySelector('[data-stat=total]').textContent = data.total;
                        document.querySelector('[data-stat=used]').textContent = data.used;
                        document.querySelector('[data-stat=free]').textContent = data.free;
                    })
                    .catch(error => console.error('Error:', error));

                // 获取楼层分布数据
                fetch('${pageContext.request.contextPath}/room/stats/floor')
                    .then(response => response.json())
                    .then(data => {
                        new Chart(document.getElementById('floorChart'), {
                            type: 'bar',
                            data: {
                                labels: data.labels,
                                datasets: [{
                                    label: '房间数量',
                                    data: data.values,
                                    backgroundColor: '#0d6efd'
                                }]
                            },
                            options: {
                                responsive: true,
                                plugins: {
                                    legend: { display: false }
                                }
                            }
                        });
                    });

                // 获取房间类型分布数据
                fetch('${pageContext.request.contextPath}/room/stats/type')
                    .then(response => response.json())
                    .then(data => {
                        new Chart(document.getElementById('typeChart'), {
                            type: 'doughnut',
                            data: {
                                labels: data.labels,
                                datasets: [{
                                    data: data.values,
                                    backgroundColor: [
                                        '#0d6efd',
                                        '#198754',
                                        '#ffc107',
                                        '#dc3545'
                                    ]
                                }]
                            }
                        });
                    });

                // 获取最近活动数据
                fetch('${pageContext.request.contextPath}/room/activity')
                    .then(response => response.json())
                    .then(data => {
                        const tbody = document.getElementById('activityList');
                        tbody.innerHTML = data.map(activity => `
                            <tr>
                                <td class='activity-time'>${activity.time}</td>
                                <td>${activity.roomNumber}</td>
                                <td>
                                    <span class='activity-badge ${activity.type}'></span>
                                    ${activity.typeText}
                                </td>
                                <td>${activity.operator}</td>
                                <td>${activity.details}</td>
                            </tr>
                        `).join('');
                    });
            });
        </script>
    " />
</jsp:include>