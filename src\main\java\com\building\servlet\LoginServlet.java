package com.building.servlet;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.model.User;
import com.building.service.UserService;
import com.building.service.impl.UserServiceImpl;

/**
 * 登录控制器
 * 处理用户登录请求
 * 包含验证码校验、用户认证和会话管理
 * 
 * <AUTHOR>
 * @date 2024-03-04
 */
@WebServlet("/login")
public class LoginServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    private UserService userService;
    
    @Override
    public void init() throws ServletException {
        userService = new UserServiceImpl();
    }
    
    /**
     * 处理登录POST请求
     * 验证用户身份并创建会话
     * 
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws ServletException 如果处理请求时发生错误
     * @throws IOException 如果发生I/O错误
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 设置请求编码
        request.setCharacterEncoding("UTF-8");
        
        // 获取表单数据
        String username = request.getParameter("username");
        String password = request.getParameter("password");
        String captcha = request.getParameter("captcha");
        String remember = request.getParameter("remember");
        
        HttpSession session = request.getSession();
        String sessionCaptcha = (String) session.getAttribute("captcha");
        
        // 验证码校验
        if (captcha == null || !captcha.equalsIgnoreCase(sessionCaptcha)) {
            returnError(request, response, "验证码错误");
            return;
        }
        
        // 清除session中的验证码
        session.removeAttribute("captcha");
        
        // 用户名和密码校验
        if (username == null || password == null || 
            username.trim().isEmpty() || password.trim().isEmpty()) {
            returnError(request, response, "用户名和密码不能为空");
            return;
        }
        
        // 验证用户登录
        User user = userService.login(username, password);
        if (user == null) {
            returnError(request, response, "用户名或密码错误");
            return;
        }
        
        // 登录成功，设置session
        session.setAttribute("user", user);
        session.setAttribute("remember", "1".equals(remember));
        
        // 重定向到主页
        response.sendRedirect("main.jsp");
    }
    
    private void returnError(HttpServletRequest request, HttpServletResponse response, String error) 
            throws ServletException, IOException {
        request.setAttribute("error", error);
        request.setAttribute("username", request.getParameter("username"));
        request.setAttribute("remember", "1".equals(request.getParameter("remember")));
        request.getRequestDispatcher("login.jsp").forward(request, response);
    }
} 