package com.building.dao;

import com.building.model.DeviceParameter;
import com.building.util.DBUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备参数数据访问对象
 * 用于处理设备参数相关的数据库操作
 */
public class DeviceParameterDao {
    
    /**
     * 添加设备参数记录
     * @param parameter 设备参数对象
     * @return 是否添加成功
     */
    public boolean addParameter(DeviceParameter parameter) {
        String sql = "INSERT INTO device_parameter_history (device_id, parameter_name, parameter_value) VALUES (?, ?, ?)";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, parameter.getDeviceId());
            pstmt.setString(2, parameter.getParameterName());
            pstmt.setString(3, parameter.getParameterValue());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 根据设备ID获取最新的参数记录
     * @param deviceId 设备ID
     * @return 参数记录列表
     */
    public List<DeviceParameter> getLatestParametersByDeviceId(int deviceId) {
        List<DeviceParameter> parameters = new ArrayList<>();
        String sql = "SELECT * FROM device_parameter_history " +
                    "WHERE device_id = ? " +
                    "ORDER BY record_time DESC";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, deviceId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    DeviceParameter parameter = new DeviceParameter();
                    parameter.setId(rs.getInt("id"));
                    parameter.setDeviceId(rs.getInt("device_id"));
                    parameter.setParameterName(rs.getString("parameter_name"));
                    parameter.setParameterValue(rs.getString("parameter_value"));
                    parameter.setRecordTime(rs.getString("record_time"));
                    parameters.add(parameter);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return parameters;
    }
    
    /**
     * 根据设备ID和参数名称获取参数历史记录
     * @param deviceId 设备ID
     * @param parameterName 参数名称
     * @return 参数历史记录列表
     */
    public List<DeviceParameter> getParameterHistoryByName(int deviceId, String parameterName) {
        List<DeviceParameter> parameters = new ArrayList<>();
        String sql = "SELECT * FROM device_parameter_history " +
                    "WHERE device_id = ? AND parameter_name = ? " +
                    "ORDER BY record_time DESC";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, deviceId);
            pstmt.setString(2, parameterName);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    DeviceParameter parameter = new DeviceParameter();
                    parameter.setId(rs.getInt("id"));
                    parameter.setDeviceId(rs.getInt("device_id"));
                    parameter.setParameterName(rs.getString("parameter_name"));
                    parameter.setParameterValue(rs.getString("parameter_value"));
                    parameter.setRecordTime(rs.getString("record_time"));
                    parameters.add(parameter);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return parameters;
    }
    
    /**
     * 删除指定时间之前的参数记录
     * @param days 天数
     * @return 是否删除成功
     */
    public boolean deleteOldParameters(int days) {
        String sql = "DELETE FROM device_parameter_history WHERE record_time < DATE_SUB(NOW(), INTERVAL ? DAY)";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, days);
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
}
