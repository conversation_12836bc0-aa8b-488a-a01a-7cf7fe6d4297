package com.building.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 数据库备份工具类
 * 提供MySQL数据库备份和恢复功能
 */
public class DatabaseBackupUtil {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseBackupUtil.class);
    
    // 从DBUtil获取数据库配置
    private static final String DB_HOST = "127.0.0.1";
    private static final String DB_PORT = "31306";
    private static final String DB_NAME = "building_db";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "Yf.31306";
    
    /**
     * 执行数据库备份
     * 
     * @param backupPath 备份文件保存路径
     * @return 备份结果对象，包含是否成功、文件路径、大小等信息
     */
    public static BackupResult backup(String backupPath) {
        logger.info("开始执行数据库备份，保存路径：{}", backupPath);
        long startTime = System.currentTimeMillis();
        BackupResult result = new BackupResult();
        
        try {
            // 确保备份目录存在
            Path backupDir = Paths.get(backupPath);
            if (!Files.exists(backupDir)) {
                Files.createDirectories(backupDir);
                logger.info("创建备份目录：{}", backupPath);
            }
            
            // 生成备份文件名
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss");
            String timestamp = dateFormat.format(new Date());
            String backupFileName = "building_db_backup_" + timestamp + ".sql";
            String fullBackupPath = Paths.get(backupPath, backupFileName).toString();
            
            // 构建mysqldump命令
            List<String> command = buildMysqlDumpCommand(fullBackupPath);
            
            // 执行备份命令
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();
            
            // 读取输出信息
            StringBuilder outputInfo = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    outputInfo.append(line).append("\n");
                }
            }
            
            // 等待命令执行完成
            int exitCode = process.waitFor();
            long endTime = System.currentTimeMillis();
            int duration = (int) ((endTime - startTime) / 1000);
            
            // 检查备份结果
            File backupFile = new File(fullBackupPath);
            if (exitCode == 0 && backupFile.exists()) {
                logger.info("数据库备份成功：{}", fullBackupPath);
                result.setSuccess(true);
                result.setBackupFile(backupFileName);
                result.setBackupPath(fullBackupPath);
                result.setBackupSize(backupFile.length());
                result.setDuration(duration);
            } else {
                logger.error("数据库备份失败，错误代码：{}，输出信息：{}", exitCode, outputInfo.toString());
                result.setSuccess(false);
                result.setErrorMessage("备份失败，错误代码：" + exitCode + "，详情：" + outputInfo.toString());
                result.setDuration(duration);
            }
        } catch (Exception e) {
            logger.error("执行数据库备份异常", e);
            result.setSuccess(false);
            result.setErrorMessage("备份异常：" + e.getMessage());
            result.setDuration((int) ((System.currentTimeMillis() - startTime) / 1000));
        }
        
        return result;
    }
    
    /**
     * 构建mysqldump命令
     */
    private static List<String> buildMysqlDumpCommand(String outputFilePath) {
        List<String> command = new ArrayList<>();
        
        // 检查操作系统
        boolean isWindows = System.getProperty("os.name").toLowerCase().contains("windows");
        
        if (isWindows) {
            // Windows系统
            command.add("cmd.exe");
            command.add("/c");
            command.add("mysqldump");
        } else {
            // Linux/Mac系统
            command.add("mysqldump");
        }
        
        // 添加连接参数
        command.add("--host=" + DB_HOST);
        command.add("--port=" + DB_PORT);
        command.add("--user=" + DB_USER);
        command.add("--password=" + DB_PASSWORD);
        
        // 添加导出选项
        command.add("--set-charset");
        command.add("--routines");
        command.add("--events");
        command.add("--triggers");
        command.add("--single-transaction");
        command.add("--databases");
        command.add(DB_NAME);
        
        // 指定输出文件
        command.add("--result-file=" + outputFilePath);
        
        return command;
    }
    
    /**
     * 恢复数据库备份
     * 
     * @param backupFilePath 备份文件路径
     * @return 恢复结果，true表示成功，false表示失败
     */
    public static boolean restore(String backupFilePath) {
        logger.info("开始恢复数据库备份：{}", backupFilePath);
        
        try {
            // 检查备份文件是否存在
            File backupFile = new File(backupFilePath);
            if (!backupFile.exists() || !backupFile.isFile()) {
                logger.error("备份文件不存在：{}", backupFilePath);
                return false;
            }
            
            // 构建mysql命令
            List<String> command = buildMysqlRestoreCommand(backupFilePath);
            
            // 执行恢复命令
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();
            
            // 读取输出信息
            StringBuilder outputInfo = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    outputInfo.append(line).append("\n");
                }
            }
            
            // 等待命令执行完成
            int exitCode = process.waitFor();
            
            // 检查恢复结果
            if (exitCode == 0) {
                logger.info("数据库恢复成功");
                return true;
            } else {
                logger.error("数据库恢复失败，错误代码：{}，输出信息：{}", exitCode, outputInfo.toString());
                return false;
            }
        } catch (Exception e) {
            logger.error("执行数据库恢复异常", e);
            return false;
        }
    }
    
    /**
     * 构建mysql恢复命令
     */
    private static List<String> buildMysqlRestoreCommand(String backupFilePath) {
        List<String> command = new ArrayList<>();
        
        // 检查操作系统
        boolean isWindows = System.getProperty("os.name").toLowerCase().contains("windows");
        
        if (isWindows) {
            // Windows系统
            command.add("cmd.exe");
            command.add("/c");
            command.add("mysql");
        } else {
            // Linux/Mac系统
            command.add("mysql");
        }
        
        // 添加连接参数
        command.add("--host=" + DB_HOST);
        command.add("--port=" + DB_PORT);
        command.add("--user=" + DB_USER);
        command.add("--password=" + DB_PASSWORD);
        command.add(DB_NAME);
        
        // 指定输入文件
        command.add("-e");
        command.add("source " + backupFilePath);
        
        return command;
    }
    
    /**
     * 清理过期备份文件
     * 
     * @param backupPath 备份文件路径
     * @param retentionDays 保留天数
     * @return 清理的文件数量
     */
    public static int cleanupBackupFiles(String backupPath, int retentionDays) {
        logger.info("开始清理过期备份文件，保留{}天", retentionDays);
        int cleanedCount = 0;
        
        try {
            // 计算保留日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -retentionDays);
            Date retentionDate = calendar.getTime();
            
            // 获取备份目录
            File backupDir = new File(backupPath);
            if (!backupDir.exists() || !backupDir.isDirectory()) {
                logger.warn("备份目录不存在：{}", backupPath);
                return 0;
            }
            
            // 遍历备份文件
            File[] files = backupDir.listFiles((dir, name) -> name.endsWith(".sql"));
            if (files != null) {
                for (File file : files) {
                    // 检查文件修改时间
                    Date fileDate = new Date(file.lastModified());
                    if (fileDate.before(retentionDate)) {
                        // 删除过期文件
                        if (file.delete()) {
                            logger.info("删除过期备份文件：{}", file.getName());
                            cleanedCount++;
                        } else {
                            logger.warn("无法删除过期备份文件：{}", file.getName());
                        }
                    }
                }
            }
            
            logger.info("清理过期备份文件完成，共删除{}个文件", cleanedCount);
        } catch (Exception e) {
            logger.error("清理过期备份文件异常", e);
        }
        
        return cleanedCount;
    }
    
    /**
     * 计算下次备份时间
     * 
     * @param scheduleType 调度类型(daily/weekly/monthly)
     * @param dayOfWeek 星期几(1-7)
     * @param dayOfMonth 每月第几天(1-31)
     * @param hour 小时(0-23)
     * @param minute 分钟(0-59)
     * @return 下次备份时间
     */
    public static Date calculateNextRunTime(String scheduleType, Integer dayOfWeek, Integer dayOfMonth, int hour, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        
        // 设置小时和分钟
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        
        // 如果当前时间已经过了今天的调度时间，从明天开始计算
        Calendar now = Calendar.getInstance();
        if (now.get(Calendar.HOUR_OF_DAY) > hour || 
            (now.get(Calendar.HOUR_OF_DAY) == hour && now.get(Calendar.MINUTE) >= minute)) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        
        switch (scheduleType) {
            case "daily":
                // 每日备份，不需要额外设置
                break;
                
            case "weekly":
                // 每周备份，设置到指定的星期几
                if (dayOfWeek != null) {
                    // Calendar中星期日为1，星期一为2，以此类推
                    while (calendar.get(Calendar.DAY_OF_WEEK) != dayOfWeek) {
                        calendar.add(Calendar.DAY_OF_MONTH, 1);
                    }
                }
                break;
                
            case "monthly":
                // 每月备份，设置到指定的日期
                if (dayOfMonth != null) {
                    // 设置为指定日期
                    calendar.set(Calendar.DAY_OF_MONTH, Math.min(dayOfMonth, calendar.getActualMaximum(Calendar.DAY_OF_MONTH)));
                    
                    // 如果当前日期已经过了本月的调度日期，设置为下个月
                    if (calendar.getTimeInMillis() < now.getTimeInMillis()) {
                        calendar.add(Calendar.MONTH, 1);
                        calendar.set(Calendar.DAY_OF_MONTH, Math.min(dayOfMonth, calendar.getActualMaximum(Calendar.DAY_OF_MONTH)));
                    }
                }
                break;
        }
        
        return calendar.getTime();
    }
    
    /**
     * 备份结果类
     */
    public static class BackupResult {
        private boolean success;
        private String backupFile;
        private String backupPath;
        private long backupSize;
        private String errorMessage;
        private int duration;
        
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getBackupFile() {
            return backupFile;
        }
        
        public void setBackupFile(String backupFile) {
            this.backupFile = backupFile;
        }
        
        public String getBackupPath() {
            return backupPath;
        }
        
        public void setBackupPath(String backupPath) {
            this.backupPath = backupPath;
        }
        
        public long getBackupSize() {
            return backupSize;
        }
        
        public void setBackupSize(long backupSize) {
            this.backupSize = backupSize;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
        
        public int getDuration() {
            return duration;
        }
        
        public void setDuration(int duration) {
            this.duration = duration;
        }
    }
} 