<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid py-4">
    <!-- 返回按钮 -->
    <div class="mb-4">
        <a href="${pageContext.request.contextPath}/device/monitor" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left"></i> 返回监控面板
        </a>
    </div>

    <!-- 设备基本信息和实时参数 -->
    <!-- 设备名称和状态 -->
    <div class="card shadow-sm mb-4">
        <div class="card-body p-4">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div class="d-flex align-items-center flex-wrap mb-2 mb-md-0">
                    <h4 class="card-title mb-0 me-3">${device.name}</h4>
                    <span class="badge bg-secondary me-2 mb-2 mb-md-0">${device.type}</span>
                    <span class="badge ${device.powerStatus == 1 ? 'bg-success' : 'bg-danger'} power-status mb-2 mb-md-0">${device.powerStatus == 1 ? '开启' : '关闭'}</span>
                </div>
                <div class="d-flex align-items-center">
                    <span class="status-indicator ${device.connectionStatus == 1 ? 'online' : 'offline'} me-2"></span>
                    <span class="connection-status fw-bold">${device.connectionStatus == 1 ? '在线' : '离线'}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 实时参数 -->
    <div class="card shadow-sm mb-4">
        <div class="card-header p-4">
            <h5 class="card-title mb-0">实时参数</h5>
        </div>
        <div class="card-body p-4">
            <div class="row g-4">
                <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                    <div class="card param-card bg-light h-100">
                        <div class="card-body p-4">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title mb-3">温度</h6>
                                    <h3 class="temperature-value mb-0">${device.temperature}°C</h3>
                                </div>
                                <i class="bi bi-thermometer-half text-danger fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                    <div class="card param-card bg-light h-100">
                        <div class="card-body p-4">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title mb-3">湿度</h6>
                                    <h3 class="humidity-value mb-0">${device.humidity}%</h3>
                                </div>
                                <i class="bi bi-droplet-half text-primary fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                    <div class="card param-card bg-light h-100">
                        <div class="card-body p-4">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title mb-3">功耗</h6>
                                    <h3 class="power-value mb-0">${device.powerConsumption}W</h3>
                                </div>
                                <i class="bi bi-lightning-charge text-warning fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                    <div class="card param-card bg-light h-100">
                        <div class="card-body p-4">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title mb-3">运行时间</h6>
                                    <h3 class="runtime-value mb-0">${device.runtime}分钟</h3>
                                </div>
                                <i class="bi bi-clock-history text-success fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备控制 -->
    <div class="card device-info-card mb-4">
        <div class="card-header p-4">
            <h5 class="card-title mb-0">设备控制</h5>
        </div>
        <div class="card-body p-4">
            <div class="row">
                <div class="col-lg-4 col-md-4 mb-3">
                    <button class="btn btn-lg btn-success control-btn py-3 w-100" data-device-id="${device.id}" data-action="on">
                        <i class="bi bi-power me-2 fs-4"></i> 开启设备
                    </button>
                </div>
                <div class="col-lg-4 col-md-4 mb-3">
                    <button class="btn btn-lg btn-danger control-btn py-3 w-100" data-device-id="${device.id}" data-action="off">
                        <i class="bi bi-power me-2 fs-4"></i> 关闭设备
                    </button>
                </div>
                <div class="col-lg-4 col-md-4 mb-3">
                    <button class="btn btn-lg btn-warning control-btn py-3 w-100" data-device-id="${device.id}" data-action="maintain">
                        <i class="bi bi-tools me-2 fs-4"></i> 设备维护
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备详细信息 -->
    <div class="card device-info-card mb-4" data-device-id="${device.id}">
                <div class="card-header p-4">
                    <h5 class="card-title mb-3">设备详细信息</h5>
                    <ul class="nav nav-tabs card-header-tabs" id="deviceInfoTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic-info" type="button" role="tab" aria-controls="basic-info" aria-selected="true">基本信息</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="technical-tab" data-bs-toggle="tab" data-bs-target="#technical-info" type="button" role="tab" aria-controls="technical-info" aria-selected="false">技术规格</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="network-tab" data-bs-toggle="tab" data-bs-target="#network-info" type="button" role="tab" aria-controls="network-info" aria-selected="false">网络信息</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="maintenance-info-tab" data-bs-toggle="tab" data-bs-target="#maintenance-info" type="button" role="tab" aria-controls="maintenance-info" aria-selected="false">维护信息</button>
                        </li>
                    </ul>
                </div>
                <div class="card-body p-4">
                    <div class="tab-content" id="deviceInfoTabsContent">
                        <!-- 基本信息 -->
                        <div class="tab-pane fade show active" id="basic-info" role="tabpanel" aria-labelledby="basic-tab">
                            <div class="row g-4 mt-2">
                                <div class="col-lg-6 col-md-6">
                                    <p class="mb-3"><strong>设备名称：</strong> ${device.name}</p>
                                    <p class="mb-3"><strong>设备类型：</strong> ${device.type}</p>
                                    <p class="mb-3"><strong>位置：</strong> ${device.location}</p>
                                    <p class="mb-3"><strong>状态：</strong> <span class="badge bg-${device.status == '使用中' ? 'success' : device.status == '维修中' ? 'warning' : device.status == '已报废' ? 'danger' : 'secondary'}">${device.status}</span></p>
                                    <p class="mb-3"><strong>电源状态：</strong> <span class="badge ${device.powerStatus == 1 ? 'bg-success' : 'bg-danger'}">${device.powerStatus == 1 ? '开启' : '关闭'}</span></p>
                                    <p class="mb-3"><strong>连接状态：</strong> <span class="badge ${device.connectionStatus == 1 ? 'bg-success' : 'bg-danger'}">${device.connectionStatus == 1 ? '在线' : '离线'}</span></p>
                                </div>
                                <div class="col-lg-6 col-md-6">
                                    <p class="mb-3"><strong>制造商：</strong> ${device.manufacturer}</p>
                                    <p class="mb-3"><strong>型号：</strong> ${device.model}</p>
                                    <p class="mb-3"><strong>序列号：</strong> ${device.serialNumber}</p>
                                    <p class="mb-3"><strong>固件版本：</strong> ${device.firmwareVersion}</p>
                                    <p class="mb-3"><strong>最后在线时间：</strong> ${device.lastOnlineTime}</p>
                                    <p class="mb-3"><strong>累计运行时间：</strong> ${device.runtime} 分钟 (约 ${Math.round(device.runtime/60)} 小时)</p>
                                </div>
                            </div>
                            <div class="mt-4">
                                <p class="mb-0"><strong>描述：</strong> ${device.description}</p>
                            </div>
                        </div>

                        <!-- 技术规格 -->
                        <div class="tab-pane fade" id="technical-info" role="tabpanel" aria-labelledby="technical-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="card border-light mb-3">
                                        <div class="card-header">性能参数</div>
                                        <div class="card-body">
                                            <p class="mb-2"><strong>最高温度：</strong> ${device.temperature + 10}°C (理论值)</p>
                                            <p class="mb-2"><strong>最低温度：</strong> ${device.temperature - 20}°C (理论值)</p>
                                            <p class="mb-2"><strong>最大功耗：</strong> ${device.powerConsumption * 1.5}W (理论值)</p>
                                            <p class="mb-2"><strong>待机功耗：</strong> ${device.powerConsumption * 0.3}W (理论值)</p>
                                            <p class="mb-2"><strong>工作湿度范围：</strong> 20% - 85%</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-light mb-3">
                                        <div class="card-header">物理特性</div>
                                        <div class="card-body">
                                            <p class="mb-2"><strong>设备尺寸：</strong> 根据型号而定</p>
                                            <p class="mb-2"><strong>设备重量：</strong> 根据型号而定</p>
                                            <p class="mb-2"><strong>外壳材质：</strong> 工程塑料/金属</p>
                                            <p class="mb-2"><strong>安装方式：</strong> 壁挂/吊装/桌面</p>
                                            <p class="mb-2"><strong>接口类型：</strong> HDMI/USB/网络接口</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card border-light mt-3">
                                <div class="card-header">功能特性</div>
                                <div class="card-body">
                                    <p class="mb-2"><strong>支持协议：</strong> HTTP, HTTPS, RTSP, ONVIF</p>
                                    <p class="mb-2"><strong>支持功能：</strong> 远程控制, 定时开关机, 自动调节</p>
                                    <p class="mb-2"><strong>兼容系统：</strong> Windows, macOS, Android, iOS</p>
                                    <p class="mb-2"><strong>认证标准：</strong> CE, FCC, RoHS</p>
                                </div>
                            </div>
                        </div>

                        <!-- 网络信息 -->
                        <div class="tab-pane fade" id="network-info" role="tabpanel" aria-labelledby="network-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="card border-light mb-3">
                                        <div class="card-header">网络配置</div>
                                        <div class="card-body">
                                            <p class="mb-2"><strong>IP地址：</strong> ${device.ipAddress}</p>
                                            <p class="mb-2"><strong>MAC地址：</strong> ${device.macAddress}</p>
                                            <p class="mb-2"><strong>子网掩码：</strong> ************* (默认)</p>
                                            <p class="mb-2"><strong>默认网关：</strong> ${device.ipAddress.substring(0, device.ipAddress.lastIndexOf('.')+1)}1 (推测)</p>
                                            <p class="mb-2"><strong>DNS服务器：</strong> 自动获取</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-light mb-3">
                                        <div class="card-header">连接状态</div>
                                        <div class="card-body">
                                            <p class="mb-2"><strong>连接类型：</strong> 有线网络</p>
                                            <p class="mb-2"><strong>连接状态：</strong> <span class="badge ${device.connectionStatus == 1 ? 'bg-success' : 'bg-danger'}">${device.connectionStatus == 1 ? '在线' : '离线'}</span></p>
                                            <p class="mb-2"><strong>最后在线时间：</strong> ${device.lastOnlineTime}</p>
                                            <p class="mb-3"><strong>连接质量：</strong></p>
                                            <div class="progress mb-3 progress-container">
                                                <div class="progress-bar bg-success progress-bar-custom" role="progressbar"
                                                    style="width: ${device.connectionStatus == 1 ? '95' : '0'}%"
                                                    aria-valuenow="${device.connectionStatus == 1 ? '95' : '0'}"
                                                    aria-valuemin="0"
                                                    aria-valuemax="100">
                                                    ${device.connectionStatus == 1 ? '良好' : '断开'}
                                                </div>
                                            </div>
                                            <p class="mb-2"><strong>网络延迟：</strong> ${device.connectionStatus == 1 ? '<span class="text-success">< 10ms</span>' : '<span class="text-danger">不可用</span>'}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card border-light mt-3">
                                <div class="card-header">远程访问</div>
                                <div class="card-body">
                                    <p class="mb-2"><strong>Web管理界面：</strong> <a href="http://${device.ipAddress}" target="_blank" class="btn btn-sm btn-outline-primary ${device.connectionStatus == 1 ? '' : 'disabled'}">访问设备管理界面</a></p>
                                    <p class="mb-2"><strong>远程控制端口：</strong> 80, 443, 8080</p>
                                    <p class="mb-2"><strong>API接口：</strong> <code>http://${device.ipAddress}/api/</code></p>
                                    <p class="mb-2"><strong>安全性：</strong> 基本认证, HTTPS (可选)</p>
                                </div>
                            </div>
                        </div>

                        <!-- 维护信息 -->
                        <div class="tab-pane fade" id="maintenance-info" role="tabpanel" aria-labelledby="maintenance-info-tab">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="card border-light mb-3">
                                        <div class="card-header">维护计划</div>
                                        <div class="card-body">
                                            <p class="mb-2"><strong>最后维护日期：</strong> ${device.lastMaintenanceDate}</p>
                                            <p class="mb-2"><strong>下次维护日期：</strong> ${device.nextMaintenanceDate}</p>
                                            <p class="mb-2"><strong>维护周期：</strong> 3个月</p>
                                            <p class="mb-2"><strong>维护类型：</strong> 常规检查, 清洁, 固件更新</p>
                                            <p class="mb-2"><strong>维护状态：</strong>
                                                <c:choose>
                                                    <c:when test="${device.status == '维修中'}">
                                                        <span class="badge bg-warning">维修中</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <span class="badge bg-success">正常</span>
                                                    </c:otherwise>
                                                </c:choose>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-light mb-3">
                                        <div class="card-header">设备健康</div>
                                        <div class="card-body">
                                            <p class="mb-2"><strong>设备状态：</strong>
                                                <span class="badge bg-${device.status == '使用中' ? 'success' : device.status == '维修中' ? 'warning' : device.status == '已报废' ? 'danger' : 'secondary'}">${device.status}</span>
                                            </p>
                                            <p class="mb-2"><strong>运行时间：</strong> ${device.runtime} 分钟</p>
                                            <p class="mb-3"><strong>健康评分：</strong></p>
                                            <div class="progress mb-3 progress-container">
                                                <div class="progress-bar ${device.status == '使用中' ? 'bg-success' : device.status == '维修中' ? 'bg-warning' : 'bg-danger'} progress-bar-custom"
                                                     role="progressbar"
                                                     style="width: ${device.status == '使用中' ? '90' : device.status == '维修中' ? '60' : '30'}%"
                                                     aria-valuenow="${device.status == '使用中' ? '90' : device.status == '维修中' ? '60' : '30'}"
                                                     aria-valuemin="0"
                                                     aria-valuemax="100">
                                                    ${device.status == '使用中' ? '良好' : device.status == '维修中' ? '需要维护' : '状态不佳'}
                                                </div>
                                            </div>
                                            <p class="mb-2"><strong>告警状态：</strong>
                                                <c:choose>
                                                    <c:when test="${not empty device.alerts}">
                                                        <span class="badge bg-warning">${device.alerts.size()} 个未解决告警</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <span class="badge bg-success">无告警</span>
                                                    </c:otherwise>
                                                </c:choose>
                                            </p>
                                            <p class="mb-2"><strong>预计寿命：</strong> 5年 (标准使用条件下)</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card border-light mt-3">
                                <div class="card-header">维护文档</div>
                                <div class="card-body">
                                    <p class="mb-2"><strong>用户手册：</strong> <a href="#" class="btn btn-sm btn-outline-primary">下载PDF</a></p>
                                    <p class="mb-2"><strong>维护指南：</strong> <a href="#" class="btn btn-sm btn-outline-primary">下载PDF</a></p>
                                    <p class="mb-2"><strong>故障排除：</strong> <a href="#" class="btn btn-sm btn-outline-primary">查看指南</a></p>
                                    <p class="mb-2"><strong>联系支持：</strong> <a href="mailto:<EMAIL>" class="btn btn-sm btn-outline-primary">发送邮件</a></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    <!-- 参数监控图表 -->
    <div class="card shadow-sm mb-5">
        <div class="card-header p-4">
            <h5 class="card-title mb-3">历史数据</h5>
            <ul class="nav nav-tabs card-header-tabs" id="historyTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="temperature-tab" data-bs-toggle="tab" data-bs-target="#temperature-content" type="button" role="tab" aria-controls="temperature-content" aria-selected="true">温度历史</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="humidity-tab" data-bs-toggle="tab" data-bs-target="#humidity-content" type="button" role="tab" aria-controls="humidity-content" aria-selected="false">湿度历史</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="power-tab" data-bs-toggle="tab" data-bs-target="#power-content" type="button" role="tab" aria-controls="power-content" aria-selected="false">功耗历史</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="runtime-tab" data-bs-toggle="tab" data-bs-target="#runtime-content" type="button" role="tab" aria-controls="runtime-content" aria-selected="false">运行时间历史</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="parameter-data-tab" data-bs-toggle="tab" data-bs-target="#parameter-data-content" type="button" role="tab" aria-controls="parameter-data-content" aria-selected="false">参数数据表</button>
                </li>
            </ul>
        </div>
        <div class="card-body p-4">
            <div class="tab-content" id="historyTabsContent">
                <!-- 温度历史 -->
                <div class="tab-pane fade show active" id="temperature-content" role="tabpanel" aria-labelledby="temperature-tab">
                    <div class="row mb-4 mt-3">
                        <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                            <div class="card border-danger h-100">
                                <div class="card-body text-center p-4">
                                    <h6 class="card-title text-muted mb-3">当前温度</h6>
                                    <h3 class="temperature-value text-danger mb-0">${device.temperature}°C</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                            <div class="card border-light h-100">
                                <div class="card-body text-center p-4">
                                    <h6 class="card-title text-muted mb-3">最高温度</h6>
                                    <h3 class="text-danger mb-0">${device.temperature + 5}°C</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                            <div class="card border-light h-100">
                                <div class="card-body text-center p-4">
                                    <h6 class="card-title text-muted mb-3">最低温度</h6>
                                    <h3 class="text-primary mb-0">${device.temperature - 10}°C</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
                            <div class="card border-light h-100">
                                <div class="card-body text-center p-4">
                                    <h6 class="card-title text-muted mb-3">平均温度</h6>
                                    <h3 class="text-secondary mb-0">${device.temperature - 2}°C</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="height: 400px;">
                        <canvas id="temperatureChart" class="parameter-chart"></canvas>
                    </div>
                </div>

                <!-- 湿度历史 -->
                <div class="tab-pane fade" id="humidity-content" role="tabpanel" aria-labelledby="humidity-tab">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">当前湿度</h6>
                                    <h3 class="humidity-value text-primary">${device.humidity}%</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">最高湿度</h6>
                                    <h3 class="text-primary">${device.humidity + 10}%</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">最低湿度</h6>
                                    <h3 class="text-primary">${device.humidity - 5}%</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">平均湿度</h6>
                                    <h3 class="text-secondary">${device.humidity - 1}%</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="height: 350px;">
                        <canvas id="humidityChart" class="parameter-chart"></canvas>
                    </div>
                </div>

                <!-- 功耗历史 -->
                <div class="tab-pane fade" id="power-content" role="tabpanel" aria-labelledby="power-tab">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">当前功耗</h6>
                                    <h3 class="power-value text-warning">${device.powerConsumption}W</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">最高功耗</h6>
                                    <h3 class="text-warning">${device.powerConsumption * 1.2}W</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">最低功耗</h6>
                                    <h3 class="text-warning">${device.powerConsumption * 0.5}W</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">平均功耗</h6>
                                    <h3 class="text-secondary">${device.powerConsumption * 0.8}W</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="height: 350px;">
                        <canvas id="powerChart" class="parameter-chart"></canvas>
                    </div>
                </div>

                <!-- 运行时间历史 -->
                <div class="tab-pane fade" id="runtime-content" role="tabpanel" aria-labelledby="runtime-tab">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">当前运行时间</h6>
                                    <h3 class="runtime-value text-success">${device.runtime}分钟</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">今日运行</h6>
                                    <h3 class="text-success">${device.runtime < 1440 ? device.runtime : 1440}分钟</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">本周运行</h6>
                                    <h3 class="text-success">${device.runtime}分钟</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">总运行时间</h6>
                                    <h3 class="text-secondary">${device.runtime}分钟</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="height: 350px;">
                        <canvas id="runtimeChart" class="parameter-chart"></canvas>
                    </div>
                </div>

                <!-- 参数数据表 -->
                <div class="tab-pane fade" id="parameter-data-content" role="tabpanel" aria-labelledby="parameter-data-tab">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>时间</th>
                                    <th>参数类型</th>
                                    <th>参数值</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach items="${device.parameters}" var="param" varStatus="status" begin="0" end="19">
                                    <tr>
                                        <td>${param.recordTime}</td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${param.parameterName == 'temperature'}">温度</c:when>
                                                <c:when test="${param.parameterName == 'humidity'}">湿度</c:when>
                                                <c:when test="${param.parameterName == 'power_consumption'}">功耗</c:when>
                                                <c:when test="${param.parameterName == 'runtime'}">运行时间</c:when>
                                                <c:when test="${param.parameterName == 'power_status'}">电源状态</c:when>
                                                <c:otherwise>${param.parameterName}</c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${param.parameterName == 'temperature'}">${param.parameterValue}°C</c:when>
                                                <c:when test="${param.parameterName == 'humidity'}">${param.parameterValue}%</c:when>
                                                <c:when test="${param.parameterName == 'power_consumption'}">${param.parameterValue}W</c:when>
                                                <c:when test="${param.parameterName == 'runtime'}">${param.parameterValue}分钟</c:when>
                                                <c:when test="${param.parameterName == 'power_status'}">${param.parameterValue == '1' ? '开启' : '关闭'}</c:when>
                                                <c:otherwise>${param.parameterValue}</c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${param.parameterName == 'temperature' && param.parameterValue > 40}">
                                                    <span class="badge bg-danger">过高</span>
                                                </c:when>
                                                <c:when test="${param.parameterName == 'humidity' && param.parameterValue > 80}">
                                                    <span class="badge bg-danger">过高</span>
                                                </c:when>
                                                <c:when test="${param.parameterName == 'power_consumption' && param.parameterValue > 500}">
                                                    <span class="badge bg-danger">过高</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span class="badge bg-success">正常</span>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </tr>
                                </c:forEach>
                                <c:if test="${empty device.parameters}">
                                    <tr>
                                        <td colspan="4" class="text-center">暂无参数历史数据</td>
                                    </tr>
                                </c:if>
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <span class="text-muted">显示最近20条记录</span>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-download"></i> 导出数据
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 告警历史和维护记录 -->
    <div class="card mt-5 shadow-sm mb-5">
        <div class="card-header p-4">
            <h5 class="card-title mb-3">告警与维护</h5>
            <ul class="nav nav-tabs card-header-tabs" id="recordTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="alerts-tab" data-bs-toggle="tab" data-bs-target="#alerts-content" type="button" role="tab" aria-controls="alerts-content" aria-selected="true">告警历史</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="maintenance-tab" data-bs-toggle="tab" data-bs-target="#maintenance-content" type="button" role="tab" aria-controls="maintenance-content" aria-selected="false">维护记录</button>
                </li>
            </ul>
        </div>
        <div class="card-body p-4">
            <div class="tab-content" id="recordTabsContent">
                <!-- 告警历史 -->
                <div class="tab-pane fade show active" id="alerts-content" role="tabpanel" aria-labelledby="alerts-tab">
                    <div class="mt-3 mb-3">
                        <c:forEach items="${device.alerts}" var="alert">
                            <div class="card alert-card ${alert.alertLevel == 3 ? 'high' : alert.alertLevel == 2 ? 'medium' : 'low'} mb-4" data-alert-id="${alert.id}">
                                <div class="card-body p-4">
                                    <div class="d-flex justify-content-between align-items-start flex-wrap">
                                        <div class="mb-3 mb-md-0">
                                            <h5 class="card-title mb-3">
                                                <span class="badge ${alert.alertLevel == 3 ? 'bg-danger' : alert.alertLevel == 2 ? 'bg-warning' : 'bg-success'} me-2">
                                                    ${alert.alertLevel == 3 ? '严重' : alert.alertLevel == 2 ? '警告' : '提示'}
                                                </span>
                                                ${alert.alertType}
                                            </h5>
                                            <p class="card-text mb-3">${alert.alertMessage}</p>
                                            <p class="alert-time mb-0">
                                                <strong>发生时间:</strong> ${alert.createTime}
                                                <span class="ms-3 alert-status"><strong>状态:</strong> ${alert.isResolved == 1 ? '已解决' : '未解决'}</span>
                                                <c:if test="${alert.isResolved == 1}">
                                                    <span class="ms-3"><strong>解决时间:</strong> ${alert.resolveTime}</span>
                                                </c:if>
                                            </p>
                                        </div>
                                        <div class="mt-2 mt-md-0">
                                            <c:if test="${alert.isResolved == 0}">
                                                <button class="btn btn-sm btn-success resolve-alert-btn px-3 py-2" data-alert-id="${alert.id}">
                                                    <i class="bi bi-check-circle me-1"></i> 标记为已解决
                                                </button>
                                            </c:if>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </c:forEach>

                        <c:if test="${empty device.alerts}">
                            <div class="alert alert-success p-4 mt-3">
                                <i class="bi bi-check-circle-fill me-2"></i> 该设备没有告警记录
                            </div>
                        </c:if>
                    </div>
                </div>

                <!-- 维护记录 -->
                <div class="tab-pane fade" id="maintenance-content" role="tabpanel" aria-labelledby="maintenance-tab">
                    <div class="mt-3 mb-3">
                        <c:forEach items="${maintenances}" var="maintenance">
                            <div class="card maintenance-card mb-4">
                                <div class="card-body p-4">
                                    <h5 class="card-title mb-3">${maintenance.maintenanceType}</h5>
                                    <p class="card-text mb-4">${maintenance.maintenanceDesc}</p>
                                    <div class="row g-3 mb-3">
                                        <div class="col-lg-6 col-md-6">
                                            <p class="mb-2"><strong>维护结果：</strong> ${maintenance.maintenanceResult}</p>
                                        </div>
                                        <div class="col-lg-6 col-md-6">
                                            <p class="mb-2"><strong>维护人员：</strong> ${maintenance.maintenancePerson}</p>
                                        </div>
                                    </div>
                                    <p class="maintenance-time mb-0"><strong>维护时间:</strong> ${maintenance.maintenanceTime}</p>
                                </div>
                            </div>
                        </c:forEach>

                        <c:if test="${empty maintenances}">
                            <div class="alert alert-info p-4 mt-3">
                                <i class="bi bi-info-circle-fill me-2"></i> 该设备没有维护记录
                            </div>
                        </c:if>

                        <!-- 添加维护记录按钮 -->
                        <div class="mt-4 text-center">
                            <a href="${pageContext.request.contextPath}/device/maintenance/add?deviceId=${device.id}" class="btn btn-primary btn-lg px-4 py-2">
                                <i class="bi bi-plus-circle me-2"></i> 添加维护记录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
