package com.building.servlet;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Random;

import javax.imageio.ImageIO;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

/**
 * 验证码生成控制器
 * 生成图片验证码并存储在会话中
 * 用于登录时的安全验证
 * 
 * <AUTHOR>
 * @date 2024-03-04
 */
@WebServlet("/captcha")
public class CaptchaServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    /**
     * 处理验证码生成请求
     * 生成随机验证码图片并输出
     * 
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws ServletException 如果处理请求时发生错误
     * @throws IOException 如果发生I/O错误
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 创建图像缓冲区
        int width = 100;    // 图片宽度
        int height = 38;    // 图片高度
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        
        // 设置背景色
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, width, height);
        
        // 设置字体
        g.setFont(new Font("Arial", Font.BOLD, 28));
        
        // 生成随机验证码
        String captcha = generateCaptcha();
        HttpSession session = request.getSession();
        session.setAttribute("captcha", captcha);
        
        // 绘制验证码
        g.setColor(Color.BLACK);
        g.drawString(captcha, 10, 30);
        
        // 添加干扰线
        Random random = new Random();
        for (int i = 0; i < 5; i++) {
            g.setColor(new Color(random.nextInt(256), random.nextInt(256), random.nextInt(256)));
            g.drawLine(random.nextInt(width), random.nextInt(height),
                    random.nextInt(width), random.nextInt(height));
        }
        
        // 输出图像
        response.setContentType("image/jpeg");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        ImageIO.write(image, "JPEG", response.getOutputStream());
    }
    
    /**
     * 生成随机验证码
     * 使用指定的字符集生成4位随机字符
     * 排除容易混淆的字符（如0、O、1、I等）
     * 
     * @return 4位随机验证码字符串
     */
    private String generateCaptcha() {
        String chars = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ";
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 4; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
        // return "1";
    }
} 