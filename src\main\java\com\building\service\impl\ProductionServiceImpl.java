package com.building.service.impl;

import com.building.dao.ProductionDao;
import com.building.model.ProductionLine;
import com.building.service.ProductionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProductionServiceImpl implements ProductionService {

    @Autowired
    private ProductionDao productionDao;

    @Override
    public List<ProductionLine> getAllProductionLines() {
        return productionDao.getAllProductionLines();
    }

    @Override
    public void addProductionLine(ProductionLine line) {
        productionDao.addProductionLine(line);
    }

    @Override
    public ProductionLine getProductionLine(int id) {
        return productionDao.getProductionLine(id);
    }

    @Override
    public void updateProductionLine(ProductionLine line) {
        productionDao.updateProductionLine(line);
    }

    @Override
    public void deleteProductionLine(int id) {
        productionDao.deleteProductionLine(id);
    }
} 