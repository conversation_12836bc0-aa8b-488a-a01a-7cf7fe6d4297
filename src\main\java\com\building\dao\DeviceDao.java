package com.building.dao;

import com.building.model.Device;
import com.building.util.DBUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

public class DeviceDao {

    public List<Device> getAllDevices() {
        List<Device> devices = new ArrayList<>();
        String sql = "SELECT * FROM device ORDER BY id";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                Device device = new Device();
                device.setId(rs.getInt("id"));
                device.setName(rs.getString("name"));
                device.setType(rs.getString("type"));
                device.setLocation(rs.getString("location"));
                device.setStatus(rs.getString("status"));
                device.setLastMaintenanceDate(rs.getString("last_maintenance_date"));
                device.setNextMaintenanceDate(rs.getString("next_maintenance_date"));
                device.setManufacturer(rs.getString("manufacturer"));
                device.setModel(rs.getString("model"));
                device.setSerialNumber(rs.getString("serial_number"));
                device.setDescription(rs.getString("description"));

                // 获取新增字段
                device.setIpAddress(rs.getString("ip_address"));
                device.setMacAddress(rs.getString("mac_address"));
                device.setPowerStatus(rs.getInt("power_status"));
                device.setTemperature(rs.getDouble("temperature"));
                device.setHumidity(rs.getDouble("humidity"));
                device.setPowerConsumption(rs.getDouble("power_consumption"));
                device.setRuntime(rs.getInt("runtime"));
                device.setFirmwareVersion(rs.getString("firmware_version"));
                device.setLastOnlineTime(rs.getString("last_online_time"));
                device.setConnectionStatus(rs.getInt("connection_status"));

                devices.add(device);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return devices;
    }

    public Device getDeviceById(int id) {
        String sql = "SELECT * FROM device WHERE id = ?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setInt(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Device device = new Device();
                    device.setId(rs.getInt("id"));
                    device.setName(rs.getString("name"));
                    device.setType(rs.getString("type"));
                    device.setLocation(rs.getString("location"));
                    device.setStatus(rs.getString("status"));
                    device.setLastMaintenanceDate(rs.getString("last_maintenance_date"));
                    device.setNextMaintenanceDate(rs.getString("next_maintenance_date"));
                    device.setManufacturer(rs.getString("manufacturer"));
                    device.setModel(rs.getString("model"));
                    device.setSerialNumber(rs.getString("serial_number"));
                    device.setDescription(rs.getString("description"));

                    // 获取新增字段
                    device.setIpAddress(rs.getString("ip_address"));
                    device.setMacAddress(rs.getString("mac_address"));
                    device.setPowerStatus(rs.getInt("power_status"));
                    device.setTemperature(rs.getDouble("temperature"));
                    device.setHumidity(rs.getDouble("humidity"));
                    device.setPowerConsumption(rs.getDouble("power_consumption"));
                    device.setRuntime(rs.getInt("runtime"));
                    device.setFirmwareVersion(rs.getString("firmware_version"));
                    device.setLastOnlineTime(rs.getString("last_online_time"));
                    device.setConnectionStatus(rs.getInt("connection_status"));

                    return device;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }

    public boolean addDevice(Device device) {
        String sql = "INSERT INTO device (name, type, location, status, last_maintenance_date, " +
                    "next_maintenance_date, manufacturer, model, serial_number, description, " +
                    "ip_address, mac_address, power_status, temperature, humidity, " +
                    "power_consumption, runtime, firmware_version, last_online_time, connection_status) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, device.getName());
            stmt.setString(2, device.getType());
            stmt.setString(3, device.getLocation());
            stmt.setString(4, device.getStatus());
            stmt.setString(5, device.getLastMaintenanceDate());
            stmt.setString(6, device.getNextMaintenanceDate());
            stmt.setString(7, device.getManufacturer());
            stmt.setString(8, device.getModel());
            stmt.setString(9, device.getSerialNumber());
            stmt.setString(10, device.getDescription());

            // 设置新增字段
            stmt.setString(11, device.getIpAddress());
            stmt.setString(12, device.getMacAddress());
            stmt.setInt(13, device.getPowerStatus());
            stmt.setDouble(14, device.getTemperature());
            stmt.setDouble(15, device.getHumidity());
            stmt.setDouble(16, device.getPowerConsumption());
            stmt.setInt(17, device.getRuntime());
            stmt.setString(18, device.getFirmwareVersion());
            stmt.setString(19, device.getLastOnlineTime());
            stmt.setInt(20, device.getConnectionStatus());

            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean updateDevice(Device device) {
        String sql = "UPDATE device SET name=?, type=?, location=?, status=?, " +
                    "last_maintenance_date=?, next_maintenance_date=?, manufacturer=?, " +
                    "model=?, serial_number=?, description=?, " +
                    "ip_address=?, mac_address=?, power_status=?, temperature=?, humidity=?, " +
                    "power_consumption=?, runtime=?, firmware_version=?, last_online_time=?, connection_status=? " +
                    "WHERE id=?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, device.getName());
            stmt.setString(2, device.getType());
            stmt.setString(3, device.getLocation());
            stmt.setString(4, device.getStatus());
            stmt.setString(5, device.getLastMaintenanceDate());
            stmt.setString(6, device.getNextMaintenanceDate());
            stmt.setString(7, device.getManufacturer());
            stmt.setString(8, device.getModel());
            stmt.setString(9, device.getSerialNumber());
            stmt.setString(10, device.getDescription());

            // 设置新增字段
            stmt.setString(11, device.getIpAddress());
            stmt.setString(12, device.getMacAddress());
            stmt.setInt(13, device.getPowerStatus());
            stmt.setDouble(14, device.getTemperature());
            stmt.setDouble(15, device.getHumidity());
            stmt.setDouble(16, device.getPowerConsumption());
            stmt.setInt(17, device.getRuntime());
            stmt.setString(18, device.getFirmwareVersion());
            stmt.setString(19, device.getLastOnlineTime());
            stmt.setInt(20, device.getConnectionStatus());

            stmt.setInt(21, device.getId());

            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean deleteDevice(int id) {
        String sql = "DELETE FROM device WHERE id = ?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setInt(1, id);
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    public Map<String, Integer> getDeviceStats() {
        Map<String, Integer> stats = new TreeMap<>();
        String sql = "SELECT COUNT(*) as total, " +
                    "SUM(CASE WHEN status = '空闲' THEN 1 ELSE 0 END) as online, " +
                    "SUM(CASE WHEN status = '使用中' THEN 1 ELSE 0 END) as maintenance " +
                    "FROM device";

        try (Connection conn = DBUtil.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                stats.put("total", rs.getInt("total"));
                stats.put("online", rs.getInt("online"));
                stats.put("maintenance", rs.getInt("maintenance"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
            // 发生错误时返回默认值
            stats.put("total", 0);
            stats.put("online", 0);
            stats.put("maintenance", 0);
        }
        return stats;
    }

    /**
     * 更新设备状态
     * @param deviceId 设备ID
     * @param status 新状态
     * @return 更新是否成功
     */
    public boolean updateDeviceStatus(int deviceId, String status) {
        String sql = "UPDATE device SET status = ? WHERE id = ?";
        System.out.println("执行SQL: " + sql);
        System.out.println("参数: deviceId=" + deviceId + ", status=" + status);

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, status);
            stmt.setInt(2, deviceId);

            int rows = stmt.executeUpdate();
            System.out.println("更新影响的行数: " + rows);
            return rows > 0;
        } catch (SQLException e) {
            System.err.println("更新设备状态时发生SQL错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取设备统计信息
     * @return 返回设备统计数组 [总数, 空闲数, 使用中数, 维修中数, 已报废数]
     */
    public int[] getDeviceStatistics() {
        int[] stats = new int[5];
        String sql = "SELECT " +
                    "COUNT(*) as total, " +
                    "SUM(CASE WHEN status = '空闲' THEN 1 ELSE 0 END) as idle, " +
                    "SUM(CASE WHEN status = '使用中' THEN 1 ELSE 0 END) as in_use, " +
                    "SUM(CASE WHEN status = '维修中' THEN 1 ELSE 0 END) as in_maintenance, " +
                    "SUM(CASE WHEN status = '已报废' THEN 1 ELSE 0 END) as scrapped " +
                    "FROM device";

        try (Connection conn = DBUtil.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                stats[0] = rs.getInt("total");
                stats[1] = rs.getInt("idle");
                stats[2] = rs.getInt("in_use");
                stats[3] = rs.getInt("in_maintenance");
                stats[4] = rs.getInt("scrapped");
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return stats;
    }

    /**
     * 更新设备参数
     * @param deviceId 设备ID
     * @param temperature 温度
     * @param humidity 湿度
     * @param powerConsumption 功耗
     * @param runtime 运行时间
     * @return 是否更新成功
     */
    public boolean updateDeviceParameters(int deviceId, double temperature, double humidity,
                                         double powerConsumption, int runtime) {
        String sql = "UPDATE device SET temperature = ?, humidity = ?, power_consumption = ?, " +
                    "runtime = ?, last_online_time = NOW(), connection_status = 1 WHERE id = ?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setDouble(1, temperature);
            stmt.setDouble(2, humidity);
            stmt.setDouble(3, powerConsumption);
            stmt.setInt(4, runtime);
            stmt.setInt(5, deviceId);

            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 更新设备电源状态
     * @param deviceId 设备ID
     * @param powerStatus 电源状态：0-关闭 1-开启
     * @return 是否更新成功
     */
    public boolean updateDevicePowerStatus(int deviceId, int powerStatus) {
        String sql = "UPDATE device SET power_status = ?, last_online_time = NOW(), connection_status = 1 WHERE id = ?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setInt(1, powerStatus);
            stmt.setInt(2, deviceId);

            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 更新设备连接状态
     * @param deviceId 设备ID
     * @param connectionStatus 连接状态：0-离线 1-在线
     * @return 是否更新成功
     */
    public boolean updateDeviceConnectionStatus(int deviceId, int connectionStatus) {
        String sql = "UPDATE device SET connection_status = ?, last_online_time = NOW() WHERE id = ?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setInt(1, connectionStatus);
            stmt.setInt(2, deviceId);

            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取所有在线设备
     * @return 在线设备列表
     */
    public List<Device> getOnlineDevices() {
        List<Device> devices = new ArrayList<>();
        String sql = "SELECT * FROM device WHERE connection_status = 1 ORDER BY id";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                Device device = new Device();
                device.setId(rs.getInt("id"));
                device.setName(rs.getString("name"));
                device.setType(rs.getString("type"));
                device.setLocation(rs.getString("location"));
                device.setStatus(rs.getString("status"));
                device.setLastMaintenanceDate(rs.getString("last_maintenance_date"));
                device.setNextMaintenanceDate(rs.getString("next_maintenance_date"));
                device.setManufacturer(rs.getString("manufacturer"));
                device.setModel(rs.getString("model"));
                device.setSerialNumber(rs.getString("serial_number"));
                device.setDescription(rs.getString("description"));

                // 获取新增字段
                device.setIpAddress(rs.getString("ip_address"));
                device.setMacAddress(rs.getString("mac_address"));
                device.setPowerStatus(rs.getInt("power_status"));
                device.setTemperature(rs.getDouble("temperature"));
                device.setHumidity(rs.getDouble("humidity"));
                device.setPowerConsumption(rs.getDouble("power_consumption"));
                device.setRuntime(rs.getInt("runtime"));
                device.setFirmwareVersion(rs.getString("firmware_version"));
                device.setLastOnlineTime(rs.getString("last_online_time"));
                device.setConnectionStatus(rs.getInt("connection_status"));

                devices.add(device);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return devices;
    }

    /**
     * 根据教室ID获取设备列表
     * @param roomId 教室ID
     * @return 设备列表
     */
    public List<Device> getDevicesByRoomId(int roomId) {
        List<Device> devices = new ArrayList<>();
        String sql = "SELECT d.* FROM device d " +
                    "JOIN device_position dp ON d.id = dp.device_id " +
                    "WHERE dp.room_id = ?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setInt(1, roomId);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Device device = new Device();
                    device.setId(rs.getInt("id"));
                    device.setName(rs.getString("name"));
                    device.setType(rs.getString("type"));
                    device.setLocation(rs.getString("location"));
                    device.setStatus(rs.getString("status"));
                    device.setLastMaintenanceDate(rs.getString("last_maintenance_date"));
                    device.setNextMaintenanceDate(rs.getString("next_maintenance_date"));
                    device.setManufacturer(rs.getString("manufacturer"));
                    device.setModel(rs.getString("model"));
                    device.setSerialNumber(rs.getString("serial_number"));
                    device.setDescription(rs.getString("description"));

                    // 获取新增字段
                    device.setIpAddress(rs.getString("ip_address"));
                    device.setMacAddress(rs.getString("mac_address"));
                    device.setPowerStatus(rs.getInt("power_status"));
                    device.setTemperature(rs.getDouble("temperature"));
                    device.setHumidity(rs.getDouble("humidity"));
                    device.setPowerConsumption(rs.getDouble("power_consumption"));
                    device.setRuntime(rs.getInt("runtime"));
                    device.setFirmwareVersion(rs.getString("firmware_version"));
                    device.setLastOnlineTime(rs.getString("last_online_time"));
                    device.setConnectionStatus(rs.getInt("connection_status"));

                    devices.add(device);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return devices;
    }
}
