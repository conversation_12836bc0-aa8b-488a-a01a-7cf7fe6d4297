package com.building.servlet;

import com.building.dao.DeviceDao;
import com.building.model.Device;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@WebServlet("/device/list")
public class DeviceListServlet extends HttpServlet {
    private DeviceDao deviceDao;
    private ObjectMapper objectMapper;

    @Override
    public void init() throws ServletException {
        deviceDao = new DeviceDao();
        objectMapper = new ObjectMapper();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 获取设备列表
        List<Device> devices = deviceDao.getAllDevices();
        request.setAttribute("devices", devices);
        
        // 获取设备统计数据
        Map<String, Integer> stats = deviceDao.getDeviceStats();
        request.setAttribute("deviceStats", stats);
        
        request.getRequestDispatcher("/WEB-INF/views/device/list.jsp").forward(request, response);
    }
} 