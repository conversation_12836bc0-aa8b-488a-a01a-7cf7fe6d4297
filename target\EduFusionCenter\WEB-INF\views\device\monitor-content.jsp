<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="text-primary">
                <i class="bi bi-display me-2"></i>设备监控中心
            </h2>
            <p class="text-muted">实时监控设备状态、参数和告警信息</p>
            <hr>
        </div>
    </div>

    <!-- 页面内容分为左右两栏 -->
    <div class="row">
        <!-- 左侧栏：设备状态概览和在线设备列表 -->
        <div class="col-lg-7 mb-4">
            <!-- 第一部分：设备状态概览 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom border-3 border-primary border-top-0 border-end-0 border-start-0">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-grid-3x3-gap-fill text-primary me-2 fs-4"></i>
                        <h5 class="card-title mb-0">设备状态概览</h5>
                    </div>
                </div>
                <div class="card-body p-4">
                    <!-- 设备状态统计 -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card bg-primary text-white h-100 border-0 shadow-sm">
                                <div class="card-body text-center py-4">
                                    <i class="bi bi-pc-display fs-1 mb-2 opacity-75"></i>
                                    <h3>${onlineDevices.size()}</h3>
                                    <p class="mb-0">在线设备</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-success text-white h-100 border-0 shadow-sm">
                                <div class="card-body text-center py-4">
                                    <i class="bi bi-play-circle fs-1 mb-2 opacity-75"></i>
                                    <h3>${onlineDevices.stream().filter(d -> d.getPowerStatus() == 1).count()}</h3>
                                    <p class="mb-0">运行中设备</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-warning h-100 border-0 shadow-sm">
                                <div class="card-body text-center py-4">
                                    <i class="bi bi-exclamation-triangle fs-1 mb-2 opacity-75"></i>
                                    <h3>${unresolvedAlerts.stream().filter(a -> a.getAlertLevel() < 3).count()}</h3>
                                    <p class="mb-0">一般告警</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-danger text-white h-100 border-0 shadow-sm">
                                <div class="card-body text-center py-4">
                                    <i class="bi bi-exclamation-circle fs-1 mb-2 opacity-75"></i>
                                    <h3>${unresolvedAlerts.stream().filter(a -> a.getAlertLevel() == 3).count()}</h3>
                                    <p class="mb-0">严重告警</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 在线设备列表 -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom border-3 border-success border-top-0 border-end-0 border-start-0">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-list-ul text-success me-2 fs-4"></i>
                        <h5 class="card-title mb-0">在线设备列表</h5>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row g-4">
                        <c:forEach items="${onlineDevices}" var="device">
                            <div class="col-xl-6 col-md-12">
                                <div class="card device-card h-100 border-0 shadow-sm hover-shadow" data-device-id="${device.id}">
                                    <div class="card-body p-4">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h5 class="card-title mb-0">${device.name}</h5>
                                            <div>
                                                <span class="status-indicator ${device.connectionStatus == 1 ? 'online' : 'offline'}"></span>
                                                <span class="connection-status">${device.connectionStatus == 1 ? '在线' : '离线'}</span>
                                            </div>
                                        </div>
                                        <p class="card-text text-muted">${device.type} - ${device.location}</p>
                                        <div class="row mt-4 g-3">
                                            <div class="col-4 text-center">
                                                <div class="device-parameter border rounded py-2">
                                                    <i class="bi bi-thermometer-half text-danger"></i>
                                                    <div class="temperature-value">${String.format("%.1f", device.temperature)}°C</div>
                                                    <small>温度</small>
                                                </div>
                                            </div>
                                            <div class="col-4 text-center">
                                                <div class="device-parameter border rounded py-2">
                                                    <i class="bi bi-droplet-half text-primary"></i>
                                                    <div class="humidity-value">${String.format("%.1f", device.humidity)}%</div>
                                                    <small>湿度</small>
                                                </div>
                                            </div>
                                            <div class="col-4 text-center">
                                                <div class="device-parameter border rounded py-2">
                                                    <i class="bi bi-lightning-charge text-warning"></i>
                                                    <div class="power-value">${String.format("%.1f", device.powerConsumption)}W</div>
                                                    <small>功耗</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center mt-4">
                                            <span class="badge ${device.powerStatus == 1 ? 'bg-success' : 'bg-danger'} power-status">
                                                ${device.powerStatus == 1 ? '开启' : '关闭'}
                                            </span>
                                            <div>
                                                <button class="btn btn-sm btn-primary device-detail-btn" data-device-id="${device.id}">
                                                    <i class="bi bi-eye"></i> 详情
                                                </button>
                                                <button class="btn btn-sm btn-success device-control-btn" data-device-id="${device.id}" data-action="on">
                                                    <i class="bi bi-sliders"></i> 控制
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </c:forEach>

                        <c:if test="${empty onlineDevices}">
                            <div class="col-12">
                                <div class="alert alert-info d-flex align-items-center">
                                    <i class="bi bi-info-circle-fill me-2 fs-4"></i>
                                    <span>当前没有在线设备</span>
                                </div>
                            </div>
                        </c:if>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧栏：参数监控图表和未解决告警 -->
        <div class="col-lg-5 mb-4">
            <!-- 第二部分：参数监控图表 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom border-3 border-info border-top-0 border-end-0 border-start-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-graph-up text-info me-2 fs-4"></i>
                            <h5 class="card-title mb-0">参数监控图表</h5>
                        </div>
                        <div class="d-flex align-items-center">
                            <select id="deviceSelector" class="form-select form-select-sm me-2" style="width: auto;">
                                <option value="">选择设备</option>
                                <c:forEach items="${onlineDevices}" var="device">
                                    <option value="${device.id}">${device.name}</option>
                                </c:forEach>
                            </select>
                            <span class="badge bg-info ms-2">实时数据</span>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div id="chartLoadingMessage" class="alert alert-info text-center d-none">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        正在加载设备数据，请稍候...
                    </div>

                    <div id="noDeviceSelectedMessage" class="alert alert-warning text-center">
                        <i class="bi bi-info-circle me-2"></i>
                        请从上方下拉菜单选择一个设备以查看其参数监控图表
                    </div>

                    <div id="chartContainer" class="d-none">
                        <div class="row">
                            <div class="col-12 mb-4">
                                <div class="card shadow-sm h-100 border-0">
                                    <div class="card-header bg-danger bg-opacity-10 border-0">
                                        <h5 class="card-title mb-0">
                                            <i class="bi bi-thermometer-half text-danger me-2"></i>
                                            温度监控
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="temperatureChart" class="parameter-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mb-4">
                                <div class="card shadow-sm h-100 border-0">
                                    <div class="card-header bg-primary bg-opacity-10 border-0">
                                        <h5 class="card-title mb-0">
                                            <i class="bi bi-droplet-half text-primary me-2"></i>
                                            湿度监控
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="humidityChart" class="parameter-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mb-4">
                                <div class="card shadow-sm h-100 border-0">
                                    <div class="card-header bg-warning bg-opacity-10 border-0">
                                        <h5 class="card-title mb-0">
                                            <i class="bi bi-lightning-charge text-warning me-2"></i>
                                            功耗监控
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="powerChart" class="parameter-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第三部分：未解决告警 -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom border-3 border-warning border-top-0 border-end-0 border-start-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-exclamation-triangle-fill text-warning me-2 fs-4"></i>
                            <h5 class="card-title mb-0">未解决告警</h5>
                        </div>
                        <span class="badge bg-${empty unresolvedAlerts ? 'success' : 'danger'} ms-2 px-3 py-2">
                            ${unresolvedAlerts.size()} 个告警
                        </span>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div id="alertsContainer">
                        <c:choose>
                            <c:when test="${empty unresolvedAlerts}">
                                <div class="alert alert-success text-center py-5 mb-0 d-flex align-items-center justify-content-center">
                                    <i class="bi bi-check-circle-fill me-3" style="font-size: 2.5rem;"></i>
                                    <span style="font-size: 1.5rem; font-weight: 500;">当前没有未解决的告警</span>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div class="row">
                                    <c:forEach items="${unresolvedAlerts}" var="alert">
                                        <div class="col-12 mb-4">
                                            <div class="card alert-card ${alert.alertLevel == 3 ? 'high' : alert.alertLevel == 2 ? 'medium' : 'low'} border-0 shadow-sm" data-alert-id="${alert.id}">
                                                <div class="card-body p-4">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h5 class="card-title d-flex align-items-center">
                                                                <span class="badge ${alert.alertLevel == 3 ? 'bg-danger' : alert.alertLevel == 2 ? 'bg-warning' : 'bg-success'} me-2 px-3 py-2">
                                                                    ${alert.alertLevel == 3 ? '严重' : alert.alertLevel == 2 ? '警告' : '提示'}
                                                                </span>
                                                                ${alert.alertType}
                                                            </h5>
                                                            <p class="card-text mt-2">${alert.alertMessage}</p>
                                                            <p class="alert-time text-muted">
                                                                <i class="bi bi-clock me-1"></i>
                                                                发生时间: ${alert.createTime}
                                                            </p>
                                                        </div>
                                                        <div>
                                                            <button class="btn btn-success resolve-alert-btn" data-alert-id="${alert.id}">
                                                                <i class="bi bi-check-circle me-1"></i> 标记为已解决
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加自定义样式 -->
<style>
.hover-shadow:hover {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
    transform: translateY(-3px);
    transition: all 0.3s ease;
}
.alert-card.high {
    border-left: 5px solid #dc3545 !important;
}
.alert-card.medium {
    border-left: 5px solid #ffc107 !important;
}
.alert-card.low {
    border-left: 5px solid #28a745 !important;
}
.parameter-chart {
    min-height: 200px;
}
/* 响应式布局优化 */
@media (max-width: 992px) {
    .col-lg-7, .col-lg-5 {
        width: 100%;
    }
}
/* 设备参数显示样式优化 */
.device-parameter {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    padding: 10px 0;
    border-radius: 8px;
    transition: all 0.3s ease;
}
.device-parameter:hover {
    background-color: rgba(0,0,0,0.03);
    transform: translateY(-2px);
}
.device-parameter i {
    font-size: 1.8rem;
    margin-bottom: 8px;
}
.temperature-value, .humidity-value, .power-value {
    font-size: 1.1rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    text-align: center;
    margin-bottom: 5px;
}
.device-parameter small {
    font-size: 0.85rem;
    color: #6c757d;
}
/* 设备卡片样式优化 */
.device-card {
    transition: all 0.3s ease;
    border-radius: 10px;
}
.device-card .card-body {
    border-radius: 10px;
}
</style>
