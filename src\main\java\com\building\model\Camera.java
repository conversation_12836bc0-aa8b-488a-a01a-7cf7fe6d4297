package com.building.model;

/**
 * 摄像头实体类
 * 对应数据库中的camera表
 */
public class Camera {
    private int id;                 // 摄像头ID
    private String name;            // 摄像头名称
    private String ipAddress;       // IP地址
    private int port;               // 端口号
    private String username;        // 用户名
    private String password;        // 密码
    private String rtspUrl;         // RTSP URL
    private String location;        // 位置
    private String brand;           // 品牌
    private String model;           // 型号
    private int status;             // 状态：0-离线 1-在线
    private String lastOnlineTime;  // 最后在线时间
    private int roomId;             // 所属房间ID
    private String createTime;      // 创建时间
    private String updateTime;      // 更新时间
    
    // 非持久化字段
    private Room room;              // 所属房间
    
    /**
     * 默认构造函数
     */
    public Camera() {
    }
    
    /**
     * 带参数的构造函数
     * @param name 摄像头名称
     * @param ipAddress IP地址
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param rtspUrl RTSP URL
     * @param location 位置
     * @param brand 品牌
     * @param model 型号
     * @param roomId 所属房间ID
     */
    public Camera(String name, String ipAddress, int port, String username, String password, 
                 String rtspUrl, String location, String brand, String model, int roomId) {
        this.name = name;
        this.ipAddress = ipAddress;
        this.port = port;
        this.username = username;
        this.password = password;
        this.rtspUrl = rtspUrl;
        this.location = location;
        this.brand = brand;
        this.model = model;
        this.roomId = roomId;
        this.status = 0; // 默认离线
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getIpAddress() {
        return ipAddress;
    }
    
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
    
    public int getPort() {
        return port;
    }
    
    public void setPort(int port) {
        this.port = port;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getRtspUrl() {
        return rtspUrl;
    }
    
    public void setRtspUrl(String rtspUrl) {
        this.rtspUrl = rtspUrl;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public String getBrand() {
        return brand;
    }
    
    public void setBrand(String brand) {
        this.brand = brand;
    }
    
    public String getModel() {
        return model;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public int getStatus() {
        return status;
    }
    
    public void setStatus(int status) {
        this.status = status;
    }
    
    public String getLastOnlineTime() {
        return lastOnlineTime;
    }
    
    public void setLastOnlineTime(String lastOnlineTime) {
        this.lastOnlineTime = lastOnlineTime;
    }
    
    public int getRoomId() {
        return roomId;
    }
    
    public void setRoomId(int roomId) {
        this.roomId = roomId;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    public String getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
    
    public Room getRoom() {
        return room;
    }
    
    public void setRoom(Room room) {
        this.room = room;
    }
    
    @Override
    public String toString() {
        return "Camera{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", ipAddress='" + ipAddress + '\'' +
                ", port=" + port +
                ", location='" + location + '\'' +
                ", brand='" + brand + '\'' +
                ", model='" + model + '\'' +
                ", status=" + status +
                ", roomId=" + roomId +
                '}';
    }
}
