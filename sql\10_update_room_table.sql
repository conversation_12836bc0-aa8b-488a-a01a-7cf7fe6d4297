-- 更新房间表，添加布局相关字段
ALTER TABLE room
ADD COLUMN capacity INT COMMENT '容纳人数' AFTER description,
ADD COLUMN shape VARCHAR(50) COMMENT '教室形状（矩形、L形等）' AFTER capacity,
ADD COLUMN width INT COMMENT '宽度（厘米）' AFTER shape,
ADD COLUMN length INT COMMENT '长度（厘米）' AFTER width,
ADD COLUMN position VARCHAR(255) COMMENT '在楼层中的位置（坐标，JSON格式）' AFTER length,
ADD COLUMN layout_image_url VARCHAR(255) COMMENT '布局图片URL' AFTER position;

-- 创建教室布局表
CREATE TABLE IF NOT EXISTS room_layout (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_id INT NOT NULL COMMENT '关联的教室ID',
    layout_data TEXT COMMENT '布局数据（JSON格式）',
    image_url VARCHAR(255) COMMENT '布局图片URL',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES room(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教室布局表';

-- 创建设备位置表
CREATE TABLE IF NOT EXISTS device_position (
    id INT PRIMARY KEY AUTO_INCREMENT,
    device_id INT NOT NULL COMMENT '设备ID',
    room_id INT NOT NULL COMMENT '教室ID',
    pos_x DOUBLE NOT NULL COMMENT 'X坐标',
    pos_y DOUBLE NOT NULL COMMENT 'Y坐标',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES device(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES room(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备位置表';
