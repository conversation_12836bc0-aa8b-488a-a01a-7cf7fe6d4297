package com.building.model;

/**
 * 设备告警实体类
 * 对应数据库中的device_alert表
 */
public class DeviceAlert {
    private int id;
    private int deviceId;
    private String alertType;
    private int alertLevel;  // 1-低 2-中 3-高
    private String alertMessage;
    private int isResolved;  // 0-未解决 1-已解决
    private String createTime;
    private String resolveTime;
    
    // 非持久化字段
    private Device device;
    
    /**
     * 默认构造函数
     */
    public DeviceAlert() {
    }
    
    /**
     * 带参数的构造函数
     * @param deviceId 设备ID
     * @param alertType 告警类型
     * @param alertLevel 告警级别
     * @param alertMessage 告警信息
     */
    public DeviceAlert(int deviceId, String alertType, int alertLevel, String alertMessage) {
        this.deviceId = deviceId;
        this.alertType = alertType;
        this.alertLevel = alertLevel;
        this.alertMessage = alertMessage;
        this.isResolved = 0;  // 默认未解决
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public int getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(int deviceId) {
        this.deviceId = deviceId;
    }
    
    public String getAlertType() {
        return alertType;
    }
    
    public void setAlertType(String alertType) {
        this.alertType = alertType;
    }
    
    public int getAlertLevel() {
        return alertLevel;
    }
    
    public void setAlertLevel(int alertLevel) {
        this.alertLevel = alertLevel;
    }
    
    public String getAlertMessage() {
        return alertMessage;
    }
    
    public void setAlertMessage(String alertMessage) {
        this.alertMessage = alertMessage;
    }
    
    public int getIsResolved() {
        return isResolved;
    }
    
    public void setIsResolved(int isResolved) {
        this.isResolved = isResolved;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    public String getResolveTime() {
        return resolveTime;
    }
    
    public void setResolveTime(String resolveTime) {
        this.resolveTime = resolveTime;
    }
    
    public Device getDevice() {
        return device;
    }
    
    public void setDevice(Device device) {
        this.device = device;
    }
}
