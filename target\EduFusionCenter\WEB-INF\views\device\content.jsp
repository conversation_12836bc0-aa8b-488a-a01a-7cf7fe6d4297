<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid px-4 py-4">
    <!-- 页面标题和操作按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-4 pb-3 border-bottom">
        <div>
            <h1 class="h2 mb-0 fw-bold">
                <i class="bi bi-tools me-2 text-primary"></i>设备管理
            </h1>
            <p class="text-muted mt-2 mb-0">管理和监控所有设备的状态和信息</p>
        </div>
        <div class="d-flex">
            <div class="input-group me-3">
                <input type="text" class="form-control" placeholder="搜索设备..." id="deviceSearchInput">
                <button class="btn btn-outline-secondary" type="button">
                    <i class="bi bi-search"></i>
                </button>
            </div>
            <button class="btn btn-primary rounded-pill px-4" data-bs-toggle="modal" data-bs-target="#addDeviceModal">
                <i class="bi bi-plus-lg me-1"></i> 添加设备
            </button>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4 g-3">
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #4e73df, #224abe); border-radius: 15px;">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-1 text-white-50">总设备数</h6>
                            <h2 class="mt-3 mb-0 text-white fw-bold">${deviceStats.total}</h2>
                            <p class="text-white-50 mt-2 mb-0">
                                <i class="bi bi-arrow-up-right"></i> 较上月增长 5%
                            </p>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle p-3">
                            <i class="bi bi-tools text-white fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #1cc88a, #13855c); border-radius: 15px;">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-1 text-white-50">空闲设备</h6>
                            <h2 class="mt-3 mb-0 text-white fw-bold">${deviceStats.online}</h2>
                            <p class="text-white-50 mt-2 mb-0">
                                <i class="bi bi-arrow-up-right"></i> 可用率 ${deviceStats.online * 100 / deviceStats.total}%
                            </p>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle p-3">
                            <i class="bi bi-power text-white fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #f6c23e, #dda20a); border-radius: 15px;">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-1 text-white-50">使用中设备</h6>
                            <h2 class="mt-3 mb-0 text-white fw-bold">${deviceStats.maintenance}</h2>
                            <p class="text-white-50 mt-2 mb-0">
                                <i class="bi bi-arrow-down-right"></i> 使用率 ${deviceStats.maintenance * 100 / deviceStats.total}%
                            </p>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle p-3">
                            <i class="bi bi-wrench text-white fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body p-4">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="typeFilter" class="form-label">设备类型</label>
                    <select class="form-select" id="typeFilter">
                        <option value="">全部类型</option>
                        <option value="实验设备">实验设备</option>
                        <option value="办公设备">办公设备</option>
                        <option value="教学设备">教学设备</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="statusFilter" class="form-label">设备状态</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="空闲">空闲</option>
                        <option value="使用中">使用中</option>
                        <option value="维修中">维修中</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="locationFilter" class="form-label">位置</label>
                    <select class="form-select" id="locationFilter">
                        <option value="">全部位置</option>
                        <c:forEach items="${locations}" var="location">
                            <option value="${location}">${location}</option>
                        </c:forEach>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button class="btn btn-primary w-100" id="applyFilters">
                        <i class="bi bi-funnel-fill me-2"></i>应用筛选
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备列表 -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-0 pt-4 pb-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0 fw-bold"><i class="bi bi-list-ul text-success me-2"></i>设备列表</h5>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-download"></i> 导出
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-grid"></i> 网格视图
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th class="border-0 ps-4">设备名称</th>
                            <th class="border-0">类型</th>
                            <th class="border-0">位置</th>
                            <th class="border-0">状态</th>
                            <th class="border-0">最后维护</th>
                            <th class="border-0">下次维护</th>
                            <th class="border-0 text-end pe-4">操作</th>
                        </tr>
                    </thead>
                    <tbody id="deviceList">
                        <c:forEach items="${devices}" var="device">
                            <tr>
                                <td class="ps-4 fw-bold">${device.name}</td>
                                <td>
                                    <span class="badge bg-info text-white">${device.type}</span>
                                </td>
                                <td>${device.location}</td>
                                <td>
                                    <span class="badge ${device.status == '空闲' ? 'bg-success' : device.status == '维修中' ? 'bg-warning' : 'bg-danger'} rounded-pill px-3">
                                        ${device.status}
                                    </span>
                                </td>
                                <td>${device.lastMaintenanceDate}</td>
                                <td>${device.nextMaintenanceDate}</td>
                                <td class="text-end pe-4">
                                    <div class="btn-group">
                                        <a href="${pageContext.request.contextPath}/device/detail?id=${device.id}" class="btn btn-sm btn-outline-primary rounded-pill me-1">
                                            <i class="bi bi-info-circle"></i> 详情
                                        </a>
                                        <button class="btn btn-sm btn-outline-success rounded-pill me-1" onclick="editDevice(${device.id})">
                                            <i class="bi bi-pencil"></i> 编辑
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger rounded-pill" onclick="deleteDevice(${device.id})">
                                            <i class="bi bi-trash"></i> 删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-between align-items-center p-3 border-top">
                <span class="text-muted">显示 1-10 条，共 ${devices.size()} 条</span>
                <nav>
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item disabled"><a class="page-link" href="#">上一页</a></li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item"><a class="page-link" href="#">下一页</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 添加设备模态框 -->
<div class="modal fade" id="addDeviceModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content border-0 shadow">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="bi bi-plus-circle me-2"></i>添加新设备</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4">
                <form id="addDeviceForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="deviceName" name="name" placeholder="设备名称" required>
                                <label for="deviceName">设备名称</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="deviceType" name="type" required>
                                    <option value="实验设备">实验设备</option>
                                    <option value="办公设备">办公设备</option>
                                    <option value="教学设备">教学设备</option>
                                    <option value="其他">其他</option>
                                </select>
                                <label for="deviceType">设备类型</label>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="deviceLocation" name="location" placeholder="位置" required>
                                <label for="deviceLocation">位置</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="deviceStatus" name="status" required>
                                    <option value="空闲中">空闲</option>
                                    <option value="使用中">使用中</option>
                                    <option value="维修中">维修中</option>
                                </select>
                                <label for="deviceStatus">状态</label>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="deviceManufacturer" name="manufacturer" placeholder="制造商">
                                <label for="deviceManufacturer">制造商</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="deviceModel" name="model" placeholder="型号">
                                <label for="deviceModel">型号</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="deviceSerialNumber" name="serialNumber" placeholder="序列号">
                                <label for="deviceSerialNumber">序列号</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-floating mb-3">
                        <textarea class="form-control" id="deviceDescription" name="description" placeholder="描述" style="height: 100px"></textarea>
                        <label for="deviceDescription">描述</label>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted small">设备图片（可选）</label>
                        <div class="input-group">
                            <input type="file" class="form-control" id="deviceImage" name="deviceImage">
                            <label class="input-group-text" for="deviceImage">上传</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-light rounded-pill px-4" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-2"></i>取消
                </button>
                <button type="button" class="btn btn-primary rounded-pill px-4" onclick="saveDevice()">
                    <i class="bi bi-check-circle me-2"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化搜索功能
    const searchInput = document.getElementById('deviceSearchInput');
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            const searchText = this.value.toLowerCase();
            const rows = document.querySelectorAll('#deviceList tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            });
        });
    }

    // 初始化筛选功能
    const applyFiltersBtn = document.getElementById('applyFilters');
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', function() {
            const typeFilter = document.getElementById('typeFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const locationFilter = document.getElementById('locationFilter').value;

            const rows = document.querySelectorAll('#deviceList tr');

            rows.forEach(row => {
                const type = row.cells[1].textContent.trim();
                const status = row.cells[3].textContent.trim();
                const location = row.cells[2].textContent.trim();

                const typeMatch = typeFilter === '' || type.includes(typeFilter);
                const statusMatch = statusFilter === '' || status.includes(statusFilter);
                const locationMatch = locationFilter === '' || location.includes(locationFilter);

                row.style.display = (typeMatch && statusMatch && locationMatch) ? '' : 'none';
            });

            // 显示筛选结果提示
            showToast('筛选已应用', '根据您的条件筛选结果已更新');
        });
    }

    // 初始化表格排序功能
    document.querySelectorAll('th').forEach(th => {
        th.addEventListener('click', () => {
            const table = th.closest('table');
            const index = Array.from(th.parentNode.children).indexOf(th);
            const rows = Array.from(table.querySelectorAll('tbody tr'));
            const direction = th.classList.contains('sort-asc') ? -1 : 1;

            // 清除所有排序标记
            table.querySelectorAll('th').forEach(el => {
                el.classList.remove('sort-asc', 'sort-desc');
            });

            // 添加新的排序标记
            if (direction == 1) {
                th.classList.add('sort-asc');
            } else {
                th.classList.add('sort-desc');
            }

            // 排序行
            rows.sort((a, b) => {
                const aValue = a.children[index].textContent.trim();
                const bValue = b.children[index].textContent.trim();
                return aValue.localeCompare(bValue) * direction;
            });

            // 重新添加排序后的行
            rows.forEach(row => {
                table.querySelector('tbody').appendChild(row);
            });
        });
    });
});

// 保存设备
function saveDevice() {
    const form = document.getElementById('addDeviceForm');
    const formData = new FormData(form);

    // 显示加载提示
    showToast('处理中', '正在保存设备信息...', 'info');

    fetch('${pageContext.request.contextPath}/device/add', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addDeviceModal'));
            modal.hide();

            // 显示成功提示
            showToast('成功', '设备已成功添加', 'success');

            // 延迟刷新页面
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showToast('错误', '保存失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('错误', '发生未知错误，请稍后重试', 'error');
    });
}

// 编辑设备
function editDevice(id) {
    // 显示加载提示
    showToast('加载中', '正在获取设备信息...', 'info');

    // 获取设备信息并填充表单
    fetch('${pageContext.request.contextPath}/device/get?id=' + id)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // TODO: 实现编辑功能，填充表单并显示模态框
                showToast('准备就绪', '请编辑设备信息', 'success');
            } else {
                showToast('错误', '获取设备信息失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('错误', '发生未知错误，请稍后重试', 'error');
        });
}

// 删除设备
function deleteDevice(id) {
    // 使用确认对话框
    if (confirm('确定要删除这个设备吗？此操作无法撤销。')) {
        // 显示加载提示
        showToast('处理中', '正在删除设备...', 'info');

        fetch('${pageContext.request.contextPath}/device/delete?id=' + id, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('成功', '设备已成功删除', 'success');

                // 动画效果删除行
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) {
                    row.style.transition = 'opacity 0.5s';
                    row.style.opacity = '0';
                    setTimeout(() => {
                        row.remove();
                    }, 500);
                } else {
                    // 如果找不到行，延迟刷新页面
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                }
            } else {
                showToast('错误', '删除失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('错误', '发生未知错误，请稍后重试', 'error');
        });
    }
}

// Toast提示函数
function showToast(title, message, type = 'info') {
    // 检查是否已存在Toast容器
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // 创建Toast元素
    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');

    // 使用常规JavaScript条件判断设置类名
    let bgColorClass = 'primary';
    if (type == 'success') {
        bgColorClass = 'success';
    } else if (type == 'error') {
        bgColorClass = 'danger';
    }

    toast.className = 'toast align-items-center text-white bg-' + bgColorClass + ' border-0';
    toast.id = toastId;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <strong>${title}</strong>: ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    // 初始化并显示Toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });
    bsToast.show();

    // 自动移除Toast元素
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}
</script>