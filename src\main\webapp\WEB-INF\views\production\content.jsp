<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<!-- 页面标题 -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4">
    <h1 class="h2"><i class="bi bi-diagram-3 me-2 text-primary"></i>产线管理</h1>
    <div class="btn-toolbar">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshData()">
                <i class="bi bi-arrow-clockwise"></i> 刷新数据
            </button>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductionLineModal">
                <i class="bi bi-plus-lg me-1"></i>添加产线
            </button>
        </div>
    </div>
</div>

<!-- 状态统计卡片 -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card stats-card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #1cc88a, #13855c);">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1 text-white-50">运行中产线</h6>
                        <h2 class="mt-3 mb-0 text-white fw-bold">
                            <c:set var="runningCount" value="0" />
                            <c:forEach items="${productionLines}" var="line">
                                <c:if test="${line.status == '运行中'}">
                                    <c:set var="runningCount" value="${runningCount + 1}" />
                                </c:if>
                            </c:forEach>
                            ${runningCount}
                        </h2>
                        <p class="text-white-50 mt-2 mb-0"><i class="bi bi-play-circle"></i> 正常运行</p>
                    </div>
                    <div class="stats-icon-container bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="bi bi-play-circle-fill stats-icon text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card stats-card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #f6c23e, #dda20a);">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1 text-white-50">维护中产线</h6>
                        <h2 class="mt-3 mb-0 text-white fw-bold">
                            <c:set var="maintenanceCount" value="0" />
                            <c:forEach items="${productionLines}" var="line">
                                <c:if test="${line.status == '维护中'}">
                                    <c:set var="maintenanceCount" value="${maintenanceCount + 1}" />
                                </c:if>
                            </c:forEach>
                            ${maintenanceCount}
                        </h2>
                        <p class="text-white-50 mt-2 mb-0"><i class="bi bi-tools"></i> 维护保养</p>
                    </div>
                    <div class="stats-icon-container bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="bi bi-tools stats-icon text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card stats-card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #e74a3b, #c0392b);">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1 text-white-50">停止产线</h6>
                        <h2 class="mt-3 mb-0 text-white fw-bold">
                            <c:set var="stoppedCount" value="0" />
                            <c:forEach items="${productionLines}" var="line">
                                <c:if test="${line.status == '停止'}">
                                    <c:set var="stoppedCount" value="${stoppedCount + 1}" />
                                </c:if>
                            </c:forEach>
                            ${stoppedCount}
                        </h2>
                        <p class="text-white-50 mt-2 mb-0"><i class="bi bi-stop-circle"></i> 暂停运行</p>
                    </div>
                    <div class="stats-icon-container bg-white bg-opacity-25 rounded-circle p-3">
                        <i class="bi bi-stop-circle-fill stats-icon text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据表格卡片 -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-0 pt-4 pb-3">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0 fw-bold"><i class="bi bi-list me-2 text-primary"></i>产线列表</h5>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-filter"></i> 筛选
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary">
                    <i class="bi bi-download"></i> 导出
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0" id="productionLineTable">
                <thead class="bg-light">
                    <tr>
                        <th class="border-0 ps-4">ID</th>
                        <th class="border-0">产线名称</th>
                        <th class="border-0">状态</th>
                        <th class="border-0">最后更新时间</th>
                        <th class="border-0 text-end pe-4">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <c:forEach items="${productionLines}" var="line">
                        <tr>
                            <td class="ps-4">${line.id}</td>
                            <td>
                                <div class="fw-bold">${line.name}</div>
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${line.status == '运行中'}">
                                        <span class="badge bg-success">
                                            <i class="bi bi-play-circle-fill me-1"></i>
                                            ${line.status}
                                        </span>
                                    </c:when>
                                    <c:when test="${line.status == '维护中'}">
                                        <span class="badge bg-warning">
                                            <i class="bi bi-tools me-1"></i>
                                            ${line.status}
                                        </span>
                                    </c:when>
                                    <c:otherwise>
                                        <span class="badge bg-danger">
                                            <i class="bi bi-stop-circle-fill me-1"></i>
                                            ${line.status}
                                        </span>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td>${line.lastUpdated}</td>
                            <td class="text-end pe-4">
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="editProductionLine(${line.id})" title="编辑">
                                    <i class="bi bi-pencil-fill"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteProductionLine(${line.id})" title="删除">
                                    <i class="bi bi-trash-fill"></i>
                                </button>
                            </td>
                        </tr>
                    </c:forEach>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 添加产线模态框 -->
<div class="modal fade" id="addProductionLineModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-plus-circle me-2"></i>添加产线</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addProductionLineForm">
                    <div class="mb-3">
                        <label for="name" class="form-label">产线名称</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="status" class="form-label">状态</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="运行中">运行中</option>
                            <option value="维护中">维护中</option>
                            <option value="停止">停止</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveProductionLine()">
                    <i class="bi bi-save me-1"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑产线模态框 -->
<div class="modal fade" id="editProductionLineModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-pencil-square me-2"></i>编辑产线</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editProductionLineForm">
                    <input type="hidden" id="editId" name="id">
                    <div class="mb-3">
                        <label for="editName" class="form-label">产线名称</label>
                        <input type="text" class="form-control" id="editName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editStatus" class="form-label">状态</label>
                        <select class="form-select" id="editStatus" name="status" required>
                            <option value="运行中">运行中</option>
                            <option value="维护中">维护中</option>
                            <option value="停止">停止</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateProductionLine()">
                    <i class="bi bi-check-lg me-1"></i>更新
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 产线管理专用样式和脚本 -->
<link href="${pageContext.request.contextPath}/css/production.css" rel="stylesheet">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script src="${pageContext.request.contextPath}/js/production.js"></script> 