package com.building.model;

/**
 * 设备维护记录实体类
 * 对应数据库中的device_maintenance表
 */
public class DeviceMaintenance {
    private int id;
    private int deviceId;
    private String maintenanceType;
    private String maintenanceDesc;
    private String maintenanceResult;
    private String maintenancePerson;
    private String maintenanceTime;
    
    // 非持久化字段
    private Device device;
    
    /**
     * 默认构造函数
     */
    public DeviceMaintenance() {
    }
    
    /**
     * 带参数的构造函数
     * @param deviceId 设备ID
     * @param maintenanceType 维护类型
     * @param maintenanceDesc 维护描述
     * @param maintenanceResult 维护结果
     * @param maintenancePerson 维护人员
     */
    public DeviceMaintenance(int deviceId, String maintenanceType, String maintenanceDesc, 
                            String maintenanceResult, String maintenancePerson) {
        this.deviceId = deviceId;
        this.maintenanceType = maintenanceType;
        this.maintenanceDesc = maintenanceDesc;
        this.maintenanceResult = maintenanceResult;
        this.maintenancePerson = maintenancePerson;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public int getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(int deviceId) {
        this.deviceId = deviceId;
    }
    
    public String getMaintenanceType() {
        return maintenanceType;
    }
    
    public void setMaintenanceType(String maintenanceType) {
        this.maintenanceType = maintenanceType;
    }
    
    public String getMaintenanceDesc() {
        return maintenanceDesc;
    }
    
    public void setMaintenanceDesc(String maintenanceDesc) {
        this.maintenanceDesc = maintenanceDesc;
    }
    
    public String getMaintenanceResult() {
        return maintenanceResult;
    }
    
    public void setMaintenanceResult(String maintenanceResult) {
        this.maintenanceResult = maintenanceResult;
    }
    
    public String getMaintenancePerson() {
        return maintenancePerson;
    }
    
    public void setMaintenancePerson(String maintenancePerson) {
        this.maintenancePerson = maintenancePerson;
    }
    
    public String getMaintenanceTime() {
        return maintenanceTime;
    }
    
    public void setMaintenanceTime(String maintenanceTime) {
        this.maintenanceTime = maintenanceTime;
    }
    
    public Device getDevice() {
        return device;
    }
    
    public void setDevice(Device device) {
        this.device = device;
    }
}
