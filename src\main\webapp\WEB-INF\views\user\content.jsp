<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<div class="container-fluid px-4 py-4">
    <!-- 页面标题和操作按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-4 pb-3 border-bottom">
        <div>
            <h1 class="h2 mb-0 fw-bold">
                <i class="bi bi-people me-2 text-primary"></i>用户管理
            </h1>
            <p class="text-muted mt-2 mb-0">管理系统用户账号、权限和信息</p>
        </div>
        <div class="d-flex">
            <div class="input-group me-3">
                <input type="text" class="form-control" placeholder="搜索用户..." id="userSearchInput">
                <button class="btn btn-outline-secondary" type="button">
                    <i class="bi bi-search"></i>
                </button>
            </div>
            <button class="btn btn-primary rounded-pill px-4" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="bi bi-plus-lg me-1"></i> 添加用户
            </button>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4 g-3">
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #4e73df, #224abe); border-radius: 15px;">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-1 text-white-50">用户总数</h6>
                            <h2 class="mt-3 mb-0 text-white fw-bold">${totalUsers}</h2>
                            <p class="text-white-50 mt-2 mb-0">
                                <i class="bi bi-arrow-up-right"></i> 较上月增长 8%
                            </p>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle p-3">
                            <i class="bi bi-people text-white fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #1cc88a, #13855c); border-radius: 15px;">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-1 text-white-50">管理员数量</h6>
                            <h2 class="mt-3 mb-0 text-white fw-bold">${adminCount}</h2>
                            <p class="text-white-50 mt-2 mb-0">
                                <i class="bi bi-arrow-up-right"></i> 占比 ${adminCount * 100 / totalUsers}%
                            </p>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle p-3">
                            <i class="bi bi-person-check text-white fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(45deg, #f6c23e, #dda20a); border-radius: 15px;">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-1 text-white-50">普通用户数量</h6>
                            <h2 class="mt-3 mb-0 text-white fw-bold">${userCount}</h2>
                            <p class="text-white-50 mt-2 mb-0">
                                <i class="bi bi-arrow-up-right"></i> 占比 ${userCount * 100 / totalUsers}%
                            </p>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle p-3">
                            <i class="bi bi-person text-white fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body p-4">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="roleFilter" class="form-label">用户角色</label>
                    <select class="form-select" id="roleFilter">
                        <option value="">全部角色</option>
                        <option value="admin">管理员</option>
                        <option value="user">普通用户</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="statusFilter" class="form-label">账号状态</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="active">活跃</option>
                        <option value="inactive">非活跃</option>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button class="btn btn-primary w-100" id="applyFilters">
                        <i class="bi bi-funnel-fill me-2"></i>应用筛选
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户列表 -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-0 pt-4 pb-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0 fw-bold"><i class="bi bi-list-ul text-success me-2"></i>用户列表</h5>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-download"></i> 导出
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-arrow-repeat"></i> 刷新
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th class="border-0 ps-4">ID</th>
                            <th class="border-0">用户名</th>
                            <th class="border-0">真实姓名</th>
                            <th class="border-0">角色</th>
                            <th class="border-0">创建时间</th>
                            <th class="border-0">最后登录</th>
                            <th class="border-0 text-end pe-4">操作</th>
                        </tr>
                    </thead>
                    <tbody id="userList">
                        <c:forEach items="${users}" var="user" varStatus="status">
                            <tr data-id="${user.id}">
                                <td class="ps-4">${user.id}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-${user.role == 'admin' ? 'primary' : 'secondary'} me-2">
                                            ${fn:substring(user.username, 0, 1).toUpperCase()}
                                        </div>
                                        <span class="fw-medium">${user.username}</span>
                                    </div>
                                </td>
                                <td>${user.realName}</td>
                                <td>
                                    <span class="badge ${user.role == 'admin' ? 'bg-primary' : 'bg-secondary'} rounded-pill px-3">
                                        ${user.role == 'admin' ? '管理员' : '普通用户'}
                                    </span>
                                </td>
                                <td>${user.createTime}</td>
                                <td>${user.lastLoginTime}</td>
                                <td class="text-end pe-4">
                                    <div class="btn-group">
                                        <button class="btn btn-sm btn-outline-success rounded-pill me-1" onclick="editUser(${user.id})">
                                            <i class="bi bi-pencil"></i> 编辑
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger rounded-pill" onclick="deleteUser(${user.id})">
                                            <i class="bi bi-trash"></i> 删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-between align-items-center p-3 border-top">
                <span class="text-muted">显示 1-${users.size()} 条，共 ${totalUsers} 条</span>
                <nav>
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item disabled"><a class="page-link" href="#">上一页</a></li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item"><a class="page-link" href="#">下一页</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="bi bi-person-plus me-2"></i>添加新用户</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4">
                <form id="addUserForm" action="${pageContext.request.contextPath}/user/add" method="post">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required>
                                <label for="username">用户名</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                                <label for="password">密码</label>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="realName" name="realName" placeholder="真实姓名" required>
                                <label for="realName">真实姓名</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="role" name="role" required>
                                    <option value="admin">管理员</option>
                                    <option value="user" selected>普通用户</option>
                                </select>
                                <label for="role">角色</label>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="email" name="email" placeholder="电子邮箱">
                                <label for="email">电子邮箱</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="tel" class="form-control" id="phone" name="phone" placeholder="联系电话">
                                <label for="phone">联系电话</label>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted small">用户头像（可选）</label>
                        <div class="input-group">
                            <input type="file" class="form-control" id="avatar" name="avatar">
                            <label class="input-group-text" for="avatar">上传</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-light rounded-pill px-4" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-2"></i>取消
                </button>
                <button type="submit" form="addUserForm" class="btn btn-primary rounded-pill px-4">
                    <i class="bi bi-check-circle me-2"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化搜索功能
    const searchInput = document.getElementById('userSearchInput');
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            const searchText = this.value.toLowerCase();
            const rows = document.querySelectorAll('#userList tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            });
        });
    }

    // 初始化筛选功能
    const applyFiltersBtn = document.getElementById('applyFilters');
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', function() {
            const roleFilter = document.getElementById('roleFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            const rows = document.querySelectorAll('#userList tr');

            rows.forEach(row => {
                const role = row.querySelector('td:nth-child(4)').textContent.trim().toLowerCase();
                const lastLogin = row.querySelector('td:nth-child(6)').textContent.trim();

                // 判断用户是否活跃（最后登录时间不为空）
                const isActive = lastLogin && lastLogin !== '-';

                const roleMatch = roleFilter === '' ||
                                 (roleFilter === 'admin' && role.includes('管理员')) ||
                                 (roleFilter === 'user' && role.includes('普通用户'));

                const statusMatch = statusFilter === '' ||
                                   (statusFilter === 'active' && isActive) ||
                                   (statusFilter === 'inactive' && !isActive);

                row.style.display = (roleMatch && statusMatch) ? '' : 'none';
            });

            // 显示筛选结果提示
            showToast('筛选已应用', '根据您的条件筛选结果已更新');
        });
    }

    // 初始化表格排序功能
    document.querySelectorAll('th').forEach(th => {
        th.addEventListener('click', () => {
            const table = th.closest('table');
            const index = Array.from(th.parentNode.children).indexOf(th);
            const rows = Array.from(table.querySelectorAll('tbody tr'));
            const direction = th.classList.contains('sort-asc') ? -1 : 1;

            // 清除所有排序标记
            table.querySelectorAll('th').forEach(el => {
                el.classList.remove('sort-asc', 'sort-desc');
            });

            // 添加新的排序标记
            if (direction == 1) {
                th.classList.add('sort-asc');
            } else {
                th.classList.add('sort-desc');
            }

            // 排序行
            rows.sort((a, b) => {
                const aValue = a.children[index].textContent.trim();
                const bValue = b.children[index].textContent.trim();
                return aValue.localeCompare(bValue) * direction;
            });

            // 重新添加排序后的行
            rows.forEach(row => {
                table.querySelector('tbody').appendChild(row);
            });
        });
    });

    // 添加用户表单验证
    const addUserForm = document.getElementById('addUserForm');
    if (addUserForm) {
        addUserForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // 表单验证
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username.length < 3) {
                showToast('验证错误', '用户名长度不能少于3个字符', 'error');
                return;
            }

            if (password.length < 6) {
                showToast('验证错误', '密码长度不能少于6个字符', 'error');
                return;
            }

            // 显示加载提示
            showToast('处理中', '正在保存用户信息...', 'info');

            // 提交表单
            const formData = new FormData(this);

            fetch('${pageContext.request.contextPath}/user/add', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
                    modal.hide();

                    // 显示成功提示
                    showToast('成功', '用户已成功添加', 'success');

                    // 延迟刷新页面
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showToast('错误', '保存失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('错误', '发生未知错误，请稍后重试', 'error');
            });
        });
    }
});

// 编辑用户
function editUser(id) {
    // 显示加载提示
    showToast('加载中', '正在获取用户信息...', 'info');

    // 获取用户信息并填充表单
    fetch('${pageContext.request.contextPath}/user/api/get?id=' + id)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // TODO: 实现编辑功能，填充表单并显示模态框
                showToast('准备就绪', '请编辑用户信息', 'success');

                // 跳转到编辑页面
                window.location.href = "${pageContext.request.contextPath}/user/edit?id=" + id;
            } else {
                showToast('错误', '获取用户信息失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('错误', '发生未知错误，请稍后重试', 'error');
        });
}

// 删除用户
function deleteUser(id) {
    // 使用确认对话框
    if (confirm('确定要删除这个用户吗？此操作无法撤销。')) {
        // 显示加载提示
        showToast('处理中', '正在删除用户...', 'info');

        fetch('${pageContext.request.contextPath}/user/api/delete?id=' + id, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('成功', '用户已成功删除', 'success');

                // 动画效果删除行
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) {
                    row.style.transition = 'opacity 0.5s';
                    row.style.opacity = '0';
                    setTimeout(() => {
                        row.remove();
                    }, 500);
                } else {
                    // 如果找不到行，延迟刷新页面
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                }
            } else {
                showToast('错误', '删除失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('错误', '发生未知错误，请稍后重试', 'error');
        });
    }
}

// Toast提示函数
function showToast(title, message, type = 'info') {
    // 检查是否已存在Toast容器
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // 创建Toast元素
    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');

    // 使用常规JavaScript条件判断设置类名
    let bgColorClass = 'primary';
    if (type == 'success') {
        bgColorClass = 'success';
    } else if (type == 'error') {
        bgColorClass = 'danger';
    }

    toast.className = 'toast align-items-center text-white bg-' + bgColorClass + ' border-0';
    toast.id = toastId;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <strong>${title}</strong>: ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    // 初始化并显示Toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });
    bsToast.show();

    // 自动移除Toast元素
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}
</script>

<style>
/* 用户头像圆圈样式 */
.avatar-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
}

/* 表格排序样式 */
.table th {
    cursor: pointer;
    position: relative;
}
.table th:hover {
    background-color: rgba(0,0,0,0.03);
}
.table th.sort-asc::after {
    content: '▲';
    position: absolute;
    right: 8px;
    color: #007bff;
}
.table th.sort-desc::after {
    content: '▼';
    position: absolute;
    right: 8px;
    color: #007bff;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}
</style>